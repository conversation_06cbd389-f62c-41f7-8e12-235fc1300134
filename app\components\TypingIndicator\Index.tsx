import {BaseColors} from '@config/theme';
import React, {useRef, useEffect} from 'react';
import {View, Animated, StyleSheet} from 'react-native';

const TypingIndicator = ({type}: any) => {
  const dot1Opacity = useRef(new Animated.Value(0)).current;
  const dot2Opacity = useRef(new Animated.Value(0)).current;
  const dot3Opacity = useRef(new Animated.Value(0)).current;

  const animateDot = (dot: any, delay: any) => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(dot, {
          toValue: 1, // Dot becomes visible
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(dot, {
          toValue: 0, // Dot fades out
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(dot, {
          toValue: 0, // Dot fades out
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
      {delay},
    ).start();
  };

  useEffect(() => {
    animateDot(dot1Opacity, 0); // Start immediately
    animateDot(dot2Opacity, 300); // Delay 300ms
    animateDot(dot3Opacity, 600); // Delay 600ms
  }, []);

  const styl =
    type === 'chatList'
      ? {
          flexDirection: 'row',
          justifyContent: 'flex-start',
          paddingTop: 10,
        }
      : styles.container;
  return (
    <View style={styl}>
      <Animated.View style={[styles.dot, {opacity: dot1Opacity}]} />
      <Animated.View style={[styles.dot, {opacity: dot2Opacity}]} />
      <Animated.View style={[styles.dot, {opacity: dot3Opacity}]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginVertical: 5,
    backgroundColor: BaseColors.secondary,
    maxWidth: '17%',
    paddingVertical: 12,
    paddingLeft: 7,
    borderRadius: 20,
    borderBottomLeftRadius: 0,
    marginLeft: 10,
  },
  dot: {
    width: 5,
    height: 5,
    marginHorizontal: 5,
    borderRadius: 5,
    backgroundColor: BaseColors.primary, // Dot color
  },
});

export default TypingIndicator;
