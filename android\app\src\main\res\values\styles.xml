<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <!-- Customize your theme here. -->
        <item name="android:textColor">#000000</item>
        <item name="android:datePickerDialogTheme">@style/Dialog.Theme</item>
        <item name="android:timePickerDialogTheme">@style/Dialog.Theme</item>
        </style>
        <!-- Configuration for Dialog.theme. -->
        <style name="Dialog.Theme" parent="Theme.AppCompat.Light.Dialog">
        <!-- BackgroundColor of the date modal -->
        <item name="colorAccent">#1d559f</item>
        <!-- Change textColor of the date modal -->
        <item name="android:textColorPrimary">#000000</item>
    </style>

</resources>
