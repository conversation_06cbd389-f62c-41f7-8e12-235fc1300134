import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import FIcon from 'react-native-vector-icons/FontAwesome5';
import EEIcon from 'react-native-vector-icons/EvilIcons';
import {BaseColors} from '@config/theme';
import {translate} from '@language/Translate';

interface StreakData {
  day: string;
  date: Date;
  earned: boolean;
}

interface StreakTrackerProps {
  currentStreak: number;
  totalReadingPoint: number;
  streakData: StreakData[];
}

const StreakTracker: React.FC<StreakTrackerProps> = ({
  currentStreak,
  totalReadingPoint,
  streakData,
}) => {
  return (
    <View style={styles.card}>
      {/* Streak Information */}
      <View style={styles.streakContainer}>
        <View>
          <Text style={styles.label}>{translate('currentStreak', '')}</Text>
          <Text style={[styles.badge, styles.primaryColor]}>
            {currentStreak}
          </Text>
        </View>
        <View>
          <Text style={styles.label}>{translate('earnDailyReward', '')}</Text>
          <View style={styles.trophySty}>
            <Text style={[styles.badge, styles.primaryColor]}>
              {totalReadingPoint}
            </Text>
            <EEIcon
              name="trophy"
              size={18}
              color={BaseColors.primary}
              style={{marginTop: 5}}
            />
          </View>
        </View>
      </View>

      {/* Streak Days */}
      <View style={styles.streakDays}>
        {streakData.map(({day, earned}, index) => (
          <View key={index} style={styles.dayContainer}>
            <View style={[styles.dayCircle, earned && styles.activeDay]}>
              {earned && <FIcon name="fire" size={18} color="white" />}
            </View>
            <Text style={styles.dayText}>{day}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#e3edfb',
    borderRadius: 10,
    padding: 15,
    marginHorizontal: 35,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
  },
  streakContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  label: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  badge: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 5,
  },
  primaryColor: {
    color: '#1d559f',
  },
  streakDays: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  dayContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
  },
  dayCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#bfcce1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeDay: {
    backgroundColor: '#1d559f',
  },
  dayText: {
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 5,
  },
  trophySty: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default StreakTracker;
