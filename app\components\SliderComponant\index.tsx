import {BaseColors, FontFamily} from '@config/theme';
import {translate} from '@language/Translate';
import React, {useCallback, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import RangeSlider from 'rn-range-slider';

interface SliderProps {
  title: string; // Title of the slider (e.g., "Radius" or "Total Salary")
  min: number; // Minimum value for the slider
  max: number; // Maximum value for the slider
  step?: number; // Step value for the slider
  low?: number; // Default lower range value
  high?: number; // Default upper range value
  disableRange?: boolean; // Whether to disable the range selection
  floating?: boolean; // Whether to show floating point values
  onValueChanged?: (low: number, high: number) => void; // Callback for value changes
}

const SliderComponant: React.FC<SliderProps> = ({
  title,
  min,
  max,
  step = 1,
  low = min,
  high = max,
  disableRange = false,
  floating = false,
  onValueChanged,
}) => {
  // // Update state and call the callback when the slider changes
  // const handleValueChange = (lowValue: number, highValue: number) => {
  //   setLow(lowValue);
  //   setHigh(highValue);
  //   if (onValueChanged) {
  //     onValueChanged(lowValue, highValue);
  //   }
  // };

  // Render functions for thumb, track, and rail
  const renderThumb = useCallback(() => <View style={styles.thumb} />, []);
  const renderRail = useCallback(() => <View style={styles.rail} />, []);
  const renderRailSelected = useCallback(
    () => <View style={styles.railSelected} />,
    [],
  );
  const renderLabel = useCallback(
    (value: string) => (
      <View style={styles.label}>
        <Text style={styles.labelText}>{value}</Text>
      </View>
    ),
    [],
  );

  return (
    <View style={styles.container}>
      {/* Title */}
      <Text style={styles.title}>{title}</Text>

      {/* Slider */}
      <RangeSlider
        style={styles.slider}
        min={min}
        max={max}
        step={step}
        disableRange={disableRange}
        floating={floating}
        low={low} // Bind low value to state
        high={high} // Bind high value to state
        onValueChanged={onValueChanged} // Update state on change
        renderThumb={renderThumb}
        renderRail={renderRail}
        renderRailSelected={renderRailSelected}
        // renderLabel={renderLabel} //TODO: No need to sisplay label as value is already shown
      />

      {/* Range Values */}
      <View style={styles.rangeLabels}>
        <Text style={styles.valueLabel}>
          {low} {floating ? 'k' : 'miles'}
        </Text>
        {/* Show max value if disableRange is true */}
        <Text style={styles.valueLabel}>
          {disableRange ? (floating ? max.toFixed(1) : max) : high}+
          {floating ? 'k' : ` ${translate('miles', '')}`}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    // paddingHorizontal: 16,
  },
  title: {
    color: BaseColors.inputColor,
    paddingBottom: 10,
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
  },
  slider: {
    height: 10,
  },
  thumb: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#1d559f',
  },
  rail: {
    flex: 1,
    height: 4,
    backgroundColor: '#E5E5E5',
    borderRadius: 2,
  },
  railSelected: {
    height: 4,
    backgroundColor: '#ADD8E6',
    borderRadius: 2,
  },
  label: {
    padding: 4,
    backgroundColor: '#1d559f',
    borderRadius: 4,
  },
  labelText: {
    color: '#FFF',
    fontSize: 12,
  },
  rangeLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 18,
  },
  valueLabel: {
    fontSize: 14,
    color: '#7A7A7A',
  },
});

export default SliderComponant;
