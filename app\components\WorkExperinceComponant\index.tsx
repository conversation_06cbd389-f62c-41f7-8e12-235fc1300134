import React, {useState, useEffect, useRef} from 'react';
import {Text, View, TouchableOpacity, ScrollView} from 'react-native';
import Button from '../../components/UI/Button';
import {BaseColors} from '@config/theme';
import TextInput from '@components/UI/TextInput';
import {translate} from '@language/Translate';
import styles from './styles';
import Toast from 'react-native-simple-toast';
import moment from 'moment';
import BaseSetting from '@config/setting';
import {getApiData} from '@app/utils/apiHelper';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {Keyboard} from 'react-native';

interface WorkExpereinceCompoant {
  setErrorShow: any;
  errorShow: any;
  preExperince: any;
  setPreExperince: any;
  edit: any;
  editExperince: any;
  setUpdateExperinceList: any;
  updateExperinceList: any;
  setCheckSaveExperince: any;
  refRBSheet: any;
}

export default function WorkExperienceComponent({
  setErrorShow,
  errorShow,
  preExperince,
  setPreExperince,
  edit,
  editExperince,
  setUpdateExperinceList,
  updateExperinceList,
  setCheckSaveExperince,
  refRBSheet,
}: WorkExpereinceCompoant) {
  const [errors, setErrors] = useState<{[key: string]: any}>({});
  const [saveLoader, setSaveLoader] = useState(false);
  const [saveClick, setSaveClick] = useState(false);
  const [showForm, setShowForm] = useState(true);
  const [savedExperiences, setSavedExperiences] = useState<Array<any>>([]);

  // Track if component is mounted
  const isMounted = useRef(true);

  // Current form data
  const [currentExperience, setCurrentExperience] = useState({
    id: editExperince?.id ? editExperince?.id : Date.now(),
    company: editExperince?.company ? editExperince?.company : '',
    docTitle: editExperince?.docTitle ? editExperince?.docTitle : '',
    // designation: editExperince?.designation ? editExperince?.designation : '',
    isCurrCompany: editExperince?.isCurrCompany
      ? editExperince?.isCurrCompany
      : 0,
    startDate: editExperince?.startDate
      ? moment(editExperince?.startDate)
      : undefined,
    endDate:
      editExperince?.endDate === null
        ? undefined
        : editExperince?.endDate
        ? moment(editExperince?.endDate)
        : undefined,
    description: editExperince?.description ? editExperince?.description : '',
    docType: 'workExperience',
  });

  // Validate fields for the current experience
  const validateFields = (experience: any) => {
    let newErrors: any = {};

    if (!experience.docTitle.trim())
      newErrors.docTitle = translate('titleisRequired', '');
    // if (!experience.designation.trim())
    //   newErrors.designation = translate('designationisRequired', '');
    if (!experience.company.trim())
      newErrors.company = translate('companyNameisRequired', '');
    if (experience.isCurrCompany === '')
      newErrors.isCurrCompany = translate('selectYesorNo', '');
    if (!experience.startDate)
      newErrors.startDate = translate('startDateisRequired', '');

    // Check endDate only if the company is not the current one
    if (experience.isCurrCompany === 0 && !experience.endDate)
      newErrors.endDate = translate('endDateisRequired', '');

    if (!experience.description.trim())
      newErrors.description = translate('descriptionisRequird', '');

    return newErrors;
  };

  // Check if all experiences are filled
  const checkAllExperiencesValid = () => {
    const newErrors = validateFields(currentExperience);
    const hasErrors = Object.keys(newErrors).length > 0;
    setErrorShow(hasErrors);
    return hasErrors;
  };

  // Format experiences for API submission
  const formatExperiencesForAPI = experiences => {
    return experiences.map(({id, startDate, endDate, ...rest}) => ({
      ...rest,
      startDate: moment(startDate).format('MM/DD/YYYY hh:mm A'),
      ...(endDate !== undefined && {
        endDate: moment(endDate).format('MM/DD/YYYY hh:mm A'),
      }),
    }));
  };
  const formateditExperiencesForAPI = experiences => {
    return experiences.map(({startDate, endDate, ...rest}) => ({
      ...rest,
      startDate: moment(startDate).format('MM/DD/YYYY hh:mm A'),
      ...(endDate !== undefined && {
        endDate: moment(endDate).format('MM/DD/YYYY hh:mm A'),
      }),
    }));
  };

  const handleSubmit = async () => {
    console.log('Submitting data to API');
    setSaveLoader(true);

    // Get only the current experience data for API submission
    const experienceToSubmit = edit ? [currentExperience] : [currentExperience];
    const formattedExperience = formatExperiencesForAPI(experienceToSubmit);
    const formattedEditExperience =
      formateditExperiencesForAPI(experienceToSubmit);

    try {
      const res = await getApiData({
        endpoint: edit
          ? BaseSetting.endpoints.updateQualification
          : BaseSetting.endpoints.AddQualification,
        method: 'POST',
        data: edit ? formattedEditExperience[0] : {array: formattedExperience},
      });

      if (res?.status === true) {
        if (isMounted.current) {
          setPreExperince(res?.data?.workExperience);
          setSaveClick(false);
          Toast.show(translate('savedSucessfully', ''), Toast.BOTTOM);

          // Reset the form after successful API call
          if (!edit) {
            setShowForm(false);
            resetForm();

            // Clear the updateExperinceList to prevent re-adding old data
            // Only update with the current successful submission
            if (res?.data?.workExperience) {
              // If the API returns the updated list, use that instead
              setPreExperince(res?.data?.workExperience);
            }
          } else if (edit) {
            refRBSheet?.current?.close();
          }
        }
      } else {
        if (isMounted.current) {
          Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        }
      }

      if (isMounted.current) {
        setSaveLoader(false);
      }
    } catch (err) {
      console.log('check Error', err);
      if (isMounted.current) {
        setSaveLoader(false);
        Toast.show(translate('err', ''), Toast.BOTTOM);
      }
    }
  };

  // Reset form to initial state
  const resetForm = () => {
    setCurrentExperience({
      id: Date.now(),
      company: '',
      docTitle: '',
      designation: '',
      isCurrCompany: 0,
      startDate: undefined,
      endDate: undefined,
      description: '',
      docType: 'workExperience',
    });
    setErrors({});
  };

  // Handle save experience
  const handleSave = () => {
    setSaveLoader(true);
    const newErrors = validateFields(currentExperience);
    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      // Format the experience object for saving
      const experienceToSave = {...currentExperience};

      // If current company, set endDate to null
      if (experienceToSave.isCurrCompany === 1) {
        experienceToSave.endDate = null;
      }

      // Update local state with the new experience
      setSavedExperiences([experienceToSave]);
      setCheckSaveExperince(true);

      // Call the API to save the data
      handleSubmit();
    } else {
      setSaveLoader(false);
    }
  };

  // Add new experience (show form)
  const addExperience = () => {
    resetForm();
    setShowForm(true);
  };

  // Cancel adding experience
  const cancelExperience = () => {
    if (savedExperiences.length > 0) {
      // If we have saved experiences, just hide the form
      setShowForm(false);
      resetForm();
    } else {
      // If no saved experiences, keep showing the form but reset it
      resetForm();
    }
  };

  // Update experience field
  const updateExperience = (field: string, value: any) => {
    setCurrentExperience(prev => ({...prev, [field]: value}));
    setErrors(prevErrors => ({...prevErrors, [field]: undefined}));
  };

  // Initialize data if in edit mode
  useEffect(() => {
    if (edit && editExperince) {
      // If in edit mode, only set the current experience being edited
      setUpdateExperinceList([]);
    } else {
      // If in add mode, clear any previous data
      setUpdateExperinceList([]);
    }

    // Initially check if the form is valid when component mounts
    checkAllExperiencesValid();

    // Cleanup function when component unmounts
    return () => {
      isMounted.current = false;

      // Clear the experience list when component unmounts
      // This prevents old data from being re-added when component is mounted again
      setUpdateExperinceList([]);
    };
  }, []);

  // Check form validity whenever currentExperience or errorShow changes
  useEffect(() => {
    checkAllExperiencesValid();
  }, [currentExperience, errorShow]);
  useEffect(() => {
    if (currentExperience.isCurrCompany === 1) {
      // Assuming 1 means "Yes"
      setCurrentExperience(prev => ({
        ...prev,
        endDate: undefined,
      }));
    }
  }, [currentExperience.isCurrCompany]); // Only depend on this value

  const handleScroll = () => {
    Keyboard.dismiss();
  };

  return (
    <>
      <View style={[styles?.boxStyle]}>
        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="handled"
          style={{flexGrow: 1}}
          contentContainerStyle={{flexGrow: 1}}
          enableOnAndroid={true}
          extraScrollHeight={100}
          enableResetScrollToCoords={false}
          keyboardOpeningTime={0}
          onScrollBeginDrag={handleScroll}>
          {/* Main form view - only shown when showForm is true */}
          {showForm && (
            <View style={styles?.mainView}>
              <TextInput
                value={currentExperience.docTitle}
                onChange={value => updateExperience('docTitle', value)}
                title={translate('Title', '')}
                placeholderText={translate('typeHere', '')}
                maxLength={50}
                showError={!!errors?.docTitle}
                errorText={errors?.docTitle}
              />

              <TextInput
                value={currentExperience.company}
                onChange={value => updateExperience('company', value)}
                title={translate('Company', '')}
                placeholderText={translate('typeHere', '')}
                maxLength={50}
                showError={!!errors?.company}
                errorText={errors?.company}
              />

              {/* <TextInput
                value={currentExperience.designation}
                onChange={value => updateExperience('designation', value)}
                title={translate('Designation', '')}
                placeholderText={translate('typeHere', '')}
                maxLength={50}
                showError={!!errors?.designation}
                errorText={errors?.designation}
              /> */}

              <Text style={styles.label}>
                {translate('currentCompany', '')}
              </Text>
              <View style={styles.radioContainer}>
                <TouchableOpacity
                  style={styles.radioButtonContainer}
                  onPress={() => updateExperience('isCurrCompany', 1)}>
                  <View style={styles.radioOuterCircle}>
                    <View
                      style={[
                        styles.radioCircle,
                        currentExperience.isCurrCompany === 1 &&
                          styles.radioSelected,
                      ]}
                    />
                  </View>
                  <Text style={styles.radioText}>{translate('Yes', '')}</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.radioButtonContainer}
                  onPress={() => updateExperience('isCurrCompany', 0)}>
                  <View style={styles.radioOuterCircle}>
                    <View
                      style={[
                        styles.radioCircle,
                        currentExperience.isCurrCompany === 0 &&
                          styles.radioSelected,
                      ]}
                    />
                  </View>
                  <Text style={styles.radioText}>{translate('No', '')}</Text>
                </TouchableOpacity>
              </View>

              <View style={styles?.viewContainer}>
                <View style={styles?.btnView}>
                  <TextInput
                    selectedDate={currentExperience.startDate}
                    Date
                    onDateChange={date => updateExperience('startDate', date)}
                    title={translate('startDate', '')}
                    datetimemodal={translate('startDate', '')}
                    showError={!!errors?.startDate}
                    errorText={errors?.startDate}
                    maxDate={new Date()}
                  />
                </View>
                <View style={styles?.btnView}>
                  <TextInput
                    selectedDate={currentExperience.endDate}
                    Date
                    iseditable={
                      currentExperience.isCurrCompany === 0 &&
                      currentExperience.startDate
                    }
                    onDateChange={date => updateExperience('endDate', date)}
                    title={translate('endDate', '')}
                    datetimemodal={translate('endDate', '')}
                    showError={!!errors?.endDate}
                    errorText={errors?.endDate}
                    minDate={new Date(currentExperience?.startDate)}
                    maxDate={new Date()}
                  />
                </View>
              </View>

              <TextInput
                value={currentExperience.description}
                onChange={value => updateExperience('description', value)}
                title={translate('discribeJob', '')}
                textArea
                placeholderText="Type here"
                maxLength={300}
                style={{
                  borderColor: BaseColors.white,
                  backgroundColor: '#f5faff',
                  opacity: 1,
                }}
                showError={!!errors?.description}
                errorText={errors?.description}
              />
            </View>
          )}

          {/* Add Experience button - only shown when not showing the form */}
          {!showForm && (
            <TouchableOpacity
              onPress={addExperience}
              activeOpacity={0.8}
              style={styles?.addExpeinceViewSty}>
              <Text style={styles?.addExperinceSty}>
                + {translate('AddExperince', '')}
              </Text>
            </TouchableOpacity>
          )}
        </KeyboardAwareScrollView>
      </View>
      {showForm ? (
        <View style={styles?.btnViewSty}>
          <View style={{width: '100%'}}>
            <Button loading={saveLoader} onPress={handleSave} type="text">
              {translate('save', '')}
            </Button>
          </View>
          <View />
        </View>
      ) : (
        <View style={styles?.btnViewSty}>
          <View style={{width: '100%'}}>
            <Button
              onPress={() => {
                refRBSheet?.current?.close();
              }}
              type="text">
              Done
            </Button>
          </View>
          <View />
        </View>
      )}
    </>
  );
}
