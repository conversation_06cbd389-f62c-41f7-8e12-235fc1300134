import {Dimensions, Platform, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '../../config/typography';

const {width, height} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  uploadDocumentText: {
    fontSize: 16,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansBold,
    paddingBottom: 10,
  },
  fileContainer: {
    // borderWidth: 1,
    // borderColor: '#e5e5e5',
    // marginVertical: 10,
    paddingTop: 10,
    paddingHorizontal: 15,
    paddingBottom: 15,
    borderRadius: 10,
    marginBottom: 10,
    backgroundColor: BaseColors.inputBackground,
  },
  resumeText: {
    fontSize: 16,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansBold,
    marginBottom: 10,
  },
  uploadCvText: {
    fontSize: 14,
    color: BaseColors.inputColor,
    marginVertical: IOS ? 6 : 3,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: 10,
  },
  cvFileContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    marginTop: 15,
    marginBottom: 5,
    justifyContent: 'space-between',
    borderColor: BaseColors.inputBorderSty,
    paddingHorizontal: 15,
    padding: 10,
    alignItems: 'center',
  },
  fileInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileDetailsContainer: {
    flexDirection: 'column',
    paddingLeft: 20,

    width: '80%',
  },
  fileNameText: {
    fontSize: 14,
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  fileSizeText: {
    fontSize: 14,
    color: BaseColors.textGrey,
    paddingTop: 5,
    fontFamily: FontFamily.OpenSansRegular,
  },
  removeFileButton: {
    borderWidth: 1,
    borderColor: BaseColors.textGrey,
    borderRadius: 6,
    position: 'absolute',
    right: 5,
    top: 5,
  },
  chooseFileContainer: {
    borderWidth: 1,
    borderColor: BaseColors.primary,
    height: 96,
    borderRadius: 8,
    justifyContent: 'center',
    backgroundColor: BaseColors?.inputBackground,
  },
  chooseFileText: {
    fontSize: 16,
    color: BaseColors.primary,
    textAlign: 'center',
    fontFamily: FontFamily.OpenSansRegular,
  },
  fileFormatContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 10,
  },
  acceptedFileText: {
    paddingTop: IOS ? 8 : 2,
    fontWeight: '400',
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: Dimensions.get('screen').width * 0.028,
    color: BaseColors.lightTxtColor,
    width: '60%',
    // backgroundColor: 'pink',
  },
  maxSizeText: {
    paddingTop: IOS ? 8 : 2,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: Dimensions.get('screen').width * 0.031,
    color: BaseColors.lightTxtColor,
  },
  licenseText: {
    fontSize: 21,
    fontWeight: '400',
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  submitLicenseText: {
    fontSize: 14,
    color: BaseColors.inputColor,
    marginTop: IOS ? 6 : 3,
    fontFamily: FontFamily.OpenSansRegular,
    marginBottom: 13,
  },
  licenseFileContainer: {
    // flexDirection: 'row',
    borderWidth: 1,
    marginVertical: 5,
    justifyContent: 'space-between',
    borderColor: BaseColors.borderColor,
    marginHorizontal: 10,
    padding: 20,
    alignItems: 'center',
    borderRadius: 5,
  },
  nextButtonContainer: {
    padding: 15,
  },
  fileIconColor: {
    color: BaseColors.primary,
  },
  removeFileIconColor: {
    color: BaseColors.inputColor,
  },
  errorText: {
    color: BaseColors.red,
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    paddingTop: IOS ? 5 : 0,
  },
  iconViewSty: {
    alignSelf: 'center',
  },
  iconSty: {
    borderWidth: 1,
    padding: 5,
    borderColor: BaseColors.primary,
    borderRadius: 10,
    width: 120,
    height: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  desTxt: {
    fontSize: 14,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
    marginVertical: 10,
  },
  resumeTxtSty: {
    fontSize: 21,
    fontFamily: FontFamily?.OpenSansMedium,
  },
  seeMoreButton: {
    marginTop: 0,
    alignItems: 'center',
    borderTopWidth: 0.7,
    paddingTop: 5,
    borderColor: BaseColors?.borderColor,
  },
  seeMoreText: {
    color: BaseColors.primary,
    fontSize: 14,
    fontFamily: FontFamily?.OpenSansMedium,
  },
});
