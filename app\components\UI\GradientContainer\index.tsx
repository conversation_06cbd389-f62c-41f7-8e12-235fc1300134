import React from 'react';
import {View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import {isEmpty} from '@app/utils/lodashFactions';
import {useTheme} from '@react-navigation/native';
/**
 * Component for GradientContainer
 *@function GradientContainer
 */
export default function GradientContainer(props: {
  style?: object;
  containerStyle?: object;
  children?: any;
  disable?: boolean;
  colorArray?: any;
}) {
  const {containerStyle, children, disable, colorArray, ...rest} = props;
  const colors = useTheme();
  const BaseColors: any = colors.colors;
  return (
    <LinearGradient
      colors={
        !isEmpty(colorArray)
          ? colorArray
          : [BaseColors.secondary, BaseColors.primary]
      }
      {...rest}>
      <View style={containerStyle}>{children}</View>
    </LinearGradient>
  );
}

GradientContainer.propTypes = {
  containerStyle: PropTypes.object,
  disable: PropTypes.bool,
};

GradientContainer.defaultProps = {
  containerStyle: {},
  disable: false,
};
