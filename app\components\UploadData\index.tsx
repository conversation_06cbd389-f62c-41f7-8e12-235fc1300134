import React, {useEffect, useState} from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  Dimensions,
  ScrollView,
} from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import styles from './styles';
import DropdownList from '@components/DropDownList';
import {translate} from '@language/Translate';
import WorkExpereincCeompoant from '@components/WorkExperinceComponant';
import CvComponent from '@components/CvComponant';
import LicenseComponent from '@components/LicenceComponant';
import {useDispatch, useSelector} from 'react-redux';
import AuthAuthentication from '../../redux/reducers/auth/actions';
import Certifications from '@components/Certifications';
import Toast from 'react-native-simple-toast';
import BaseSetting from '@config/setting';
import {getApiData} from '@app/utils/apiHelper';
import moment from 'moment';
import Button from '@components/UI/Button';
import {BaseColors} from '@config/theme';
import EIcon from 'react-native-vector-icons/Entypo'; // Replace with your specific icon library if different
import authActions from '@redux/reducers/auth/actions';

interface DropdownItem {
  title: string;
}
interface UploadDataProps {
  refRBSheet: any;
  setOpenBottomSheet: any;
  handleCvFileSelect: any;
  deleteFile: any;
  cvFile: any;
  cvError: any;
  loader: any;
  setLoader?: any;
  Review: any;
  licenseFiles: any;
  licenseError: any;
  loaderLicence: any;
  handleLicenseFileSelect: any;
  // Added state fields
  company: any;
  setCompany: any;
  companyErr: any;
  setCompanyErr: any;
  designation: any;
  setDesignation: any;
  designationErr: any;
  setDesignationErr: any;
  working: any;
  setWorking: any;
  workingErr: any;
  setWorkingErr: any;
  isCurrentCompany: any;
  setIsCurrentCompany: any;
  jobProfile: any;
  setJobProfile: any;
  jobProfileErr: any;
  setJobProfileErr: any;
  workingSince: any;
  setWorkingSince: any;
  workingTill: any;
  setWorkingTill: any;
  workingSinceErr: any;
  setWorkingSinceErr: any;
  workingTillErr: any;
  setWorkingTillErr: any;
  workExperince: any;
  setWorkExperince: any;
  experienceList: any;
  setExperienceList: any;
  setCertificateFile: any;
  certificateFile: any;
  setcertificateFileError: any;
  certificateFileError: any;
  handleCertificateFileSelect: any;
  companyNameForCerti: any;
  setCompanyNameForCerti: any;
  setStartDate: any;
  startDate: any;
  setEndDate: any;
  endDate: any;
  certificationList: any;
  setCertificationList: any;
  certificateerrorShow: any;
  setCertificateErrorShow: any;
  setErrorShow: any;
  errorShow: any;
  setPreCertificate: any;
  preCertificate: any;
  preExperince: any;
  setPreExperince: any;
  setEdit: any;
  edit: any;
  editExperince: any;
  setEditExperince: any;
  setEditCertificate: any;
  editCertificate: any;
  setEditCertificateList: any;
  editCertificateList: any;
  setUploadLicenseFile: any;
  uploadLicenseFile: any;
  setAddData: any;
  addData: any;
  setCheckSaveCerti: any;
  setCheckSaveExperince: any;
  checkSaveCerti: any;
  checkSaveexperince: any;
  setCvFile: any;
  ActionSheetRefIOS: any;
  ActionSheetRef: any;
  handleOpenCamera: any;
  uploadLicense: any;
  setUploadLicene: any;
  handlePhotoSelect: any;
  handleImageFileSelect: any;
}

const workExperience: DropdownItem[] = [
  {title: 'CV/Resume'},
  {title: 'Enter Experience'},
  {title: 'Upload Liscense'},
  {title: 'Upload Certifications'},
];

const UploadData: React.FC<UploadDataProps> = ({
  refRBSheet,
  setOpenBottomSheet,
  handleCvFileSelect,
  deleteFile,
  cvFile,
  cvError,
  loader,
  setLoader = () =>{},
  Review,
  licenseFiles,
  licenseError,
  loaderLicence,
  handleLicenseFileSelect,
  // Added state fields
  company,
  setCompany,
  companyErr,
  setCompanyErr,
  designation,
  setDesignation,
  designationErr,
  setDesignationErr,
  working,
  setWorking,
  workingErr,
  setWorkingErr,
  isCurrentCompany,
  setIsCurrentCompany,
  jobProfile,
  setJobProfile,
  jobProfileErr,
  setJobProfileErr,
  workingSince,
  setWorkingSince,
  workingTill,
  setWorkingTill,
  workingSinceErr,
  setWorkingSinceErr,
  workingTillErr,
  setWorkingTillErr,
  workExperince,
  setWorkExperince,
  experienceList,
  setExperienceList,
  setCertificateFile,
  certificateFile,
  setcertificateFileError,
  certificateFileError,
  handleCertificateFileSelect,
  companyNameForCerti,
  setCompanyNameForCerti,
  setStartDate,
  startDate,
  setEndDate,
  endDate,
  certificationList,
  setCertificationList,
  errorShow,
  setErrorShow,
  certificateerrorShow,
  setCertificateErrorShow,
  preCertificate,
  setPreCertificate,
  preExperince,
  setPreExperince,
  setEdit,
  edit,
  setEditExperince,
  editExperince,
  setEditCertificate,
  editCertificate,
  setEditCertificateList,
  editCertificateList,
  setUploadLicenseFile,
  uploadLicenseFile,
  setLicenseFiles,
  setAddData,
  addData,
  setCheckSaveCerti,
  setCheckSaveExperince,
  checkSaveCerti,
  checkSaveexperince,
  setCvFile,
  ActionSheetRefIOS,
  ActionSheetRef,
  handleOpenCamera,
  uploadLicense,
  setUploadLicene,
  handlePhotoSelect,
  handleImageFileSelect,
}) => {
  const [expandedOption, setExpandedOption] = useState<string | null>(null);
  const [updateExperinceList, setUpdateExperinceList] = useState([]);
  const [updateCertificateList, setUpdateCertificateList] = useState([]);
  const {userProfileData, userData} = useSelector((state: any) => state.auth); // Use your RootState type
  const {setUserProfileData} = AuthAuthentication;
  const dispatch = useDispatch();
  const [loaderbtn, setLoaderBtn] = useState<any>(false);
  const [checkSave, setCheckSave] = useState(false);

  const transformedExperienceList = updateExperinceList.map(
    ({id, startDate, endDate, ...rest}) => ({
      ...rest,
      startDate: moment(startDate).format('MM/DD/YYYY hh:mm A'),
      ...(endDate !== null && {
        endDate: moment(endDate).format('MM/DD/YYYY hh:mm A'),
      }),
    }),
  );

  const formattedCertificate = preCertificate.map(item => ({
    ...item,
    startDate: moment(item.startDate).format('MM/DD/YYYY hh:mm A'),
    endDate: moment(item.endDate).format('MM/DD/YYYY hh:mm A'),
  }));
  const formattedExperince = updateExperinceList.map(
    ({startDate, endDate, ...rest}) => ({
      ...rest,
      startDate: moment(startDate).format('MM/DD/YYYY hh:mm A'),
      ...(endDate !== null && {
        endDate: moment(endDate).format('MM/DD/YYYY hh:mm A'),
      }),
    }),
  );

  const checkUploadFile = uploadLicenseFile
    ? uploadLicenseFile.map((file: any) => {
        return {...file, docType: 'licenses'}; // Create a new object with the docType added
      })
    : null;

  const handleSubmit = async () => {
    console.log('abssss');
    setLoaderBtn(true);

    const array =
      addData === 'Upload Certifications'
        ? preCertificate.map(({id, startDate, endDate, ...rest}) => ({
            ...rest,
            startDate: moment(startDate).format('MM/DD/YYYY hh:mm A'),
            endDate: moment(endDate).format('MM/DD/YYYY hh:mm A'),
          }))
        : addData === 'Upload Liscense'
        ? checkUploadFile
        : transformedExperienceList;

    try {
      const res = await getApiData({
        endpoint:
          edit || editCertificate
            ? BaseSetting.endpoints.updateQualification
            : BaseSetting.endpoints.AddQualification,
        method: 'POST',
        data: editCertificate
          ? formattedCertificate[0]
          : edit
          ? formattedExperince[0]
          : {array},
      });
      console.log('🚀 ~ handleSubmit ~ res:', res);
      if (res?.status === true) {
        if (editCertificate || addData === 'Upload Certifications') {
          setPreCertificate(res?.data?.certifications);
        } else if (addData === 'Upload Liscense') {
          setLicenseFiles(res?.data?.licenses);
          setUploadLicene(res?.data?.licenses);
        } else {
          setPreExperince(res?.data?.workExperience);
        }
        // if (addData === 'Certifications') {
        //   dispatch(
        //     setUserProfileData({
        //       ...userProfileData,
        //       certifications: certificationList.map(
        //         ({id, startDate, endDate, ...rest}) => ({
        //           ...rest,
        //           startDate: moment(startDate).format('MM/DD/YYYY hh:mm A'),
        //           endDate: moment(endDate).format('MM/DD/YYYY hh:mm A'),
        //         }),
        //       ),
        //     }) as any,
        //   );
        // } else {
        //   dispatch(
        //     setUserProfileData({
        //       ...userProfileData,
        //       workExperince: transformedExperienceList,
        //     }) as any,
        //   );
        // }
        refRBSheet.current?.close();
        setLoaderBtn(false);

        // dispatch(authActions.setUserProfileData(newObj) as any);
      } else {
        // dispatch(authActions.setUserProfileData(newObj) as any);
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      }
      setLoaderBtn(false);
    } catch (err) {
      console.log('check Error');
      setLoaderBtn(false);
      Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };
  const handleClose = () => {
    if (
      addData === 'CV/Resume' ||
      addData === 'Upload Liscense' ||
      (addData === 'Upload Certifications' && checkSaveCerti === false) ||
      (addData === 'Enter Experience' && checkSaveexperince === false)
    ) {
      refRBSheet?.current?.close();
    } else {
      null;
    }
  };

  return (
    <RBSheet
      ref={refRBSheet}
      height={700}
      useNativeDriver={false}
      onClose={() => {
        setOpenBottomSheet(false);
        setExpandedOption(null);
        setAddData(null); // Reset the selected data on close
      }}
      closeOnPressMask
      customStyles={{
        container: styles.rbSheetContainer,
      }}>
      <View style={styles.headerContainer}>
        <View style={styles.textStyle}>
          <Text style={styles.headerText}>{translate('Upload')}</Text>
        </View>
        <View style={styles?.iconSty}>
          <EIcon
            onPress={() => {
              refRBSheet?.current?.close();
            }}
            name="cross"
            size={25}
            color={BaseColors.textColor}
          />
        </View>
      </View>
      <View style={styles.container}>
        {edit || editCertificate ? null : (
          <View style={[styles.dropdownContainer, {marginBottom: 0}]}>
            <DropdownList
              title={translate('selectDocument', '')}
              data={workExperience}
              selectedValue={addData}
              onSelect={(value: string) => {
                setAddData(value);
              }}
              placeholder={translate('selectDocument', '')}
            />
          </View>
        )}
        <View style={{flex: 1}}>
          {/* <ScrollView style={styles.dropdownContainer}> */}
          {edit === true || addData === 'Enter Experience' ? (
            <View style={styles.dropdownContainer}>
              <WorkExpereincCeompoant // Passing all state fields
                company={company}
                setCompany={setCompany}
                companyErr={companyErr}
                setCompanyErr={setCompanyErr}
                designation={designation}
                setDesignation={setDesignation}
                designationErr={designationErr}
                setDesignationErr={setDesignationErr}
                working={working}
                setWorking={setWorking}
                workingErr={workingErr}
                setWorkingErr={setWorkingErr}
                isCurrentCompany={isCurrentCompany}
                setIsCurrentCompany={setIsCurrentCompany}
                jobProfile={jobProfile}
                setJobProfile={setJobProfile}
                jobProfileErr={jobProfileErr}
                setJobProfileErr={setJobProfileErr}
                workingSince={workingSince}
                setWorkingSince={setWorkingSince}
                workingTill={workingTill}
                setWorkingTill={setWorkingTill}
                workingSinceErr={workingSinceErr}
                setWorkingSinceErr={setWorkingSinceErr}
                workingTillErr={workingTillErr}
                setWorkingTillErr={setWorkingTillErr}
                setWorkExperince={setWorkExperince}
                workExperince={workExperince}
                setExperienceList={setExperienceList}
                experienceList={experienceList}
                setErrorShow={setErrorShow}
                errorShow={errorShow}
                preExperince={preExperince}
                setPreExperince={setPreExperince}
                edit={edit}
                setEdit={setEdit}
                setEditExperince={setEditExperince}
                editExperince={editExperince}
                setUpdateExperinceList={setUpdateExperinceList}
                updateExperinceList={updateExperinceList}
                setCheckSaveExperince={setCheckSaveExperince}
                handleSubmit={handleSubmit}
                refRBSheet={refRBSheet}
              />
            </View>
          ) : addData === 'CV/Resume' ? (
            <View style={styles.dropdownContainer}>
              <CvComponent
                handleCvFileSelect={handleCvFileSelect}
                deleteFile={deleteFile}
                cvFile={cvFile}
                cvError={cvError}
                loader={loader}
                setLoader={setLoader}
                Review={Review}
                setCvFile={setCvFile}
                ActionSheetRef={ActionSheetRef}
                ActionSheetRefIOS={ActionSheetRefIOS}
                handlePhotoSelect={handlePhotoSelect}
              />
            </View>
          ) : addData === 'Upload Liscense' ? (
            <LicenseComponent
              loader={loader}
              Review={Review}
              deleteFile={deleteFile}
              licenseFiles={licenseFiles}
              licenseError={licenseError}
              loaderLicence={loaderLicence}
              handleLicenseFileSelect={handleLicenseFileSelect}
              uploadLicenseFile={uploadLicenseFile}
              handleOpenCamera={handleOpenCamera}
              ActionSheetRef={ActionSheetRef}
              ActionSheetRefIOS={ActionSheetRefIOS}
              handleImageFileSelect={handleImageFileSelect}
            />
          ) : editCertificate || addData === 'Upload Certifications' ? (
            <Certifications
              setCertificateFile={setCertificateFile}
              certificateFile={certificateFile}
              setcertificateFileError={setcertificateFileError}
              certificateFileError={certificateFileError}
              handleCertificateFileSelect={handleCertificateFileSelect}
              setCompanyNameForCerti={setCompanyNameForCerti}
              companyNameForCerti={companyNameForCerti}
              setStartDate={setStartDate}
              startDate={startDate}
              setEndDate={setEndDate}
              endDate={endDate}
              certificationList={certificationList}
              setCertificationList={setCertificationList}
              setCertificateErrorShow={setCertificateErrorShow}
              certificateerrorShow={certificateerrorShow}
              preCertificate={preCertificate}
              setPreCertificate={setPreCertificate}
              setEditCertificate={setEditCertificate}
              editCertificate={editCertificate}
              setEditCertificateList={setEditCertificateList}
              editCertificateList={editCertificateList}
              setCheckSave={setCheckSave}
              setCheckSaveCerti={setCheckSaveCerti}
              setUpdateCertificateList={setUpdateCertificateList}
              updateCertificateList={updateCertificateList}
              refRBSheet={refRBSheet}
            />
          ) : null}
          {/* </ScrollView> */}
        </View>
        {addData === 'CV/Resume' || addData === 'Upload Liscense' ? (
          <View style={{width: '100%'}}>
            <Button
              loading={loaderbtn}
              type="text"
              onPress={
                addData === 'CV/Resume' ||
                (addData === 'Enter Experience' && errorShow === true) ||
                (addData === 'Upload Certifications' &&
                  certificateerrorShow === true) ||
                (addData === null &&
                  edit === false &&
                  editCertificate === false) ||
                (addData === 'Enter Experience' && !checkSaveexperince) ||
                (addData === 'Upload Certifications' && !checkSaveCerti) ||
                (addData === 'Upload Liscense' && uploadLicense?.length > 5) ||
                (addData === 'Upload Liscense' && checkUploadFile === null)
                  ? handleClose
                  : handleSubmit
              }>
              <Text style={styles.uploadButtonText}>Done</Text>
            </Button>
          </View>
        ) : null}
      </View>
    </RBSheet>
  );
};

export default UploadData;
