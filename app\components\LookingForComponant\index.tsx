import React from 'react';
import { Image, Text, Touchable, TouchableOpacity, View } from 'react-native';
import styles from './styles';
import { CustomIcon } from '@config/LoadIcons';
import { lookingData } from '@screens/Settings/staticData';
import { BaseColors } from '@config/theme';
import FastImage from 'react-native-fast-image';
import { Images } from '@config/images';
import { navigationRef } from '@navigation/NavigationService';

interface LookingForComponantProps { }

const LookingForComponant: React.FC<LookingForComponantProps> = ({ }) => {
  return (
    <>
      <View>
        <View style={styles.container}>
          <View style={[styles?.mainView, {
            borderWidth: 0.5,
            borderRadius: 10,
            borderColor: BaseColors.activeColor,
          }]}>
            {lookingData?.map((item: any, index: number) => {
              return (
                <TouchableOpacity style={styles?.mainBoxView} key={index} onPress={() =>
                  navigationRef.current.navigate('SearchScreen', { searchItem: item.title })
                }>
                  <View style={[styles?.boxView, {
                    borderRightWidth: index < lookingData.length - 1 ? 0.5 : 0,
                    borderColor: BaseColors.activeColor
                  }]}>
                    <View style={{
                      height: 30,
                      width: 30,
                      justifyContent: 'center',
                      alignContent: 'center',
                      alignItems: 'center',
                    }}>
                      <FastImage
                        source={
                          item.icon
                        }
                        style={{
                          height: 30,
                          width: 30,
                          justifyContent: 'center',
                          alignContent: 'center',
                          alignItems: 'center',
                          marginBottom: 8,
                        }}
                      // style={styles.profileImage}
                      />
                      {/* <Image
                        name={item?.icon}
                        size={18}
                        color={BaseColors.textColor}
                      /> */}
                    </View>
                    <View>
                      <Text style={styles?.titleSty}>{item?.title}</Text>
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      </View>
    </>
  );
};

export default LookingForComponant;
