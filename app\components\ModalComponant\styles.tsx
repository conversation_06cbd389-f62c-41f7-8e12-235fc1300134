import {Platform, StyleSheet} from 'react-native';
import {store} from '../../redux/store/configureStore';
import {FontFamily} from '@config/typography';
import {BaseColors} from '@config/theme';

const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  menuContainer: {
    position: 'absolute',
  },
  menuItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  menuItemText: {
    fontFamily: FontFamily.OpenSansMedium, // Adjust based on your FontFamily setup
    paddingBottom: IOS ? 0 : 5,
    paddingLeft: 0,
    fontSize: 16,
  },
  okButtonContainer: {
    marginTop: 10,
    marginHorizontal: 20,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  radioOuterCircle: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: BaseColors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
    marginVertical: 5,
  },
  radioInnerCircle: {
    height: 10,
    width: 10,
    borderRadius: 5,
    backgroundColor: BaseColors.primary,
  },
  submenuContainer: {
    marginLeft: 30,
    marginTop: 2,
    backgroundColor: BaseColors.lightGray,
    borderRadius: 5,
    paddingLeft: 10,
  },
  submenuOption: {
    paddingTop: 4,
    paddingBottom: 7,
    // paddingVertical: 7,
  },
  submenuText: {
    color: BaseColors.black,
    paddingLeft: 2,
  },
  submenuSelectedText: {
    fontWeight: 'bold',
    color: BaseColors.primary,
  },
  modalVieWSty: {
    backgroundColor: BaseColors.white,
    borderRadius: 8,
    padding: 10,
    maxHeight: 350, // Adjust the max height as needed
    borderWidth: 1,
    borderColor: BaseColors.borderColor,
  },
  btnSty: {
    width: '49%',
  },
  textStyle: {
    color: BaseColors.white,
    fontSize: 10,
    fontFamily: FontFamily.OpenSansBold,
  },
  nameViewSty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '90%',
  },
  textoulineStyle: {
    color: BaseColors.primary,
    fontSize: 10,
    fontFamily: FontFamily.OpenSansBold,
  },
});
