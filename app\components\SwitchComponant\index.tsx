import {BaseColors} from '@config/theme';
import React from 'react';
import {StyleProp, TextStyle, View} from 'react-native';
import {Switch} from 'react-native-switch';

interface SwitchComponentProps {
  onValueChange?: (value: boolean) => void;
  onChange?: () => void;
  value: boolean;
  thumbColor?: string;
  trackColor?: {
    false: string;
    true: string;
  };
  textSty?: StyleProp<TextStyle>;
  renderInsideCircle?: () => React.ReactNode;
  backgroundActive?: string;
  backgroundInactive?: string;
  circleActiveColor?: string;
  circleInActiveColor?: string;
  activeText?: string;
  inActiveText?: string;
  renderInActiveText?: boolean;
  renderActiveText?: boolean;
  barHeight?: number;
  circleSize?: number;
  disabled?: boolean;
  type?: string;
}

const SwitchComponent: React.FC<SwitchComponentProps> = props => {
  const {
    onValueChange = () => {},
    onChange = () => {},
    value,
    thumbColor,
    trackColor,
    renderInsideCircle,

    activeText,
    inActiveText,
    renderInActiveText = false,
    renderActiveText = false,
    barHeight,
    circleSize,
    disabled,
    type,
  } = props;

  return (
    <View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Switch
          value={value}
          onValueChange={onValueChange}
          onChange={onChange}
          disabled={disabled}
          circleSize={circleSize || 19}
          barHeight={barHeight || 17}
          thumbColor={thumbColor}
          trackColor={trackColor}
          backgroundActive={BaseColors.white}
          backgroundInactive={BaseColors.white}
          circleActiveColor={
            type === 'review' ? BaseColors.green : BaseColors.primary
          }
          circleInActiveColor={BaseColors.textGrey}
          circleBorderActiveColor={
            type === 'review' ? BaseColors.green : BaseColors.primary
          }
          circleBorderInactiveColor={BaseColors.white}
          renderInsideCircle={renderInsideCircle}
          changeValueImmediately={true}
          innerCircleStyle={{alignItems: 'center', justifyContent: 'center'}}
          outerCircleStyle={{borderColor: 'transparent'}}
          activeText={activeText}
          inActiveText={inActiveText}
          renderActiveText={renderActiveText}
          renderInActiveText={renderInActiveText}
          // switchLeftPx={-1.1}
          // switchRightPx={-1}
          // switchWidthMultiplier={2}
          switchBorderRadius={30}
          containerStyle={{borderWidth: 1, borderColor: '#dedfe1'}}
        />
      </View>
    </View>
  );
};

export default SwitchComponent;
