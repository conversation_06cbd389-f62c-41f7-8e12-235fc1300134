import React, { useEffect } from 'react';
import { Dimensions, Modal, StyleSheet, Text, View } from 'react-native';
import { translate } from '@language/Translate';
import { FontFamily } from '@config/typography';
import socketActions from '@redux/reducers/socket/actions';
import { Images } from '@config/images';
import { useDispatch, useSelector } from 'react-redux';
import { isEmpty } from '@app/utils/lodashFactions';
import FastImage from 'react-native-fast-image';

// const LottieView = require('lottie-react-native').default;
const { initialization } = socketActions;

const NetworkLost = ({ isVisible }: { isVisible: any }) => {
  const dispatch = useDispatch();
  const socketIo = useSelector((state: { socket?: { socketObj: any } }) => state?.socket?.socketObj);
  const { accessToken } = useSelector((state: { auth: { accessToken: string } }) => state.auth);
  //  Socket connection logic
  useEffect(() => {
    console.log('accessToken ===>', accessToken, !isEmpty(accessToken));
    if (!isEmpty(accessToken)) {
      dispatch(initialization() as any);
    }
  }, [socketIo, accessToken, isVisible]);

  return (
    <Modal transparent={true} animationType={'none'} visible={isVisible}>
      <View style={styles.modalBackground}>
        <View style={styles.activityIndicatorWrapper}>

          <View style={styles.imgView}>
            <FastImage
              source={Images.harborLogo}
              resizeMode={FastImage.resizeMode.contain} // Ensure proper scaling
              style={{ width: '70%', height: '100%' }} // Match the image's natural dimensions
            />
          </View>
          {/* <LottieView
            autoSize={true}
            source={Images.noInternet}
            autoPlay={true}
            // loop={true}
            style={{
              width: Dimensions.get('screen').width - 100,
              height: Dimensions.get('screen').width - 200,
            }}
          /> */}
          <Text style={styles.txt}>{translate('noInternet', '')}</Text>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  // Define your styles here
  modalBackground: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'space-around',
    backgroundColor: '#00000040',
    padding: 50,
  },
  activityIndicatorWrapper: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    // display: 'flex',
    alignItems: 'center',
    // justifyContent: 'space-around',
    padding: 20,
  },
  imgView: {
    alignItems: 'center',
    justifyContent: 'center',
    // flex: 1,
    width: Dimensions.get('screen').width / 2,
    height: 80,
    // height: Dimensions.get('screen').width / 5,
  },
  txt: {
    fontFamily: FontFamily.OpenSansBold,
    fontSize: 20,
  },
});

export default NetworkLost;
