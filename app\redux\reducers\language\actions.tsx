const actions = {
  SET_LANGUAGE: 'auth/SET_LANGUAGE',
  SELECT_LANGUAGE: 'auth/SELECT_LANGUAGE',
  UPDATE_LANGUAGE: 'auth/UPDATE_LANGUAGE',
  CLEAR_DATA: 'CLEAR_DATA',
  SET_LANGUAGE_JSON: 'auth/SET_LANGUAGE_JSON',

  setLanguage: (languageData: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_LANGUAGE,
      languageData,
    }),
  setSelectLanguage: (selectLanguage: any) => (dispatch: any) =>
    dispatch({
      type: actions.SELECT_LANGUAGE,
      selectLanguage,
    }),
  languageUpdate: (updateLanguage: any) => (dispatch: any) =>
    dispatch({
      type: actions.UPDATE_LANGUAGE,
      updateLanguage,
    }),

  setLanguageJson: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_LANGUAGE_JSON,
        languageJson: data,
      });
  },
  clearData: () => (dispatch: any) =>
    dispatch({
      type: actions.CLEAR_DATA,
    }),
};

export default actions;
