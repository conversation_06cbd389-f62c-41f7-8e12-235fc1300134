import io from 'socket.io-client';
import settings from '@config/setting';
import {
  isArray,
  isEmpty,
  isObject,
  isUndefined,
} from '@app/utils/lodashFactions';
import UserConfigActions from '@redux/reducers/userConfig/actions';
import { navigationRef } from '@navigation/NavigationService';
import { EventRegister } from 'react-native-event-listeners';
import { logOutCall } from '@app/utils/CommonFunction';

let SOCKET: any = null;

const actions = {
  INIT_SOCKET: 'socket/INIT_SOCKET',
  EMIT_SOCKET: 'socket/EMIT_SOCKET',
  SET_SOCKET: 'socket/SET_SOCKET',
  CLEAR_SOCKET: 'socket/CLEAR_SOCKET',
  SET_RECEIVED_CHAT_DATA: 'socket/SET_RECEIVED_CHAT_DATA',
  SET_UPDATED_CHAT_DATA: 'SET_UPDATED_CHAT_DATA',
  SET_CHAT_ROOM: 'SET_CHAT_ROOM',
  SET_SELECTED_CHAT_ROOM: 'SET_SELECTED_CHAT_ROOM',
  SET_LOADER: 'SET_LOADER',
  SET_CHATROOM_PAGINATION: 'SET_CHATROOM_PAGINATION',
  SET_MAIN_CHAT_LOADER: 'SET_MAIN_CHAT_LOADER',
  SET_IS_READ_TIC: 'SET_IS_READ_TIC',
  SET_BOTTOM_LOADER: 'SET_BOTTOM_LOADER',
  SET_SELECTED_CHAT_LIST: 'SET_SELECTED_CHAT_LIST', //ChatList

  CLEAR_CHAT_DATA: 'socket/CLEAR_CHAT_DATA',
  SET_TYPING: 'socket/SET_TYPING',
  SET_TOTAL_MSG_COUNT: 'socket/SET_TOTAL_MSG_COUNT',

  setSocket: (socketObj: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_SOCKET,
      socketObj,
    }),

  onDisConnect: (bool: any) => (dispatch: any) => {
    if (bool) {
      dispatch({
        type: actions.CLEAR_SOCKET,
        socketObj: null,
      });
    }
  },

  onReceive: (chatData: any) => (dispatch: any) => {
    if (isObject(chatData) && !isEmpty(chatData)) {
      const cdata = chatData;
      dispatch({
        type: actions.SET_RECEIVED_CHAT_DATA,
        chatData: cdata,
      });
    }
  },
  setUpdateChat: (updateChatData: any) => (dispatch: any) => {
    dispatch({
      type: actions.SET_UPDATED_CHAT_DATA,
      updateChatData: updateChatData,
    });
  },

  initialization: () => (dispatch: any, getState: any) => {
    // console.log('getState()----', getState().socket);
    const { socketObj } = getState().socket;
    if (
      socketObj === null ||
      isUndefined(socketObj?.emit) ||
      !socketObj?.connected
    ) {
      const connectionConfig = {
        transports: ['websocket'], /// you need to explicitly tell it to use websockets
        reconnection: true, // enable reconnection
        reconnectionAttempts: Infinity, // try to reconnect Infinity times
        reconnectionDelay: 1000, // start with 1s delay, then increase
        timeout: 20000, // connection timeout in ms
        autoConnect: true, // automatically connect
      };
      SOCKET = io(settings.socketURL, connectionConfig);
      console.log('🚀 ~ settings.socketURL:', settings.socketURL);

      console.log('socket initialesed');

      // Set up all event handlers outside the connect event to avoid race conditions
      SOCKET.on('connect', (d: any) => {
        console.log('Socket connected ===>', d);
        const { userData } = getState().auth;
        const options = {
          userId: userData?.id,
        };
        // if (socketObj === null) {
        SOCKET.emit('user_connection', options, (response: any) => {
          console.log('Acknowledgment from server:', response);

          try {
            console.log('Socket setSocket init ===>');
            dispatch(actions.setSocket(SOCKET));
            console.log('Socket setSocket ===>');
          } catch (e) {
            console.log('e ====>', e);
          }
        });

        // SOCKET.on('user_data', (data: any) => {
        //   console.log('data user_data ==>', data);
        // });
        // dispatch(actions.emit('user_data', {}));
        console.log('Socket user_connection ===>');
        // }
      });
      SOCKET.on('connection_success', (data: any) => {
        console.log('data connection_success ==>', data);
      });
      SOCKET.on('receive_message', (data: any) => {
        console.log('receive_message data ===>', data);
        dispatch(actions.onReceive(data));
      });

      // SOCKET.on('receiveTyping', (data: any) => {
      //   dispatch(actions.onReceiveTyping(data));
      // });

      SOCKET.on('update_count', (data: any) => {
        console.log('update_count data ===>', data);
        dispatch({
          type: actions.SET_TOTAL_MSG_COUNT,
          data: data,
        });
      });

      SOCKET.on('payment', (data: any) => {
        const currentRoute = navigationRef?.current?.getCurrentRoute();
        console.log('payment data ===>', data);
        if (
          currentRoute?.name === 'JobApplicant' ||
          currentRoute?.name === 'ChatDetails' ||
          currentRoute?.name === 'JobDetailScreen' ||
          currentRoute?.name === 'chat'
        ) {
          dispatch(UserConfigActions.setUpdateJobData(true));
        }
      });

      SOCKET.on('reward', (data: any) => {
        console.log('reward data ===>', data);
        // Handle specific action cases
        EventRegister.emit('reward_Update', data?.data);
      });

      SOCKET.on('streakNotification', (data: any) => {
        console.log('streakNotification data ===>', data);
        // Handle specific action cases
        EventRegister.emit('reward_Update', data?.data);
      });

      SOCKET.on('read_message_success', (data: any) => {
        dispatch(actions.setIsReadTic(data?.data));
      });

      SOCKET.on('is_typing_continue', (data: any) => {
        dispatch(actions.onReceiveTyping(data));
      });
      SOCKET.on('delete_user_emit', (data: any) => {
        logOutCall();
      });

      SOCKET.on('is_typing_stop', (data: any) => {
        console.log('data ===>', data);

        dispatch(actions.onReceiveTyping({}));
      });

      SOCKET.on('error', data => {
        console.log('SOCKET error =======>', data);
        // dispatch(actions.onReceiveTyping(data, 'stop'));
      });

      SOCKET.on('disconnect', () => {
        dispatch({
          type: actions.SET_SOCKET,
          socketObj: null,
        }),
        dispatch(actions.onDisConnect(true));
      });
    } else {
      console.log('Socket Already connected ==>', socketObj);
    }
  },

  emit:
    (event: any, data: any, callback = () => {}) =>
      (dispatch: any, getState: any) => {
        const { socketObj } = getState().socket;
        console.log(
          `Socket ${event} INSIDE initial ===>`,
          event,
          data,
          socketObj,
        );
        if (socketObj !== null && !isUndefined(socketObj?.emit)) {
          console.log(`Socket ${event} INSIDE ===>`, event, data);
          try {
            console.log('Socket user_connection trying ===>');
            socketObj?.emit(event, data, (callBackData: any) => {
              console.log(
                `Socket ${event}  INSIDE EMIT ===>`,
                event,
                data,
                callBackData,
              );
              callback(callBackData);
            });
            console.log(`Socket ${event} tried ===>`);
          } catch (e) {
            console.log(`Socket ${event} error ===>`, e);
          }
        } else {
          callback({
            success: false,
            message: 'Someting went wrong',
            type: socketObj !== null ? 'socket_connection' : '',
          });
        }
      },

  onReceiveTyping: (data: any) => (dispatch: any, getState: any) => {
    dispatch({
      type: actions.SET_TYPING,
      typing: '',
      typingData: isObject(data) ? data : {},
    });
  },

  disconnect: () => (dispatch: any, getState: any) => {
    const { socketObj } = getState().socket;
    if (socketObj !== null) {
      socketObj.disconnect();
      dispatch(actions.setSocket(null));
    }
  },

  setTotalMsgCount: (data: any) => (dispatch: any) => {
    dispatch({
      type: actions.SET_TOTAL_MSG_COUNT,
      data,
    });
  },

  getChatList:
    (
      type = '',
      tab,
      pages = 1,
      flt = '',
      loader = true,
      chatBottomLoader = false,
    ) =>
      (dispatch: any, getState: any) => {
      // const { view } = getState().app;
        const { chatRooms } = getState().socket;
        const { userData } = getState().auth;
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        // const loader = true;

        const params = {
          userId: userData?.id,
          selectedTab: tab,
          page: pages,
          timezone: timezone,
          perPage: 20,
        };
        console.log('params');
        if (type === 'init' && loader) {
          dispatch({
            type: actions.SET_LOADER,
            chatLoader: true,
          });
        }

        if (chatBottomLoader) {
          dispatch({
            type: actions.SET_BOTTOM_LOADER,
            bottomLoader: true,
          });
        } else {
        // dispatch({
        //   type: actions.SET_CHAT_ROOM,
        //   chatRooms: [],
        // });
        }

        console.log('conversation_list init ===>', pages > 1, chatBottomLoader);
        dispatch(
          actions.emit('conversation_list', params, (data: any) => {
            console.log('conversation_list data ===>', data);
            const chatLists =
            isObject(data) && isArray(data?.data) ? data?.data : [];
            dispatch({
              type: actions.SET_CHAT_ROOM,
              chatRooms:
              pages > 1 || chatBottomLoader
                ? [...chatRooms, ...chatLists]
                : chatLists,
            });
            dispatch({
              type: actions.SET_BOTTOM_LOADER,
              bottomLoader: false,
            });
            dispatch({
              type: actions.SET_LOADER,
              chatLoader: false,
            });
            dispatch({
              type: actions.SET_MAIN_CHAT_LOADER,
              data: false,
            });
            dispatch({
              type: actions.SET_CHATROOM_PAGINATION,
              chatListNextEnablePage: {
                page: data?.page,
                nextEnable: data?.nextEnable || false,
              },
            });
            dispatch({
              type: actions.SET_RECEIVED_CHAT_DATA,
              chatData: {},
            });
          }),
        );
      },

  clearChatData: () => (dispatch: any, getState: any) => {
    dispatch({
      type: actions.CLEAR_CHAT_DATA,
    });
  },

  clearRecievedChatData: () => (dispatch: any, getState: any) => {
    dispatch({
      type: actions.SET_RECEIVED_CHAT_DATA,
      chatData: {},
    });
  },
  setChatList: (chatRooms: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_CHAT_ROOM,
      chatRooms,
    }),
  setChatListLoader: (chatLoader: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_LOADER,
      chatLoader,
    }),
  setChatListBottomLoader: (bottomLoader: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_BOTTOM_LOADER,
      bottomLoader,
    }),
  setChatListPagination: (chatListNextEnablePage: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_CHATROOM_PAGINATION,
      chatListNextEnablePage,
    }),
  setSelectedRoom: (selectedRoom: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_SELECTED_CHAT_ROOM,
      selectedRoom,
    }),
  setIsReadTic: (isReadTic: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_IS_READ_TIC,
      isReadTic,
    }),
  setSelectedChatList: (selectedChatList: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_SELECTED_CHAT_LIST,
      selectedChatList,
    }),
};

export default actions;
