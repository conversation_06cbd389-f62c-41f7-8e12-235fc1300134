import React, { useCallback, useRef, useState } from 'react';
import { useTheme } from '@react-navigation/native';
import styles from './styles';
import AwesomeGallery, {
  GalleryRef,
  RenderItemInfo,
} from 'react-native-awesome-gallery';
import { Dimensions, Image, StatusBar, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AnimatedView from '@components/AnimatedView';

const renderItem = ({
  item,
  setImageDimensions,
}: RenderItemInfo<{ uri: string }>) => {
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    }}>
      <Image
        src={item}
        style={StyleSheet.absoluteFillObject}
        resizeMode='contain'
        onLoad={(e) => {
          const { width, height } = e?.nativeEvent?.source;
          setImageDimensions({ width, height });
        }}
        onError={(err) => {
          console.log('first error', err)
        }}
      />
    </View>
  );
};

export default function GalleryView({
  navigation,
  route,
}: {
  navigation: any;
  route: any;
}) {

  const { colors } = useTheme();
  const { setParams, goBack } = useNavigation();
  const isFocused = useIsFocused();
  const gallery = useRef<GalleryRef>(null);
  const { params } = route;

  const [infoVisible, setInfoVisible] = useState(true);

  const onIndexChange = useCallback(
    (index: number) => {
      isFocused && setParams({ index });
    },
    [isFocused, setParams]
  );

  const onTap = () => {
    StatusBar.setHidden(infoVisible, 'slide');
    setInfoVisible(!infoVisible);
  };


  return (
    <KeyboardAwareScrollView
      bounces={false}
      contentContainerStyle={styles.container}
      style={{
        backgroundColor: '#fff'
      }}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
      enableOnAndroid={false}>
      <AnimatedView>
        <AwesomeGallery
          ref={gallery}
          data={params.images.map((uri: string) => uri)}
          keyExtractor={(item: string) => item}
          renderItem={renderItem}
          initialIndex={params.index}
          numToRender={3}
          doubleTapInterval={150}
          onIndexChange={onIndexChange}
          onSwipeToClose={() => navigation.goBack()}
          onTap={onTap}
          loop
          onScaleEnd={(scale) => {
            if (scale < 0.8) {
              navigation.goBack();
            }
          }}
        />
        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}
          style={{
            position: 'absolute',
            right: 15,
            top: Dimensions.get('screen').height / 18,
          }}>
          <Icon
            name="close"
            size={30}
            color={'#1d559f'}
            style={{
              position: 'absolute',
              top: '50%',
              right: "20%",
            }}
          />
        </TouchableOpacity>
      </AnimatedView>
    </KeyboardAwareScrollView>
  );
};
