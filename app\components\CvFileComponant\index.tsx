import React from 'react';
import {View, Text, TouchableOpacity, ActivityIndicator} from 'react-native';
import EIcon from 'react-native-vector-icons/Entypo'; // Adjust the library if necessary
import styles from './styles'; // Ensure you have a styles file
import {CustomIcon} from '@config/LoadIcons';
import {translate} from '@language/Translate';
import {BaseColors} from '@config/theme';
import FastImage from 'react-native-fast-image';
import FIcon from 'react-native-vector-icons/FontAwesome'; // Replace with your specific icon library if different
import NoRecord from '@components/NoRecord';
import { imagePattern } from '@app/utils/CommonFunction';

const CvFileComponent = ({
  cvFile,
  deleteFile,
  loader,
  reviewType,
  navigation,
}: any) => {
  if (!cvFile)
    return (
      <>
        {reviewType === ('reviewbyEmployer' || 'review') ? (
          <View style={styles.fileContainer}>
            <View>
              <Text style={styles?.resumeText}>{translate('Resume', '')}</Text>
            </View>
            <NoRecord title={'noCvFileAvailable'} />
          </View>
        ) : null}
      </>
    );

  return (
    <View style={styles.fileContainer}>
      <View>
        <Text style={styles?.resumeText}>{translate('Resume', '')}</Text>
      </View>
      {/* <View style={styles.cvFileContainer}>
        <View style={styles.fileInfoContainer}>
          <View style={styles.iconSty}>
            <CustomIcon
              name="document"
              size={25}
              color={styles.fileIconColor.color}
            />
          </View>
          <View style={styles.fileDetailsContainer}>
            <Text numberOfLines={1} style={styles.fileNameText}>
              {typeof cvFile === 'string' && cvFile.includes('http')
                ? cvFile.split('/').pop()
                : cvFile?.fileName || '-'}
            </Text>
            {cvFile?.fileSize && (
              <Text style={styles.fileSizeText}>Size: {cvFile.fileSize}</Text>
            )}
          </View>
        </View>
        {reviewType === 'review' ? null : (
          <TouchableOpacity
            onPress={() => {
              deleteFile('cv');
            }}
            style={styles.removeFileButton}>
            {loader === 'cv' ? (
              <ActivityIndicator color={BaseColors.primary} />
            ) : (
              <EIcon name="cross" size={20} color={BaseColors.textGrey} />
            )}
          </TouchableOpacity>
        )}
      </View> */}
      <View
        style={[
          styles.licenseFileContainer,
          {
            width: '43%',
            padding: 10,
          },
        ]}>
        {reviewType === 'review' ? null : (
          <TouchableOpacity
            onPress={() => {
              deleteFile('cv');
            }}
            style={styles.removeFileButton}>
            {loader === 'cv' ? (
              <ActivityIndicator color={BaseColors.primary} />
            ) : (
              <EIcon name="cross" size={20} color={BaseColors.textGrey} />
            )}
          </TouchableOpacity>
        )}
        <View
          style={[
            styles.fileInfoContainer,
            {paddingTop: reviewType === 'review' ? 0 : 25},
          ]}>
          <View style={{alignItems: 'center', justifyContent: 'center'}}>
            <TouchableOpacity
              onPress={() => {
                if (imagePattern.test(cvFile?.fileName || cvFile)) {
                  navigation.navigate('GalleryView', {
                    images: [cvFile || cvFile?.filePath || cvFile?.fileName],
                    index: 0,
                  });
                } else if (
                  /\.(pdf|docx)$/i.test(
                    cvFile?.filePath || cvFile || cvFile?.fileName,
                  )
                ) {
                  navigation.navigate('WebViewScreen', {
                    uri: cvFile?.filePath || cvFile || cvFile?.fileName,
                    type: 'profile',
                  });
                }
                // Handle other file types if needed (e.g., show file icon)
                else {
                  console.log('File type not supported for preview');
                }
              }}
              style={[
                styles.iconSty,
                // {
                //   marginTop: reviewType === 'review' ? null : 20,
                //   marginBottom: 5,
                // },
              ]}>
              {cvFile || cvFile?.fileName ? (
                // Check for image files
                imagePattern.test(
                  cvFile?.filePath || cvFile || cvFile?.fileName,
                ) ? (
                  <FastImage
                    source={{
                      uri:
                        cvFile?.filePath ||
                        cvFile ||
                        cvFile?.fileName ||
                        cvFile,
                    }}
                    style={{width: '100%', height: '100%'}}
                    resizeMode="contain"
                  />
                ) : /\.(pdf|docx)$/i.test(
                    cvFile?.filePath || cvFile || cvFile?.fileName,
                  ) ? (
                  // Check for document files (pdf, docx, etc.)
                  <FIcon
                    name="file-pdf-o" // You can replace this with the appropriate icon if needed
                    size={30}
                    color={BaseColors.primary}
                  />
                ) : (
                  // Default for unsupported file types
                  <FIcon name="file" size={30} color={BaseColors.primary} />
                )
              ) : (
                <FIcon name="file" size={30} color={BaseColors.primary} />
              )}
            </TouchableOpacity>
            {cvFile?.isVerified ? (
              <View style={{marginTop: 10}}>
                <CustomIcon
                  name="Vector"
                  color={BaseColors.primary}
                  size={20}
                />
              </View>
            ) : null}
          </View>
          {/* <View style={styles.fileDetailsContainer}>
          <Text numberOfLines={1} style={styles.fileNameText}>
            {file
              ? typeof file === 'string' && file.includes('http')
                ? file.split('/').pop()
                : typeof file === 'string'
                ? file
                : file?.fileName || '-'
              : '-'}
          </Text>
          {file?.fileSize && (
            <Text style={styles.fileSizeText}>
              {translate('Size', '')}: {file?.fileSize || '-'}
            </Text>
          )}
        </View> */}
        </View>

        {/* {reviewType === 'review' ? null : (
              <TouchableOpacity
                style={styles.removeFileButton}
                onPress={() => deleteFile('licence', index)}>
                {loader === index ? (
                  <ActivityIndicator color={BaseColors.primary} />
                ) : (
                  <EIcon name="cross" size={20} color={BaseColors.textGrey} />
                )}
              </TouchableOpacity>
            )} */}
      </View>
    </View>
  );
};

export default CvFileComponent;
