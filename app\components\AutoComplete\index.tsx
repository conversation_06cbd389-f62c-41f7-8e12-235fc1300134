import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  View,
  TextInput,
  FlatList,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
// import Toast from 'react-native-simple-toast';
import {getApiData} from '@app/utils/apiHelper';
import {debounce, isArray, isEmpty, isObject} from '@app/utils/lodashFactions';
import {FontFamily} from '@config/typography';
import {BaseColors} from '@config/theme';
import {translate} from '@language/Translate';
import {CustomIcon} from '@config/LoadIcons';
import AIcon from 'react-native-vector-icons/AntDesign';

const MAX_TAGS = 3;

const AutoComplete = ({
  options,
  placeholder = 'Select here',
  containerStyle,
  value,
  onChange,
  error,
  errorText,
  mandatory,
  selectedTags,
  setSelectedTags,
  url,
  Review,
}: {
  options?: any[];
  placeholder?: string;
  containerStyle?: any;
  value?: string[];
  onChange?: (tags: string[]) => void;
  error?: boolean;
  errorText?: string;
  mandatory?: boolean;
  selectedTags?: any;
  setSelectedTags?: any;
  url?: string;
  Review: any;
}) => {
  const [query, setQuery] = useState('');
  console.log('🚀 ~ query:', query);
  const [loader, setLoader] = useState(false);
  const [allOptions, setAllOptions] = useState<any>(options);
  const [suggestionsList, setSuggestions] = useState([]);
  const debounceDelay = 500;

  // Function to filter options based on query
  const fetchOptions = async (text: string, type?: any) => {
    if (text.length <= 3) return;

    setLoader(true);
    try {
      const resp = await getApiData({
        endpoint: url,
        method: 'GET',
        data: {query: text},
      });
      if (resp?.data && resp?.status) {
        if (type === 'load') {
          setSuggestions(!isEmpty(resp.data) ? resp.data : []);
        } else {
          setAllOptions(!isEmpty(resp.data) ? resp.data : []); // Assuming API returns options array
        }
      } else {
        // Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
      setLoader(false);
    } catch (e) {
      setLoader(false);
      console.log('ERRR', e);
    }
  };

  // Memoize debounced fetchOptions to prevent recreation on each render
  const debouncedFetchOptions = useMemo(
    () => debounce(fetchOptions, debounceDelay),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );
  // Memoize filtered options based on the current query and allOptions
  const filteredOptionss = useCallback(
    (text: string) => {
      setQuery(text);
      if (text.length >= 2) {
        debouncedFetchOptions(text); // Directly use text instead of query
      }
      return Array.isArray(allOptions) && allOptions.length > 0
        ? allOptions.filter((option: any) =>
            String(option?.name || option)
              ?.toLowerCase()
              ?.includes(String(text)?.toLowerCase()),
          )
        : [];
    },
    [allOptions, debouncedFetchOptions],
  );

  // Add selected tag or a new custom tag
  const addTag = (tag: any) => {
    console.log('🚀 ~ addTag ~ tag:', tag);
    if (selectedTags?.length < MAX_TAGS && !selectedTags.includes(tag)) {
      const data = isObject(tag)
        ? [...selectedTags, tag]
        : [...selectedTags, ...tag];
      setSelectedTags(data);
    }
    setQuery('');
    setAllOptions([]);
  };

  // Remove selected tag
  const removeTag = (tag: any) => {
    setSelectedTags(selectedTags.filter((selected: any) => selected !== tag));
  };

  // Add custom tag if not in options
  const addCustomTag = () => {
    if (query?.trim() && !allOptions.includes(query)) {
      const newTags = allOptions;
      setAllOptions(newTags);
      addTag(newTags);
    }
  };

  useEffect(() => {
    fetchOptions('ship', 'load');
  }, []);

  return (
    <View style={[styles.container, containerStyle]}>
      <View
        style={[styles.inputContainer, error && styles.inputContainerError]}>
        <View style={styles.tagsInput}>
          {!isEmpty(selectedTags) &&
            selectedTags.map((tag: any, index: any) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag?.name}</Text>
                <TouchableOpacity
                  onPress={() =>
                    Review === 'reviewType' ? null : removeTag(tag)
                  }>
                  <Icon
                    name="close"
                    size={14}
                    color={BaseColors.primary}
                    style={styles.iconStyle}
                  />
                </TouchableOpacity>
              </View>
            ))}
          <TextInput
            style={styles.input}
            value={query}
            placeholder={selectedTags?.length < MAX_TAGS ? placeholder : ''}
            onChangeText={filteredOptionss}
            onSubmitEditing={addCustomTag}
            autoCapitalize="none"
            autoCorrect={false}
            editable={
              Review === 'reviewType' ? false : selectedTags?.length < MAX_TAGS
            }
            placeholderTextColor={BaseColors.dividerColor} // Adjusted placeholder text color for better visibility
          />
        </View>
        <TouchableOpacity>
          <CustomIcon
            name="Search"
            size={20}
            color={BaseColors.primary}
            style={styles.searchIcon}
          />
        </TouchableOpacity>
      </View>
      {error && errorText && <Text style={styles.errorText}>{errorText}</Text>}

      {query.length > 0 && selectedTags?.length < MAX_TAGS && (
        <View
          style={{
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 1,
            },
            shadowOpacity: 0.22,
            shadowRadius: 2.22,

            elevation: 3,
          }}>
          <FlatList
            style={styles.dropdown}
            data={allOptions}
            keyExtractor={item => item}
            renderItem={({item, index}: {item: any; index: Number}) => (
              <TouchableOpacity
                style={
                  selectedTags &&
                  selectedTags?.findIndex(
                    (o: {id: number}) => o?.id === item?.id,
                  ) !== -1
                    ? styles.dropdownHeader
                    : styles.dropdownItem
                }
                onPress={() => (item?.isNew ? addCustomTag() : addTag(item))}
                key={`${index}${+1}`}>
                <Text
                  style={
                    selectedTags &&
                    selectedTags?.findIndex(
                      (o: {id: number}) => o?.id === item?.id,
                    ) !== -1
                      ? {
                          ...styles.dropdownHeaderText,
                          alignSelf: loader ? 'center' : 'flex-start',
                        }
                      : {
                          ...styles.dropdownItemText,
                          alignSelf: loader ? 'center' : 'flex-start',
                        }
                  }>
                  {loader ? (
                    <ActivityIndicator color={BaseColors.primary} />
                  ) : item?.isNew ? (
                    `Add ${item?.name || item}`
                  ) : (
                    item?.name || item
                  )}
                </Text>
              </TouchableOpacity>
            )}
          />
        </View>
      )}
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          flexWrap: 'wrap',
          flex: 1,
        }}>
        <Text
          style={{
            color: BaseColors.textGrey,
            fontSize: 14,
            fontFamily: FontFamily.OpenSansBold,
          }}>
          {translate('Suggestions', '')} :
        </Text>
        {!isEmpty(suggestionsList) && isArray(suggestionsList)
          ? suggestionsList?.map((item: any, index: number) => (
              <TouchableOpacity
                key={index}
                style={styles.suggestionList}
                onPress={() => {
                  // Append the clicked suggestion to the existing query
                  const fI = !isEmpty(selectedTags)
                    ? selectedTags?.findIndex((o: any) => o === item)
                    : -1;
                  const updatedData =
                    fI === -1 ? [...selectedTags, item] : selectedTags;
                  Review === 'reviewType'
                    ? null
                    : selectedTags?.length === 3
                    ? null
                    : setSelectedTags(updatedData);
                }}>
                <Text style={styles.suggestionsText}>{item?.name}</Text>
                <AIcon name="plus" size={14} color={BaseColors.primary} />
              </TouchableOpacity>
            ))
          : null}
      </View>
    </View>
  );
};
const IOS = Platform.OS === 'ios';

const styles = StyleSheet.create({
  container: {},
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'transparent',
    borderRadius: 15,
    paddingHorizontal: 10,
    backgroundColor: BaseColors.inputBackground,
    paddingVertical: 5,
  },
  inputContainerError: {
    borderColor: 'red',
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 5,
    marginLeft: 5,
  },
  tagsInput: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    flex: 1,
    alignItems: 'center',
  },
  input: {
    // height: 30,
    // flex: 1,
    borderRadius: 10,
    minWidth: 50,
    paddingLeft: 6,
    paddingRight: 6,
    paddingTop: 4,
    paddingBottom: 4,
    margin: 3,
    backgroundColor: BaseColors.whiteColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  searchIcon: {
    marginRight: 5,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BaseColors.whiteColor,
    borderRadius: 10,
    paddingHorizontal: IOS ? 10 : 6,
    paddingVertical: IOS ? 6 : 4,
    borderWidth: 1,
    borderColor: BaseColors.primary,
    margin: 3,
    justifyContent: 'center',
    textAlign: 'center',
    alignSelf: 'center',
  },
  tagText: {
    color: BaseColors.primary,
    marginRight: 5,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 13,
    paddingBottom: IOS ? 0 : 5,
  },
  dropdown: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    backgroundColor: BaseColors.white,
    marginTop: 5,
  },

  dropdownHeader: {
    padding: 10,
    backgroundColor: BaseColors.whiteColor,
  },
  dropdownHeaderText: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansSemiBold,
    fontSize: 14,
  },
  dropdownItem: {
    padding: 10,
  },
  dropdownItemText: {
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansSemiBold,
    fontSize: 14,
  },
  suggestionList: {
    marginTop: IOS ? 7 : 1,
    marginBottom: IOS ? 0 : 5,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: BaseColors.primary,
    borderRadius: 10,
    marginHorizontal: 3,
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: IOS ? 5 : 0,
  },
  suggestionsText: {
    fontSize: 14,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    paddingHorizontal: 5,
    paddingBottom: IOS ? 0 : 5,
  },
  iconStyle: {paddingTop: 2},
});

export default AutoComplete;
