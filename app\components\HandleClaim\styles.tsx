import {Dimensions, Platform, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

export default StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 1,
    // margin: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  taskContainer: {
    // flexDirection: 'row',
    // alignItems: 'center',
    // justifyContent: 'space-between',
  },
  taskDetails: {
    flex: 1,
  },
  description: {
    fontSize: 16,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
  },
  level: {
    fontSize: 14,
    color: BaseColors.textColor,
    marginTop: 4,
    fontFamily: FontFamily.OpenSansRegular,
  },

  separator: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 12,
  },
  keyValueSty: {
    flexDirection: 'column',
  },
});
