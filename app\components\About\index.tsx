import React, {useRef, useState} from 'react';
import {
  Dimensions,
  Modal,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import {translate} from '@language/Translate';
import Button from '@components/UI/Button';
import SubTagComponant from '@components/SubTagComponat';
import AiComponant from '@components/AiComponant';
import AnimatedView from '@components/AnimatedView';
import {getApiData} from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import authActions from '@redux/reducers/auth/actions';
import {formatDate} from '@app/utils/CommonFunction';
import {isArray, isEmpty} from '@app/utils/lodashFactions';
import {useRedux} from '@components/UseRedux';
import {BaseColors} from '@config/theme';
import {AnySizeDragSortableView} from 'react-native-drag-sort';
import { FontFamily } from '@config/typography';
// Define the type for the data items
export interface SparkDataItem {
  id: number;
  name: string;
  key: string;
}

interface NewObj {
  skillIds?: Array<number | string>; // Adjust based on the actual type of `selectedTags`
  about?: string; // Optional because it uses `data?.about`
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  birthDate?: any;
  company?: string;
  gender?: string;
  location?: string;
  countryCode?: string;
  profilePhoto?: any;
  licenses?: string | Array<any>; // Adjust the type of `licenseFiles` accordingly
  coordinates?: any;
  flagCode?: string;
  cv?: any; // Optional because it depends on `cvFile`
  shortAddress?: any;
  startDate?: any;
  endDate?: any;
  description?: any;
  setAiDescription?: any;
  aiDescription?: any;
}

export default function About({
  onNext,
  setSelectedTags,
  selectedTags,
  setSelectedTagErr,
  selectedtagErr,
  loader,
  setAbout,
  about,
  Review,
  name,
  phoneNumber,
  selectedDate,
  companyName,
  selectedGender,
  location,
  countryCode,
  flagCode,
  profileImage,
  referralCode,
  lastname,
  email,
  cvFile,
  licenseFiles,
  setLicenseFiles,
  formatedAddress,
  startDate,
  endDate,
  description,
  setAiDescription,
  aiDescription,
}: any) {
  const refRBSheet = useRef<any>(null);
  const [skillsOptions, setSkillsOptions] = useState({loader: false, data: []});

  const {dispatch, useAppSelector} = useRedux();
  const [submitLoader, setSubmitLoader] = useState<boolean>(false);
  const {userData} = useAppSelector((state: any) => state.auth); // Use your RootState type
  const [showTagsModal, setShowTagsModal] = useState<boolean>(false);

  const [localTags, setLocalTags] = useState(selectedTags);

  const handleNextPress = () => {
    if (validateFields()) {
      handleSubmit();
    }
    // setOnClick(true);
  };
  const validateFields = () => {
    let isValid = true;
    if (isEmpty(selectedTags)) {
      setSelectedTagErr({err: true, txt: translate('selectOneSkill', '')});
      isValid = false;
    } else {
      setSelectedTagErr({err: false, txt: ''});
    }
    return isValid;
  };
  const formattedDate = selectedDate ? formatDate(selectedDate) : ''; // Format the date

  const handleSubmit = async () => {
    setSubmitLoader(true);
    const newObj: NewObj = {
      about: about || '',
      firstName: name || undefined,
      lastName: lastname || undefined,
      email: email || undefined,
      phoneNumber: phoneNumber || undefined,
      birthDate: formattedDate || undefined,
      company: companyName || undefined,
      gender: selectedGender ? selectedGender.toLowerCase() : undefined,
      location: location?.description || '',
      countryCode: `+${countryCode}` || undefined,
      skillIds: !isEmpty(selectedTags) ? selectedTags : undefined,
      flagCode: flagCode,
      shortAddress: formatedAddress,
      coordinates: {
        lat: location?.lat || '',
        long: location?.long || '',
      },
    };
    newObj.profilePhoto =
      profileImage && profileImage?.includes('http')
        ? profileImage?.split('/').pop()
        : profileImage || '';

    newObj.cv = (() => {
      if (typeof cvFile === 'string') {
        if (cvFile.includes('http')) {
          // Extract the file name from the URL
          return cvFile.split('/').pop();
        } else {
          // Return the string directly if it doesn't contain 'http'
          return cvFile;
        }
      } else if (cvFile && typeof cvFile === 'object') {
        // Return the fileName if cvFile is an object
        return cvFile.fileName;
      }
      return ''; // Fallback if cvFile is neither string nor valid object
    })();

    const updatedLicenseFiles = licenseFiles
      .map((file: any) => {
        if (typeof file === 'string') {
          // If the file is a URL, extract the file name
          return file.split('/').pop();
        } else if (file?.fileName) {
          // If the file is an object, use the fileName property
          return file.fileName;
        }
        return null;
      })
      .filter(Boolean); // Filter out any null values

    // Update the licenseFiles state with the extracted file names
    setLicenseFiles(updatedLicenseFiles);

    if (!isEmpty(updatedLicenseFiles) && isArray(updatedLicenseFiles)) {
      newObj.licenses = updatedLicenseFiles.map(file => {
        console.log('Current file:', file); // Log the current file for debugging

        if (typeof file === 'string') {
          // Case 1: When `licenseFiles` is an array of URLs (strings)
          if (file.includes('http')) {
            const fileName = file.split('/').pop();
            console.log('Extracted file name:', fileName);
            return fileName; // Extract file name from URL
          } else {
            console.log('Returning file as is:', file);
            return file; // Return as is
          }
        } else if (typeof file === 'string') {
          return file;
        } else if (typeof file === 'object' && file?.fileName) {
          // Case 2: When `licenseFiles` is an array of objects
          console.log('Returning fileName from object:', file.fileName);
          return file.fileName; // Extract fileName from the object
        } else {
          console.warn('Unhandled file format:', file);
          return null; // Return null if the format is unknown
        }
      });
    }

    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.updateUser,
        method: 'POST',
        data: newObj,
      });

      if (res?.status === true) {
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        setSubmitLoader(false);
        dispatch(authActions.setUserData(res?.data) as any);

        // dispatch(authActions.setUserProfileData(res?.data) as any);
        onNext();
      } else {
        console.log('error', res);
        setSubmitLoader(false);
      }
    } catch (err) {
      console.log('check Error');
      setSubmitLoader(false);

      // Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };

  const handleDone = (tags: string[]) => {
    setSelectedTags(tags); // Limit selectedTags to 3
    refRBSheet.current?.close();
  };

  const [movedKey, setMovedKey] = useState(null);

  const sortableViewRef = useRef();
  const [items, setItems] = useState(selectedTags);
  console.log('🚀 ~ items:', items);

  const onDeleteItem = (item, index) => {
    // Update the selectedTags directly
    const newTags = [...selectedTags];
    newTags.splice(index, 1);
    setSelectedTags(newTags);
  };

  const _renderItem = (item, index, isMoved) => (
    <TouchableOpacity
      onLongPress={() => {
        setMovedKey(item.id);
        sortableViewRef.current.startTouch(item, index);
      }}
      onPressOut={() => sortableViewRef.current.onPressOut()}>
      <View
        style={[
          styles.item_wrap,
          {opacity: movedKey === item.id && !isMoved ? 1 : 1},
        ]}>
        <View style={[styles.item]}>
          <View style={styles.item_text_swipe}>
            <Text style={styles.item_text}>{item.name}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: '#fff',
        justifyContent: 'space-between',
      }}>
      <ScrollView
        bounces={false}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"

        // enableOnAndroid={false

        // }
      >
        <AnimatedView>
          <View style={styles?.skillViewSty}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Text style={styles?.selfTxtSty}>{translate('addTags', '')}</Text>
              <Button
                onPress={() => {
                  setItems(selectedTags);
                  setShowTagsModal(true);
                }}
                buttonTextStyle={{fontFamily:FontFamily.OpenSansRegular}}
                containerStyle={{paddingVertical: 5, paddingHorizontal: 5}}
                type="outlined">
                {translate('reOrder', '')}
              </Button>
            </View>
            <View style={styles.mVertical}>
              <Text style={styles.addskillTxtSty}>
                {translate('addYourSkill', '')}
              </Text>
            </View>
            <View>
              <SubTagComponant
                refRBSheet={refRBSheet}
                setOpenBottomSheet={() => {}}
                title={translate('selectTag', '')}
                Description=""
                cancelProp={() => {}}
                onClick={handleDone} // Pass the callback
                doneProp="Done"
                showDescription={false}
                deleteSty={false}
                showCancelbutton={false}
                options={[]} // Pass your options here
                setSelectedLanguage={() => {}}
                selectedLanguage={''}
                selectedTags={selectedTags} // Pass the actual selectedTags
                setSelectedTags={setSelectedTags}
                Review={Review}
                error={selectedtagErr.err}
                errorText={selectedtagErr.txt}
              />
              {/* <AutoComplete
              options={[]}
              placeholder={translate('selecthere', '')}
              // value={}
              // onChange={}
              error={selectedtagErr.err}
              errorText={selectedtagErr.txt}
              mandatory={true}
              setSelectedTags={setSelectedTags}
              selectedTags={selectedTags}
              url={BaseSetting.endpoints.getSkills}
              Review={Review}
            /> */}
            </View>

            <AiComponant
              about={about}
              setAbout={setAbout}
              Review={Review}
              type="user"
              selectedTags={selectedTags}
              jobTitle=""
              skillsOptions={skillsOptions}
              setSkillsOptions={setSkillsOptions}
              location={location}
              startDate={startDate}
              endDate={endDate}
              setAiDescription={setAiDescription}
              aiDescription={aiDescription}
            />
          </View>
        </AnimatedView>
      </ScrollView>
      {showTagsModal ? (
        <Modal
          visible={showTagsModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowTagsModal(false)}
          style={{maxWidth: '80%', maxHeight: '50%'}}>
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            activeOpacity={1}
            onPress={() => setShowTagsModal(true)}>
            <View
              style={{
                backgroundColor: 'white',
                padding: 20,
                borderRadius: 10,
                maxWidth: '80%',
                maxHeight: '50%',
              }}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  marginBottom: 15,
                  color: BaseColors.black,
                }}>
                {translate('tags')}
              </Text>

              <AnySizeDragSortableView
                ref={sortableViewRef}
                // Key change: Use selectedTags directly
                dataSource={selectedTags}
                keyExtractor={item => item.id.toString()}
                renderItem={_renderItem}
                onDataChange={(data, callback) => {
                  // Update selectedTags directly
                  setSelectedTags(data);
                  callback();
                }}
                onDragEndBefore={(fromIndex: any, toIndex: any) => {
                  console.log(`Dragged from ${fromIndex} to ${toIndex}`);
                  const updatedTags = [...localTags];
                  const [movedTag] = updatedTags.splice(fromIndex, 1);
                  updatedTags.splice(toIndex, 0, movedTag);

                  console.log('Final Drag Updated Tags:', updatedTags);
                  setLocalTags(updatedTags);
                }}
              />

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <View style={{width: '45%'}}>
                  <Button
                    type="outlined"
                    style={{
                      marginTop: 20,
                    }}
                    onPress={() => {
                      setShowTagsModal(false);
                      setSelectedTags(items);
                    }}>
                    Close
                  </Button>
                </View>
                <View style={{width: '45%'}}>
                  <Button
                    type="text"
                    style={{
                      marginTop: 20,
                    }}
                    onPress={() => {
                      setShowTagsModal(false);
                    }}>
                    Continue
                  </Button>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </Modal>
      ) : null}

      {Review === 'reviewType' ? null : (
        <View style={styles?.nextBtnSty}>
          <Button loading={submitLoader} type="text" onPress={handleNextPress}>
            {translate('next', '')}
          </Button>
        </View>
      )}
    </View>
  );
}
