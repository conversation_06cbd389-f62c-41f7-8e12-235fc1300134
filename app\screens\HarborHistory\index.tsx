import React from 'react';
import {translate} from '@language/Translate';
import Header from '@components/Header';
import MyHarbor from '@screens/MyHarbor';
import {View} from 'react-native';
import {BaseColors} from '@config/theme';

export default function HarborHistory(props: any) {
  const {navigation, route}: {navigation: any; route: any} = props;
  const params = route?.params;
  return (
    <View style={{flex: 1, backgroundColor: BaseColors.white}}>
      <Header
        leftIcon="back-arrow"
        title={translate('harborHistory', '')}
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <View style={{flex: 1, marginHorizontal: 20}}>
        <MyHarbor navigation={navigation} type={'history'} id={params?.id} />
      </View>
    </View>
  );
}
