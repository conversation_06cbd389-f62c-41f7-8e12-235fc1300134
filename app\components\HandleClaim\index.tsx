import React, {useState} from 'react';
import {
  Dimensions,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Button from '@components/UI/Button';
import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {CustomIcon} from '@config/LoadIcons';
import AnimatedView from '@components/AnimatedView';
import moment from 'moment';

const HandleClaimComponant = ({rewardList}: any) => {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const toggleSection = (id: string) => {
    setExpandedSection((prev: any) => (prev === id ? null : id));
  };

  const renderTask = ({item}: {item: any}) => {
    return (
      <View style={styles.taskContainer}>
        <View style={styles?.keyValueSty}>
          <Text numberOfLines={2} style={styles.description}>
            {item?.keyName}
          </Text>
          {item?.isDone === true ? (
            <Text style={styles.dateSty}>
              {moment(item?.isDoneCreatedAt).format('M/D/YYYY')}
            </Text>
          ) : null}
        </View>

        {item?.isDone === true ? (
          <CustomIcon name="verified" size={20} color={BaseColors.primary} />
        ) : (
          // <Button
          //   buttonTextStyle={styles.buttonText}
          //   type="outline"
          //   style={styles.buttonStyles}>
          //   {item?.points} pts
          // </Button>
          <TouchableOpacity style={styles?.btnView}>
            <Text style={styles?.categoryPointSty}>{item?.points} pts</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <AnimatedView>
        {/* <FlatList
          data={rewardList}
          keyExtractor={item => item.id}
          renderItem={renderTask}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        /> */}

        {rewardList &&
          rewardList?.map((section: any) => (
            <View key={section.id} style={styles.section}>
              <TouchableOpacity
                onPress={() => toggleSection(section.id)}
                style={styles.sectionHeader}>
                <Text numberOfLines={2} style={styles.sectionTitle}>
                  {section?.name || '-'}
                </Text>
                <CustomIcon
                  name="ArrowUp" // Change to any icon name
                  size={24}
                  color={BaseColors.primary}
                  style={{
                    paddingRight: 5,
                    transform: [
                      {
                        rotate:
                          expandedSection === section.id ? '360deg' : '180deg',
                      },
                    ], // Rotate the icon
                  }}
                />
              </TouchableOpacity>
              {expandedSection === section.id &&
                section?.category?.length > 0 && (
                  <FlatList
                    data={section?.category || []}
                    keyExtractor={item => item.id}
                    renderItem={renderTask}
                    ItemSeparatorComponent={() => (
                      <View style={styles.separator} />
                    )}
                  />
                )}
            </View>
          ))}
      </AnimatedView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    // padding: 16,
  },
  buttonStyles: {borderWidth: 1},
  section: {
    marginBottom: 16,
    borderRadius: 8,
    // borderWidth: 1,
    // borderColor: '#ddd',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: BaseColors.white,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.primary,
    // width: Dimensions.get('screen').width / 1.6,
    flex: 1,
  },
  icon: {
    fontSize: 16,
    color: '#333',
  },
  taskContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  description: {
    fontSize: 14,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansRegular,
    flex: 1,
  },
  dateSty: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
    flex: 1,
  },
  button: {
    backgroundColor: '#1d559f',
    paddingVertical: 8,
    // paddingHorizontal: 16,
    borderRadius: 8,
  },
  buttonText: {
    fontSize: 10,
    color: BaseColors.primary,
    backgroundColor: BaseColors.white,
  },
  separator: {
    height: 1,
    backgroundColor: '#ddd',
    marginHorizontal: 5,
  },
  btnView: {
    borderWidth: 0.7,
    borderColor: BaseColors?.primary,
    paddingVertical: 5,
    width: Dimensions.get('screen').width / 5.2,
    alignItems: 'center',
    borderRadius: 5,
  },
  categoryPointSty: {
    fontSize: 13,
    fontFamily: FontFamily?.OpenSansSemiBold,
    color: BaseColors.primary,
  },
  keyValueSty: {
    width: Dimensions.get('screen').width / 1.7,
  },
});

export default HandleClaimComponant;
