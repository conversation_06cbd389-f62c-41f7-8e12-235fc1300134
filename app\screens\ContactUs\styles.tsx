import {Dimensions, Platform, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: BaseColors?.white,

    // paddingBottom: Dimensions.get('screen').height / 9.8,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  mainView: {
    padding: 15,
    borderWidth: 1,
    borderColor: BaseColors.white20,
    marginHorizontal: 20,
  },
  btnView: {
    marginBottom: 20,
    marginHorizontal: 20,
  },
  marginTop: {
    marginTop: 10,
  },
  declinedStatus: {
    marginHorizontal: 45,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderWidth: 0.6,
    borderRadius: 5,
    borderColor: BaseColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.5,
    marginBottom: 15,
  },
  declineText: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.primary,
  },
});
