/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import Image from 'react-native-fast-image';
import {
  Animated,
  Dimensions,
  Easing,
  Modal,
  TouchableOpacity,
  View,
} from 'react-native';
import {BlurView} from '@react-native-community/blur';
import {useSelector} from 'react-redux';
import {useTheme} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';

/**
 * Component for Image
 * @function CImage
 *
 */

export default function CImage(props: {
  imgUrl?: any;
  imgStyle?: any;
  user?: any;
  tintColor?: any;
  resizeMode?: any;
  loaderStyle?: any;
  onpress?: any;
  hashtalker?: any;
  isPreview?: any;
}) {
  const {
    imgUrl,
    imgStyle,
    user = false,
    tintColor,
    resizeMode = 'contain',
    loaderStyle,
    onpress,
    hashtalker,
    isPreview = false,
  } = props;
  const {darkmode} = useSelector((state: any) => {
    return state.auth;
  });
  const colors = useTheme();
  const BaseColors: any = colors.colors;
  const [imageError1, setImageError1] = useState(true);
  const [imageLoader, setImageLoader] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const translateY = useState(new Animated.Value(0))[0];

  const onImageNotFound = () => {
    setImageError1(false);
  };
  const openModal = () => {
    setModalVisible(true);
    Animated.timing(translateY, {
      toValue: 100,
      duration: 300,
      easing: Easing.linear,
      useNativeDriver: true,
    }).start();
  };
  const closeModal = () => {
    Animated.timing(translateY, {
      toValue: 0,
      duration: 300,
      easing: Easing.linear,
      useNativeDriver: true,
    }).start(() => setModalVisible(false));
  };
  return (
    <TouchableOpacity
      activeOpacity={1}
      style={{...imgStyle, borderWidth: hashtalker && 0}}
      onPress={() => {
        isPreview ? openModal() : onpress();
      }}>
      {imageLoader && (
        <View style={{backgroundColor: BaseColors.cardLightBg, ...imgStyle}} />
      )}
      <FastImage
        source={imageError1 && imgUrl ? {uri: imgUrl} : user}
        tintColor={tintColor}
        resizeMode={resizeMode}
        onError={() => onImageNotFound()}
        onLoadStart={() => setImageLoader(true)}
        onLoadEnd={() => setImageLoader(false)}
        style={{
          backgroundColor: BaseColors.cardLightBg,
          ...imgStyle,
        }}
      />
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => closeModal()}>
        <BlurView
          style={{position: 'absolute', left: 0, right: 0, top: 0, bottom: 0}}
          blurType="light"
          blurAmount={10}
          reducedTransparencyFallbackColor="white"
        />
        <TouchableOpacity
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0,0,0,0.2)',
          }}
          onPress={() => closeModal()}
          activeOpacity={1}>
          <FastImage
            tintColor={tintColor}
            onError={() => onImageNotFound()}
            onLoadStart={() => setImageLoader(true)}
            onLoadEnd={() => setImageLoader(false)}
            style={{
              width: Dimensions.get('window').width / 1.2,
              height: Dimensions.get('window').width / 1.2,
              borderRadius: Dimensions.get('window').width / 1.2 / 2,
              backgroundColor: '#1C1C1C',
            }}
            source={imageError1 && imgUrl ? {uri: imgUrl} : user}
            resizeMode={resizeMode}
          />
        </TouchableOpacity>
      </Modal>
    </TouchableOpacity>
  );
}

CImage.propTypes = {};

CImage.defaultProps = {};
