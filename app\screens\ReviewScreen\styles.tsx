import { BaseColors, FontFamily } from '@config/theme';
import { StyleSheet } from 'react-native';
import { Dimensions } from 'react-native';
import { Platform } from 'react-native';
const IOS = Platform.OS === 'ios';
export default StyleSheet.create({
  container: {
    flex: 1,
    // justifyContent: 'center',
    backgroundColor: BaseColors.white,
  },
  mainView: {
    flex: 1,

    marginHorizontal: 15,
  },
  content: {
    flex: 1,
  },
  buttonContainer: {
    paddingBottom: 20, // Add padding for safe area spacing
    alignSelf: 'stretch', // Ensures the button takes up the full width
    marginHorizontal: 10,
  },
  scrollContainer: {
    flexGrow: 1,
    backgroundColor: BaseColors.white,
  },
});
