import React from 'react';
import {Text, View} from 'react-native';
import styles from './styles';
import {translate} from '@language/Translate';

interface SearchComponantProps {}

const SearchComponant: React.FC<SearchComponantProps> = ({}) => {
  return (
    <>
      <View style={styles?.container}>
        <View style={styles?.verifyTalent}>
          <Text style={styles?.textSty}>
            {translate('verifyTextTitle', '')}
          </Text>
          <Text style={styles?.desSty}>{translate('leadingJob')}</Text>
        </View>
      </View>
    </>
  );
};

export default SearchComponant;
