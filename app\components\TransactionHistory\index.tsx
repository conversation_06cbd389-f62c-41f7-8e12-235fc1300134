import React from 'react';
import { copyToClipBoard, downloadFile } from '@app/utils/CommonFunction';
import moment from 'moment';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { Text, TouchableOpacity, View } from 'react-native';
import styles from './styles';
import { BaseColors } from '@config/theme';

export default function TransactionHistory({ item, navigation }: any) {
  const isCredited = item?.transaction === 'credited';
  return (
    <TouchableOpacity activeOpacity={0.8}  onPress={() => {
      navigation.navigate('JobApplicant', { jobID: item?.jobId });
    }}style={styles.renderView}>
      <View style={styles.row}><Text style={styles.name} numberOfLines={1}>
        {`${item?.firstName} ${item?.lastName}` || '-'}
      </Text>

      <Text numberOfLines={1} style={{ ...styles.amount, color: isCredited ? BaseColors.green : BaseColors.red }}>
        {isCredited ? '+ ' : '- '} ${item?.amount || '0'}
      </Text>
      </View>
      <View style={styles.row}>
        <Text style={styles.title} numberOfLines={1}>
          {item?.title || 'New title'}
        </Text>
        <Text style={styles.date}>
          {moment(item?.createdAt).format('DD MMM YYYY')}
        </Text>
      </View>
      <View style={styles.row}>

        <TouchableOpacity style={[styles.clipBoard, styles.row]} activeOpacity={0.8} onPress={() => {
          copyToClipBoard(item?.stripeTransactionId);
        }}>
          <Text style={styles.id}>
            {item?.stripeTransactionId}
          </Text>
          <AntDesign name="copy1"  />
        </TouchableOpacity>

        <TouchableOpacity style={styles.clipBoard} activeOpacity={0.8} onPress={() => {
          if (item?.invoice) {downloadFile(item?.invoice);}
        }}>
          <AntDesign name="download" size={20} color={BaseColors.lightGrey} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
}
