import React, {useRef, useState} from 'react';
import {Text, TouchableOpacity, View} from 'react-native';
import styles from './styles';
import Header from '@components/Header';
import {CustomIcon} from '@config/LoadIcons';
import {translate} from '@language/Translate';
import {BaseColors} from '@config/theme';
import LanguageModal from '@components/LanguageModal';
import {logOutCall} from '@app/utils/CommonFunction';
import {getApiData} from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import Toast from 'react-native-simple-toast';

export default function EditAccount({navigation}: any) {
  const refRBSheet = useRef(null);
  const [openBottomSheet, setOpenBottomSheet] = useState<boolean>(false);
  const [deleteLoader, setDeleteLoader] = useState<boolean>(false);
  const [opendltAccountBottomSheet, setDltAccountBottomSheet] =
    useState<boolean>(false);
  const policies = [
    {title: 'Log Out', screen: '', type: 'logout', icon: 'Upload'},
    {title: 'Delete Account', screen: '', type: 'delete', icon: 'trash'},
  ];

  const handleDeleteAccount = async () => {
    setDeleteLoader(true);
    try {
      const resp = await getApiData({
        endpoint: `${BaseSetting.endpoints.deleteAccount}`,
        method: 'DELETE',
      });
      if (resp?.status) {
        setDeleteLoader(false);
        setOpenBottomSheet(false);
        setDltAccountBottomSheet(false);
        refRBSheet?.current?.close();
        await logOutCall('deleteAccount');
      } else {
        setDeleteLoader(false);
        setOpenBottomSheet(false);
        setDltAccountBottomSheet(false);
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
    } catch (error) {
      setDeleteLoader(false);
      setOpenBottomSheet(false);
      setDltAccountBottomSheet(false);
      setDeleteLoader(false);

      console.error('Error fetching list:', error);
      Toast.show('Failed to fetch data.', Toast.SHORT);
    }
  };
  const [Loader, setLoader] = useState<boolean>(false);

  const handleLogout = async () => {
    setLoader(true);
    await logOutCall();
    setLoader(false);
    navigation.replace('AuthScreen');
    refRBSheet?.current?.close();
  };

  return (
    <View style={{...styles.container}}>
      <Header
        title={translate('manageAccount')}
        leftIcon="back-arrow"
        onLeftPress={() => navigation.goBack()}
      />
      <View style={styles.policyListContainer}>
        {policies.map((policy, index) => (
          <TouchableOpacity
            key={index}
            style={styles.policyItem}
            onPress={() => {
              if (policy?.type === 'logout') {
                if (policy?.type === 'delete') {
                  setOpenBottomSheet(false);
                } else {
                  setOpenBottomSheet(true);
                  refRBSheet.current?.open();
                  return true;
                }
              } else if (policy?.type === 'delete') {
                setDltAccountBottomSheet(true);
                setOpenBottomSheet(false);
                refRBSheet.current?.open();
              } else {
                navigation.navigate(policy.screen);
              }
            }}>
            <CustomIcon
              name={policy.icon}
              size={20}
              color={policy.type === 'delete' ? 'red' : BaseColors.text}
              style={
                policy.type !== 'delete' && {
                  transform: [{rotate: '90deg'}],
                }
              }
            />
            <Text
              style={{
                ...styles.policyTitle,
                color: policy.type === 'delete' ? 'red' : BaseColors.textGrey,
              }}>
              {policy.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      {openBottomSheet ? (
        <LanguageModal
          loader={Loader}
          refRBSheet={refRBSheet}
          setOpenBottomSheet={setOpenBottomSheet}
          title={translate('Logout', '')}
          openLanguage={false}
          cancelProp={translate('Cancel', '')}
          onClick={handleLogout}
          doneProp={translate('logOut', '')}
          showDescription={true}
          Description={translate('askingLogout', '')}
          deleteSty={true}
        />
      ) : (
        <LanguageModal
          loader={deleteLoader}
          refRBSheet={refRBSheet}
          setOpenBottomSheet={setDltAccountBottomSheet}
          title={translate('areyousuretoDelete')}
          openLanguage={false}
          cancelProp={translate('Cancel', '')}
          onClick={handleDeleteAccount}
          doneProp={translate('delete')}
          showDescription={true}
          Description={translate('deleteAccountPermisson')}
          deleteSty={true}
          type="delete"
        />
      )}
    </View>
  );
}
