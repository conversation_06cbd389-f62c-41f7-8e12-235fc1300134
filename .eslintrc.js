module.exports = {
  root: true,
  extends: '@react-native',
  rules: {
    // Formatting rules
    'object-curly-spacing': ['error', 'always'],
    'quotes': ['error', 'single'],                // Enforce single quotes
    'semi': ['error', 'always'],                  // Require semicolons
    'indent': ['error', 2],
    'import/no-duplicates': 'error',
    // Enforce consistent indentation
    // Unused variables
    'no-unused-vars': ['error', { 'argsIgnorePattern': '^_', 'varsIgnorePattern': '^_' }],
    '@typescript-eslint/no-unused-vars': ['error', { 'argsIgnorePattern': '^_', 'varsIgnorePattern': '^_' }],

    // Import rules
    // 'sort-imports': [
    //   'error',
    //   {
    //     'ignoreDeclarationSort': true,
    //     'ignoreCase': false,
    //   },
    // ],
  },
};
