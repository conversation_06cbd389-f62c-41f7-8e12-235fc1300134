import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
} from 'react-native';
import EIcon from 'react-native-vector-icons/Entypo'; // Replace with your specific icon library if different
import styles from './styles';
import {CustomIcon} from '@config/LoadIcons';
import {BaseColors} from '@config/theme';
import {translate} from '@language/Translate';
import {isEmpty} from '@app/utils/lodashFactions';
import RBSheet from 'react-native-raw-bottom-sheet';
import ActionSheet from 'react-native-actionsheet';
import Icon from 'react-native-vector-icons/Fontisto';
import {ScrollView} from 'react-native';
import FIcon from 'react-native-vector-icons/FontAwesome';

const LicenseComponent = ({
  licenseFiles,
  licenseError,
  loaderLicence,
  loader,
  Review,
  handleLicenseFileSelect,
  deleteFile,
  uploadLicenseFile,
  handleOpenCamera,
  ActionSheetRefIOS,
  ActionSheetRef,
  setUploadLicene,
  uploadLicense,
  handleImageFileSelect,
}: any) => {
  const IOS = Platform.OS === 'ios';
  const CANCEL_INDEX = 3;
  const DESTRUCTIVE_INDEX = 0;

  function showActionSheet() {
    if (IOS) {
      ActionSheetRefIOS.current.open();
    } else {
      ActionSheetRef.current.show();
    }
  }

  function doAction(index: any) {
    if (index === 0) {
      handleLicenseFileSelect(index);
    } else if (index === 1) {
      handleOpenCamera();
    } else if (index === 3) {
      handleImageFileSelect();
    }
  }

  const options = [
    <TouchableOpacity
      onPress={() => handleLicenseFileSelect()}
      style={[styles.optionsContainer, {marginTop: IOS ? 15 : 0}]}>
      <Icon
        name="file-1"
        size={22}
        color={BaseColors.primary}
        style={{paddingRight: 5}}
      />
      <Text
        style={{
          marginLeft: 15,
          color: BaseColors.primary,
        }}>
        {translate('Files', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => handleOpenCamera()}
      style={[styles.optionsContainer, {paddingVertical: 10, marginLeft: 6}]}>
      <FIcon
        name="camera"
        size={16}
        color={BaseColors.primary}
        style={{paddingLeft: 4}}
      />
      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Camera', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => handleImageFileSelect()}
      style={[
        styles.optionsContainer,
        {paddingVertical: IOS ? 0 : 10, marginLeft: 6},
      ]}>
      <FIcon name="photo" size={18} color={BaseColors.primary} />
      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Photos', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => {
        if (IOS) {
          ActionSheetRefIOS.current.close();
        } else {
          ActionSheetRef.current.hide();
        }
      }}
      key={`cancel-option-${4}`}
      style={[
        styles.optionsContainer,
        {
          paddingVertical: 10,
          marginHorizontal: IOS ? 0 : 20,
          borderTopWidth: IOS ? 3 : 0,
          borderTopColor: BaseColors.textInput,
        },
      ]}>
      <EIcon name="cross" size={18} color={BaseColors.primary} />

      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Cancel', '')}
      </Text>
    </TouchableOpacity>,
  ];

  return (
    <View style={styles.fileContainer}>
      <Text style={styles.licenseText}>{translate('license', '')}</Text>
      <Text style={styles.submitLicenseText}>
        {translate('submitLiecense', '')}
      </Text>

      <View style={styles.chooseFileContainer}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={Review === 'reviewType' ? null : showActionSheet}>
          {loaderLicence ? (
            <ActivityIndicator color={BaseColors.primary} size={20} />
          ) : (
            <>
              <View style={styles.iconViewSty}>
                <CustomIcon
                  name="Upload"
                  size={20}
                  color={BaseColors.primary}
                />
              </View>
              <Text style={styles.chooseFileText}>
                {translate('chooseFiles', '')}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      <View style={[styles.fileFormatContainer, {paddingBottom: 2}]}>
        <Text style={styles.acceptedFileText}>
          {translate('accepetedFileLicence', '')}
        </Text>
        <Text style={styles.maxSizeText}>{translate('maxSize', '')}</Text>
      </View>
      {licenseError && <Text style={styles.errorText}>{licenseError}</Text>}

      {/* <ScrollView style={{flex: 1}}> */}
      {!isEmpty(uploadLicenseFile) &&
        uploadLicenseFile?.map((file, index) => (
          <View key={index} style={styles.licenseFileContainer}>
            <View style={styles.fileInfoContainer}>
              <View style={styles.iconSty}>
                <CustomIcon
                  name="document"
                  size={25}
                  color={styles.fileIconColor.color}
                />
              </View>
              <View style={styles.fileDetailsContainer}>
                <Text numberOfLines={1} style={styles.fileNameText}>
                  {file
                    ? typeof file === 'string' && file.includes('http')
                      ? file.split('/').pop()
                      : typeof file === 'string'
                      ? file
                      : file?.fileName || '-'
                    : file
                    ? typeof file.url === 'string' && file.url.includes('http')
                      ? file.url.split('/').pop()
                      : typeof file === 'string'
                      ? file
                      : file?.fileName || '-'
                    : '-'}
                </Text>
                {file?.fileSize && (
                  <Text style={styles.fileSizeText}>
                    {translate('Size', '')}: {file?.fileSize || '-'}
                  </Text>
                )}
              </View>
            </View>
          </View>
        ))}
      {/* </ScrollView> */}

      <ActionSheet
        ref={ActionSheetRef}
        options={options}
        cancelButtonIndex={CANCEL_INDEX}
        destructiveButtonIndex={DESTRUCTIVE_INDEX}
        onPress={(index: any) => doAction(index)}
      />
      <RBSheet
        ref={ActionSheetRefIOS}
        closeOnDragDown={true}
        closeOnPressMask={true}
        dragFromTopOnly={true}
        height={180}
        customStyles={{
          draggableIcon: {
            width: 50,
            marginTop: 30,
          },
          container: {
            backgroundColor: '#FFF',
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
          },
        }}>
        <View>
          {options?.map(item => {
            return item;
          })}
        </View>
      </RBSheet>
    </View>
  );
};
export default LicenseComponent;
