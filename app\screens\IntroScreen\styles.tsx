import {Dimensions, Platform, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const {width, height} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

const dimensions = {
  imgHeight: height / 2.7,
  imgWidth: width / 1.35,
  dotWidth: width * 0.09,
  dotHeight: height * 0.005,
  titleFontSize: 30,
  bodyFontSize: 16,
};

export default StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  skipButton: {
    marginTop:
      Platform.OS === 'ios'
        ? Dimensions.get('screen').height / 15
        : Dimensions.get('screen').height / 30,
    marginRight: 16,
    alignSelf: 'flex-end',
    borderRadius: 6,
    paddingVertical: 7,
    // borderWidth: 1,
    // borderColor: BaseColors?.primary,
  },
  swiperContainer: {
    backgroundColor: BaseColors.white,
  },
  container: {
    flex: 1,
    // backgroundColor: 'yellow',
  },
  imgContainer: {
    justifyContent: 'center',
    alignSelf: 'center',
  },
  imgView: {
    height: dimensions.imgHeight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  introImage: {
    width: dimensions.imgWidth,
    height: dimensions.imgHeight,
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    position: 'absolute',
    top: IOS ? height / 1.8 : height / 2.07,
    alignSelf: 'center',
  },
  dot: {
    width: dimensions.dotWidth,
    height: dimensions.dotHeight,
    borderRadius: 7,
    marginHorizontal: 3,
  },
  textContainer: {
    paddingHorizontal: 20,
    marginTop: IOS
      ? Dimensions.get('screen').height / 40
      : Dimensions.get('screen').height / 40,
    alignSelf: 'center',
    position: 'absolute',
    top: IOS ? height / 1.69 : height / 2.03,
  },
  titleText: {
    color: BaseColors.textColor,
    fontSize: dimensions.titleFontSize,
    textAlign: 'center',
    fontFamily: FontFamily.OpenSansSemiBold,
    textTransform: 'capitalize',
  },
  bodyText: {
    color: BaseColors.discriptionTextColor,
    fontSize: dimensions.bodyFontSize,
    textAlign: 'center',
    paddingTop: Dimensions.get('screen').height / 40,
    fontFamily: FontFamily.OpenSansRegular,
    lineHeight: 18,
    // textTransform: 'capitalize',

  },
  buttonContainer: {
    // marginBottom: Dimensions.get('screen').height / 18,
    // marginTop: Dimensions.get('screen').height / 40,
    marginHorizontal: 30,
    marginBottom: 20,
  },
  logInContainer: {
    marginHorizontal: 30,
    marginBottom: Dimensions.get('screen').height / 15,
  },
  nextButton: {
    backgroundColor: BaseColors.primary,
  },
  rbSheetCustomStyles: {
    wrapper: {backgroundColor: 'hsla(360, 20%,2%, 0.6)'},
    draggableIcon: {backgroundColor: '#000'},
    container: {
      backgroundColor: 'white',
      borderTopRightRadius: 20,
      borderTopLeftRadius: 20,
      height: height / 2.5,
    },
  },
  languageContainer: {
    marginTop: Dimensions.get('screen').height / 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 28,
    marginHorizontal: 20,
  },
  languageTitle: {
    fontSize: 30,
    color: BaseColors.textColor,
    fontWeight: '600',
  },
  languageOption: {
    height: height / 16,
    // marginBottom: 15,
    borderWidth: 1.7,
    justifyContent: 'center',
    // alignItems: 'center',
    width: width / 1.2,
    borderRadius: 6,
    marginTop: Dimensions.get('screen').height / 40,
  },
  languageText: {
    fontSize: 17,
    color: BaseColors.textColor,
    fontWeight: '500',
    paddingHorizontal: 20,
  },
  iconPadding: {
    position: 'absolute',
    right: 15,
  },
  languageButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: Dimensions.get('screen').height / 40,
    width: '100%',
    paddingHorizontal: 15,
  },
  imageView: {
    width: 50,
    height: 27,
  },
});
