import {Dimensions, Platform, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const IOS = Platform.OS === 'ios';
const HEIGHT = Dimensions.get('window').height;

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseColors?.white,
  },
  mainContainer: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  mainView: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 12,
    backgroundColor: '#fff',
  },
  titleView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  questionText: {
    fontSize: 16,
    fontFamily: FontFamily?.OpenSansBold,
    color: BaseColors?.inputColor,
    textTransform: 'capitalize',
  },
  answerView: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderTopWidth: 1,
    borderTopColor: '#fff',
  },
  answerText: {
    fontSize: 14,
    color: BaseColors?.inputColor,
    fontFamily: FontFamily?.OpenSansMedium,
    textTransform: 'capitalize',

  },
  centerMain: {
    flex: 1,
    marginTop: HEIGHT / 4.3,
  },
  searchView: {
    marginBottom: 20,
  },
});
