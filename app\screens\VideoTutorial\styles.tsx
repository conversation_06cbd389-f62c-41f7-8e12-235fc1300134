import {Dimensions, Platform, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');

export default StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: BaseColors?.white,

    // paddingBottom: Dimensions.get('screen').height / 9.8,
  },
  videoBoxSty: {
    width: '100%',
    height: Dimensions?.get('screen').height / 4,
    borderWidth: 1,
    borderColor: BaseColors?.primary,
    borderRadius: 10,
  },
  mainViewSty: {
    marginHorizontal: 25,
    marginBottom: 10,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: 10,
  },
  Textoverlay: {
    position: 'absolute',
    left: 10,
    bottom: 10,
    justifyContent: 'center',
  },
  playButton: {
    fontSize: 40,
    color: 'white',
  },
  textSty: {
    color: BaseColors?.white,
    fontSize: 16,
    fontFamily: FontFamily?.OpenSansMedium,
  },
  videoSty: {
    marginBottom: 10,
    flex: 1,
  },
  modalContent: {
    width: width * 0.9,
    height: height * 0.6,
    backgroundColor: BaseColors.black,
    borderRadius: 10,
    overflow: 'hidden',
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 2,
  },
  webView: {
    // flex: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: BaseColors.black, // Ensure a dark background
  },
});
