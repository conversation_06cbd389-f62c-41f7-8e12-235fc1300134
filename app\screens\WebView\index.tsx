import React from 'react';
import {View} from 'react-native';
import {BaseColors} from '@config/theme';
import Header from '@components/Header';
import WebViewCmp from '@components/WebView';
import BaseSetting from '@config/setting';
import styles from './styles';
import {translate} from '@language/Translate';

/**
 * WebView screen
 * @module  privacy
 *
 */

export default function WebViewScreen({navigation, route}: any) {
  const params = route?.params;

  const type = params?.type;
  const uri = params?.uri;

  let url = BaseSetting.externalEndpoints.termsService;
  if (type === 'PrivacyPolicy') {
    url = BaseSetting.externalEndpoints.policy;
  }
  if (type === 'terms') {
    url = BaseSetting.externalEndpoints.termsService;
  }

  return (
    <View style={{flex: 1, backgroundColor: BaseColors.white}}>
      <Header
        title={
          type === 'PrivacyPolicy'
            ? translate('privacy')
            : type === 'profile'
            ? translate('profileSet')
              : type === 'jobView'
              ? ''
            : translate('terms')
        }
        leftIcon="back-arrow"
        onLeftPress={() => navigation.goBack()}
      />
      <View style={styles.container}>
        <WebViewCmp
          url={type === 'profile' ? uri : type === 'jobView' ? uri : url}
          style={{}}
          type={type}
        />
      </View>
    </View>
  );
}
