module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    'react-native-reanimated/plugin',
    'hot-updater/babel-plugin',
    [
      'module-resolver',
      {
        root: ['./app'],
        alias: {
          '@config': './app/config',
          '@components': './app/components',
          '@assets': './app/assets',
          '@redux': './app/redux',
          '@screens': './app/screens',
          '@navigation': './app/navigation',
          '@language': './app/lang',
          '@app': './app',
        },
        extensions: ['.js', '.jsx', '.ts', '.tsx'], // Include extensions if you use TypeScript or JSX
      },
    ],
  ],
};
