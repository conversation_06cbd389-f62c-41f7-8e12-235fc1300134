import {capitalizeFirstLetter} from '@app/utils/CommonFunction';
import TextInput from '@components/UI/TextInput';
import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {translate} from '@language/Translate';
import React, {useState} from 'react';
import {Keyboard, StyleSheet, Text, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {Rating} from 'react-native-ratings';

const EmployeeRating = ({
  setText,
  text,
  setRating,
  lastName,
  firstName,
}: any) => {
  const handleRating = (rating: number) => {
    console.log('Selected Rating:', rating);
    setRating(rating); // Pass the selected rating back to the parent
  };

  return (
    <View style={styles?.bodySty}>
      <KeyboardAwareScrollView
        bounces={false}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={false}>
        <Text style={styles.title}>
          {translate('writeYourExperince', '')} {''}
          <Text
            style={[
              styles.title,
              {
         
              },
            ]}>
           {firstName || lastName
              ? `${capitalizeFirstLetter(firstName)} ${capitalizeFirstLetter(
                  lastName?.charAt(0),
                )}`
              : ''}
          </Text>
          {''} ?
        </Text>
        <View style={styles.container}>
          <Rating
            type="custom"
            ratingColor={BaseColors.starColor}
            ratingBackgroundColor={BaseColors.ratingbackground}
            ratingCount={5}
            imageSize={30}
            tintColor={BaseColors.lightcolor}
            startingValue={0} // Set default rating to 0
            style={{flexDirection: 'column-reverse'}}
            onFinishRating={handleRating} // Use updated function
          />
        </View>
        <View style={styles?.textInputSty}>
          <TextInput
            value={text}
            onChange={(value: any) => {
              setText(value.trimStart());
            }}
            titleLines={2}
            title={translate('tellUsMore', '')}
            titleSty={styles.title}
            textArea={true}
            placeholderText={translate('typeHere', '')}
            maxLength={300}
            placeholderStyle={{color: BaseColors.black}}
            style={{
              borderColor: BaseColors.white,
              backgroundColor: '#f5faff',
              opacity: 1,
            }}
            onSubmit={() => {
              Keyboard.dismiss();
            }}
          />
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 10,
    backgroundColor: '#f6f9ff',
    alignItems: 'center',
  },
  bodySty: {
    margin: 10,
  },
  title: {
    fontSize: 18,
    color: BaseColors?.textColor,
    marginBottom: 10,
    fontFamily: FontFamily.OpenSansRegular,
  },
  ratingContainer: {
    marginVertical: 10,
  },
  rating: {
    backgroundColor: '#f6f9ff', // Ensures no background color issues
  },
  description: {
    fontSize: 14,
    color: BaseColors.inputColor,
    textAlign: 'center',
    fontFamily: FontFamily.OpenSansRegular,
    marginTop: 20,
  },
  textInputSty: {
    marginTop: 20,
  },
  scrollContainer: {
    flexGrow: 1,
  },
});

export default EmployeeRating;
