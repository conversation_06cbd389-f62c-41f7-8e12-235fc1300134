buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "25.1.8937393"
        kotlinVersion = "2.0.0"
        supportLibVersion  = "28.0.0"
        googlePlayServicesVersion  = "+"
        firebaseMessagingVersion  = "23.4.0"
        androidGradlePluginVersion = "8.5.0"
    }
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.google.com' }
        maven { url 'https://www.jitpack.io' }
    }
    dependencies {
        classpath("com.android.tools.build:gradle:${androidGradlePluginVersion}")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${kotlinVersion}")
        
        classpath("com.google.gms:google-services:4.4.2")
        // classpath 'com.google.firebase:perf-plugin:1.4.1'
    }
}

allprojects {
    repositories {
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are
            // older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }
        maven {
            url "https://sdk.withpersona.com/android/releases"
        }
        google()
        maven { url 'https://www.jitpack.io' }
        maven { url = uri("https://plugins.gradle.org/m2/") }
    }
}

apply plugin: "com.facebook.react.rootproject"
