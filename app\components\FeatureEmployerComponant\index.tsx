import React, {useCallback, useState} from 'react';
import {ActivityIndicator, FlatList, View} from 'react-native';
import NoRecord from '@components/NoRecord';
import styles from './styles';
import BaseSetting from '@config/setting';
import {getApiData} from '@app/utils/apiHelper';
import Toast from 'react-native-simple-toast';
import {BaseColors} from '@config/theme';
import {translate} from '@language/Translate';
import SeekerCard from '@components/SeekerCard';
import EmployarCard from '@components/EmployerCard';
import {useFocusEffect} from '@react-navigation/native';

const FeatureEmployerComponant = ({
  navigation,
  featueType,
  featureloader,
  setFeatureLoader,
}: any) => {
  const [saveLoader, setSaveLoader] = useState(false);

  const [featurelist, setFeatureList] = useState({
    data: [],
    pagination: {currentPage: 1, isMore: null},
  });
  const [upcomingJobData, setUpcomingJobData] = useState({
    data: [],
    pagination: {currentPage: 1, isMore: null},
  });
  console.log('🚀 ~ featurelist:', featurelist);
  const [featurebottomLoading, setFeatureBottomLoading] = useState(false);

  const ListEndFeatureLoader = () => {
    if (!featureloader && featurebottomLoading) {
      return (
        <View style={{alignSelf: 'center', justifyContent: 'center', flex: 1}}>
          <ActivityIndicator color={BaseColors.primary} size={'small'} />
        </View>
      );
    }
  };

  const getJobList = async (page = 1, bottomFeatureLoader: boolean = false) => {
    if (bottomFeatureLoader) {
      setFeatureBottomLoading(true);
    } else {
      setFeatureLoader(true);
    }
    const data: any = {page, limit: 5};
    if (featueType === 'forEmployer') {
      data.type = 'employer';
    } else {
      data.type = 'seeker';
    }
    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.getFeatured,
        method: 'GET',
        data: data,
      });
      if (resp?.data && resp?.status) {
        setFeatureList(p => ({
          ...p,
          data:
            page > 1
              ? [...featurelist?.data, ...resp?.data?.items]
              : resp.data?.items || [],
          pagination: resp?.data?.pagination,
        }));
      } else {
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }

      setFeatureBottomLoading(false);
      setFeatureLoader(false);
    } catch (e) {
      setFeatureBottomLoading(false);
      setFeatureLoader(false);
      console.log('ERRR', e);
    }
  };

  const handleSave = useCallback(
    async (item: any) => {
      setSaveLoader(item?.id);
      try {
        let data: any = {};
        if (featueType === 'forSeeker') {
          data = {userId: item?.id};
        } else {
          data = {jobId: item?.id};
        }
        const resp = await getApiData({
          endpoint:
            featueType === 'forSeeker'
              ? BaseSetting.endpoints.seekerSave
              : BaseSetting.endpoints.saveCard,
          method: 'POST',
          data: data,
        });
        if (resp?.status) {
          setFeatureList((prevList: any) => ({
            ...prevList,
            data: prevList?.data?.map((i: any) =>
              i.id === item?.id
                ? {
                    ...i,
                    isSaved: !i?.isSaved, // Revert the save state if error occurs
                    savedUser: i?.savedUser ? null : item?.savedUser, // Revert savedUser
                  }
                : i,
            ),
          }));
        }
        setSaveLoader(false);
      } catch (e) {
        setSaveLoader(false);
        console.log('ERRR', e);
      }
    },
    [featueType],
  );
  // Function to get list of seekers or employers
  const getList = async (
    page = 1,
    bottomLoader: boolean = false,
    status: string = '',
  ) => {
    const data: any = {
      page,
      limit: 5,
      type: 'myHarbor',
      status: 'upcoming',
      sortKey: 'level',
      sort: 'ASC',
    };

    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.jobList,
        method: 'POST',
        data: data,
      });
      console.log('resp ==>', JSON.stringify(resp));
      if (resp?.data && resp?.status) {
        setUpcomingJobData(p => ({
          ...p,
          data:
            page > 1
              ? [...upcomingJobData?.data, ...resp?.data?.items]
              : resp.data?.items || [],
          pagination: resp?.data?.pagination,
        }));
      } else {
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
    } catch (e) {
      console.log('ERRR', e);
    }
  };
  const renderItem = (item: any) => {
    if (featueType === 'forSeeker') {
      return (
        <SeekerCard
          item={item}
          onSave={handleSave}
          saveLoader={saveLoader}
          navigation={navigation}
          type="seeker"
          bodyType="home"
        />
      );
    } else {
      return (
        <EmployarCard
          item={item}
          onSave={handleSave}
          saveLoader={saveLoader}
          navigation={navigation}
          navigateName={'JobApplicant'}
          bodyType="home"
        />
      );
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      console.log('useFocuss effect called');
      getJobList(1, false);
      if (featueType === 'upcomingJobs') {
        getList(1, false);
      }
      return () => {};
    }, [featueType]),
  );

  return (
    <>
      {featureloader ? (
        <View style={styles.centerMain}>
          <ActivityIndicator color={BaseColors.primary} />
        </View>
      ) : (
        <FlatList
          data={
            featueType === 'upcomingJobs'
              ? upcomingJobData?.data
              : featurelist?.data || []
          }
          showsHorizontalScrollIndicator
          horizontal={true}
          snapToAlignment="center" // Center alignment for snapping
          keyExtractor={(item: any) => `${item.id}+1`}
          renderItem={({item}) => renderItem(item)}
          contentContainerStyle={{
            marginTop: 10,
            marginHorizontal: 5,
          }}
          ListFooterComponent={ListEndFeatureLoader}
          ListEmptyComponent={
            <View style={styles.centerMain}>
              <NoRecord title={'noData'} type="employer" iconName="employer" />
            </View>
          }
          onScroll={({nativeEvent}) => {
            const {layoutMeasurement, contentOffset, contentSize} = nativeEvent;
            const isCloseToBottom =
              layoutMeasurement.height + contentOffset.y >=
              contentSize.height - 20;

            if (
              isCloseToBottom &&
              featurelist?.pagination?.isMore &&
              !featureloader &&
              !featurebottomLoading
            ) {
              getJobList(
                Number(featurelist?.pagination?.currentPage) + 1,
                true,
              );
            }
          }}
          scrollEventThrottle={16} // Ensure smooth handling of scroll events
          scrollEnabled
        />
      )}
    </>
  );
};

export default FeatureEmployerComponant;
