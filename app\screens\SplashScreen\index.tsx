import React, { useRef, useState } from 'react';
import { View } from 'react-native';
import styles from './styles';
import { useTheme } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import Video from 'react-native-video';
import { isEmpty, isString } from 'lodash-es';

export default function SplashScreen(props: any) {
  const { navigation }: any = props;
  const {
    accessToken,
  } = useSelector((state: any) => {
    return state.auth;
  });

  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  const [playInBackground, setPlayInBackground] = useState(false);

  const videoPlayerRef = useRef();
  const colors = useTheme();
  const BaseColors: any = colors.colors;

  return (
    <View
      style={{
        ...styles.container,
        backgroundColor: BaseColors.backgroundColor,
      }}>
      <Video
        source={{
          uri: 'https://theharborapp.azureedge.net/job/Splashscreen1.mp4',
        }}
        ref={videoPlayerRef}
        style={styles.backgroundVideo}
        muted={false}
        repeat={false}
        allowsExternalPlayback={true}
        playInBackground={playInBackground}
        playWhenInactive={playInBackground}
        resizeMode={'cover'}
        disableFocus={true}
        ignoreSilentSwitch="obey"
        onEnd={() => {
          navigation.replace(
            accessToken ? 'BottomTabsNavigator' : 'IntroScreen',
          );
        }}
      />
    </View>
  );
}
