import React, {useState} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {BaseColors} from '../../config/theme';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import styles from './styles';
import Button from '../UI/Button';
import EIcon from 'react-native-vector-icons/Entypo';
import Toast from 'react-native-simple-toast';
import {translate} from '@language/Translate';
import {CustomIcon} from '@config/LoadIcons';

export default function Plans({navigation}: any) {
  const [selectedPlan, setSelectedPlan] = useState<number>(0);
  const [expandedPlan, setExpandedPlan] = useState(null); // New state to track expanded plan
  const plans = [
    {
      name: translate('basicPlan', ''),
      price: translate('basicPrice', ''),
      popular: false,
      features: [
        '0$/mo/yr',
        'Access to Seekers & Employers',
        '3 Day Trial AI Assistant',
      ],
    },
    {
      name: translate('herborVerified', ''),
      price: translate('verifyPrice', ''),
      popular: true,
      tag: 'Most Popular',
      features: [
        'Verified Badge',
        'Priority Access',
        '5 Day Trial AI Assistant',
      ],
    },
    {
      name: translate('herborPro', ''),
      price: translate('proPrice', ''),
      popular: true,
      tag: 'Best Value',
      proTag: 'Pro',
      features: ['Pro Badge', 'Unlimited Access', '7 Day Trial AI Assistant'],
    },
  ];

  const validateFields = () => {
    let isValid = true;

    if (selectedPlan === null) {
      isValid = false;
      Toast.show('Please select any plan', Toast.SHORT);
    } else {
      navigation.navigate('PaymentScreen', {selectedPlan: selectedPlan});
    }
    return isValid;
  };

  return (
    <View style={styles?.containerSty}>
      <KeyboardAwareScrollView
        bounces={false}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={false}>
        <View style={styles?.chooseplanSty}>
          <Text style={styles.headerText}>{translate('selectPlan', '')}</Text>
          <Text style={styles.subHeaderText}>
            {translate('choosePlan', '')}
          </Text>
          {/* Plan Options */}
          {plans.map((plan, index) => (
            <View
              key={index}
              style={[
                styles.planContainer,
                selectedPlan === index && styles.selectedPlanContainer,
                {borderStyle: selectedPlan === index ? 'solid' : 'dashed'},
              ]}>
              <View style={styles.planHeader}>
                <TouchableOpacity
                  style={[
                    styles.radioCircle,
                    selectedPlan === index && styles.radioCircleSelected,
                  ]}
                  onPress={() => setSelectedPlan(index)}>
                  <View
                    style={[
                      styles.innerradioCircle,
                      selectedPlan === index && styles.innerradioCircleSelected,
                    ]}>
                    {selectedPlan === index && <View style={styles.radioDot} />}
                  </View>
                </TouchableOpacity>

                <Text
                  style={[
                    styles.planTitle,
                    selectedPlan === index && styles.selectedPlanName,
                  ]}>
                  {plan.name}
                </Text>
                {plan.tag && (
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <View
                      style={[
                        styles.tagContainer,
                        selectedPlan === index && styles.selectagContainer,
                        {
                          backgroundColor:
                            plan.name === 'Harbor Verified' &&
                            selectedPlan === index
                              ? BaseColors.white
                              : plan.name === 'Harbor Verified'
                              ? BaseColors.primary
                              : '#b1cbe2',
                        },
                      ]}>
                      <Text
                        style={[
                          styles.tagText,
                          selectedPlan === index && styles.selectTagText,
                          {
                            color:
                              plan.name === 'Harbor Verified' &&
                              selectedPlan === index
                                ? BaseColors.primary
                                : plan.name === 'Harbor Verified'
                                ? BaseColors.white
                                : BaseColors.primary,
                          },
                        ]}>
                        {plan.tag}
                      </Text>
                    </View>
                    <View style={{paddingHorizontal: 7}}>
                      <CustomIcon
                        name="verified"
                        size={20}
                        color={
                          selectedPlan === index
                            ? BaseColors.white
                            : BaseColors.vecorColor
                        }
                      />
                    </View>
                  </View>
                )}
                {plan.proTag && (
                  <View
                    style={[
                      styles.proTagContainer,
                      {
                        backgroundColor:
                          selectedPlan === index
                            ? BaseColors.white
                            : BaseColors.primary,
                      },
                    ]}>
                    <Text
                      style={[
                        styles.proTagText,
                        {
                          color:
                            selectedPlan === index
                              ? BaseColors.primary
                              : BaseColors.white,
                        },
                      ]}>
                      {plan.proTag}
                    </Text>
                  </View>
                )}
              </View>
              <View
                style={[
                  styles?.priceViewSty,
                  {
                    backgroundColor:
                      expandedPlan && selectedPlan === index
                        ? BaseColors.inputBackground
                        : BaseColors.white,
                  },
                ]}>
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={() =>
                    setExpandedPlan(expandedPlan === index ? null : index)
                  }
                  style={{
                    ...styles.planDetails,
                    borderWidth: expandedPlan === index ? null : 1,
                  }}>
                  <Text
                    style={{
                      ...styles.planPrice,
                      color: BaseColors.textColor,
                    }}>
                    {plan.price}
                  </Text>

                  <CustomIcon
                    name={'ChevronLeft'}
                    size={20}
                    color={BaseColors.inputColor}
                    style={{
                      transform:
                        expandedPlan === index
                          ? [{rotate: '180deg'}]
                          : [{rotate: '0deg'}],
                      top: expandedPlan === index ? -2 : 3,
                    }}
                  />
                </TouchableOpacity>
                <View style={styles.underLineView} />
                {expandedPlan === index && (
                  <View
                    style={[
                      styles.selectedPlanDetails,
                      {
                        backgroundColor:
                          expandedPlan && selectedPlan === index
                            ? BaseColors.inputBackground
                            : BaseColors.white,
                        paddingBottom: selectedPlan === index ? 13 : 0,
                      },
                    ]}>
                    <Text style={styles.featuresTitle}>Features:</Text>
                    {plan.features.map((feature, i) => (
                      <Text key={i} style={styles.featureItem}>
                        • {feature}
                      </Text>
                    ))}
                  </View>
                )}
              </View>
            </View>
          ))}
        </View>
      </KeyboardAwareScrollView>
      {/* Next Button */}
      <View style={styles?.nextViewSty}>
        <Button
          type="text"
          onPress={() => {
            validateFields();
          }}
          loading={false}>
          {translate('next', '')}
        </Button>
      </View>
    </View>
  );
}
