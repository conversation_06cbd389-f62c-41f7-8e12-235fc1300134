import React, {useState} from 'react';
import {ActivityIndicator, Text, TouchableOpacity, View} from 'react-native';
import EIcon from 'react-native-vector-icons/Entypo';
import {isEmpty} from '@app/utils/lodashFactions';
import {CustomIcon} from '@config/LoadIcons';
import {translate} from '@language/Translate';
import {BaseColors} from '@config/theme';
import styles from './styles';
import FIcon from 'react-native-vector-icons/FontAwesome'; // Replace with your specific icon library if different
import Toast from 'react-native-simple-toast';
import BaseSetting from '@config/setting';
import {getApiData} from '@app/utils/apiHelper';
import FastImage from 'react-native-fast-image';
import {useDispatch, useSelector} from 'react-redux';
import authActions from '@redux/reducers/auth/actions';

const LicenseFileList = ({
  licenseFiles,
  reviewType,
  navigation,
  setLicenseFiles,
  userDetails,
  setUploadLicene,
}: any) => {
  if (
    reviewType === 'reviewbyEmployer'
      ? isEmpty(userDetails?.licenses)
      : isEmpty(licenseFiles)
  ) {
    return null;
  }
  const dispatch = useDispatch();

  const [showAll, setShowAll] = useState<boolean>(false);
  const [loader, setLoader] = useState<any>(false);

  const deleteFile = async (type: string, index?: any) => {
    setLoader(index ? index : 'cv');

    // Set up file data based on the type
    const data = {array: [licenseFiles[index]?.id] || licenseFiles[index]}; // Get the fileName of the selected license file

    try {
      const url = BaseSetting.endpoints.deleteQualification;
      const resp = await getApiData({
        endpoint: url,
        method: 'POST',
        data: data,
      });

      if (resp?.status) {
        Toast.show(resp?.message, Toast.BOTTOM);
        setUploadLicene(resp?.data?.licenses);
        setLicenseFiles(resp?.data?.licenses);
      } else {
        Toast.show(resp?.message, Toast.BOTTOM);
      }
      setLoader(false);
    } catch (err) {
      setLoader(false);
    }
  };

  const visibleExperiences = showAll
    ? reviewType === 'reviewbyEmployer'
      ? userDetails?.licenses
      : licenseFiles
    : reviewType === 'reviewbyEmployer'
    ? userDetails?.licenses?.slice(0, 2)
    : licenseFiles.slice(0, 2);

  return (
    <View style={styles.fileContainer}>
      <Text style={styles.resumeText}>{translate('license', '')}</Text>
      <View>
        {/* <FlatList data={licenseFiles} numColumns={2} renderItem={()=>{

        }}/> */}
      </View>
      <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
        {visibleExperiences.map((file, index) => (
          <View
            key={index}
            style={[
              styles.licenseFileContainer,
              {
                width: '43%',
                padding: 10,
              },
            ]}>
            {reviewType === 'reviewbyEmployer' ||
            reviewType === 'review' ? null : (
              <TouchableOpacity
                style={[styles.removeFileButton, {margin: 5}]}
                onPress={() => deleteFile('licence', index)}>
                {loader === index ? (
                  <ActivityIndicator color={BaseColors.primary} />
                ) : (
                  <EIcon name="cross" size={20} color={BaseColors.textGrey} />
                )}
              </TouchableOpacity>
            )}
            <View
              style={[
                styles.fileInfoContainer,
                {paddingTop: reviewType === 'review' ? 0 : 25},
              ]}>
              <View style={{alignItems: 'center', justifyContent: 'center'}}>
                <TouchableOpacity
                  onPress={() => {
                    if (/\.(jpg|jpeg|png|gif)$/i.test(file?.url)) {
                      navigation.navigate('GalleryView', {
                        images: [file?.url],
                        index: 0,
                      });
                    } else if (/\.(pdf|docx)$/i.test(file?.url)) {
                      navigation.navigate('WebViewScreen', {
                        uri: file?.url,
                        type: 'profile',
                      });
                    }
                    // Handle other file types if needed (e.g., show file icon)
                    else {
                      console.log('File type not supported for preview');
                    }
                  }}
                  style={[
                    styles.iconSty,
                    // {
                    //   marginTop: reviewType === 'review' ? null : 20,
                    //   marginBottom: 5,
                    // },
                  ]}>
                  {file?.url ? (
                    // Check for image files
                    /\.(jpg|jpeg|png|gif)$/i.test(file?.url) ? (
                      <FastImage
                        source={{uri: file?.url}}
                        style={{width: '100%', height: '100%'}}
                        resizeMode="contain"
                      />
                    ) : /\.(pdf|docx)$/i.test(file?.url) ? (
                      // Check for document files (pdf, docx, etc.)
                      <FIcon
                        name="file-pdf-o" // You can replace this with the appropriate icon if needed
                        size={30}
                        color={BaseColors.primary}
                      />
                    ) : (
                      // Default for unsupported file types
                      <FIcon name="file" size={30} color={BaseColors.primary} />
                    )
                  ) : (
                    <FIcon name="file" size={30} color={BaseColors.primary} />
                  )}
                </TouchableOpacity>
                {file?.isVerified ? (
                  <View style={{marginTop: 10}}>
                    <CustomIcon
                      name="Vector"
                      color={BaseColors.primary}
                      size={20}
                    />
                  </View>
                ) : null}
              </View>
              {/* <View style={styles.fileDetailsContainer}>
          <Text numberOfLines={1} style={styles.fileNameText}>
            {file
              ? typeof file === 'string' && file.includes('http')
                ? file.split('/').pop()
                : typeof file === 'string'
                ? file
                : file?.fileName || '-'
              : '-'}
          </Text>
          {file?.fileSize && (
            <Text style={styles.fileSizeText}>
              {translate('Size', '')}: {file?.fileSize || '-'}
            </Text>
          )}
        </View> */}
            </View>

            {/* {reviewType === 'review' ? null : (
              <TouchableOpacity
                style={styles.removeFileButton}
                onPress={() => deleteFile('licence', index)}>
                {loader === index ? (
                  <ActivityIndicator color={BaseColors.primary} />
                ) : (
                  <EIcon name="cross" size={20} color={BaseColors.textGrey} />
                )}
              </TouchableOpacity>
            )} */}
          </View>
        ))}
      </View>
      {reviewType === 'reviewbyEmployer'
        ? userDetails?.licenses?.length > 2
        : licenseFiles.length > 2 && (
            <TouchableOpacity
              onPress={() => setShowAll(!showAll)}
              style={styles.seeMoreButton}>
              <Text style={styles.seeMoreText}>
                {showAll ? translate('Less', '') : translate('More', '')}
              </Text>
            </TouchableOpacity>
          )}
    </View>
  );
};

export default LicenseFileList;
