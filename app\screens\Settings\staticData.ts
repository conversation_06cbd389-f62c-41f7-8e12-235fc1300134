import {Images} from '@config/images';
import {translate} from '@language/Translate';

// Sample data array for settings items
export const settings = [
  // {
  //   id: '1',
  //   title: 'editProfile',
  //   icon: 'profile',
  //   screen: 'editProfile',
  //   type: 'ProfileSetUp',
  // },
  // {
  //   id: '2',
  //   title: 'notifications',
  //   icon: 'notification1',
  //   screen: 'notification',
  //   type: 'Notification',
  // },
  // {
  //   id: '3',
  //   title: 'changeLanguage',
  //   icon: 'flag',
  //   isPopup: 'language',
  // },
  {
    id: '4',
    title: 'Verification',
    icon: 'profile',
    screen: 'VerificationDetail',
    type: 'VerificationDetail',
  },
  {
    id: '5',
    title: 'manageAccount',
    icon: 'AboutApp',
    type: 'EditAccount',
    screen: 'EditAccount',
  },
];
// Data for rewards
export const rewardItems = [
  {
    id: 1,
    name: 'briefcase1',
    text: 'Job Applications',
    count: 25,
  },
  {
    id: 2,
    name: 'profile',
    text: 'Profile Completions',
    count: 25,
  },
  {
    id: 3,
    name: 'Vector',
    text: 'Badges Earned',
    count: 25,
  },
];
export const notificationData = [
  {
    id: '1',
    title: 'editProfile',
    message:
      'hddkds nsdnasnadasd ewldwlkdwdwd application for test location is in . stay turn for updates and chck',
  },
  {
    id: '2',
    title: 'notifications',
    message:
      'hddkds nsdnasnadasd ewldwlkdwdwd application for test location is in . stay tur',
  },
];
export const lookingData = [
  {
    id: '1',
    title: 'Captain',
    icon: Images.Captain,
  },
  {
    id: '2',
    title: 'Mate',
    icon: Images.Mate,
  },
  {
    id: '3',
    title: 'Stew',
    icon: Images.Stew,
  },
  {
    id: '4',
    title: 'Engineer',
    icon: Images.Engineer,
  },
  {
    id: '5',
    title: 'Technician',
    icon: Images.Technician,
  },
  {
    id: '6',
    title: 'Admin',
    icon: Images.Captain,
  },
];

export const withdrawHistoryData = {
  status: true,
  data: {
    items: [
      {
        id: 26,
        userId: 747,
        stripeConnectedAccountId: 'acct_1QzGFn00sh3FZEYZ',
        stripePayoutId: 'po_1R5oqn00sh3FZEYZckbCTkBM',
        status: 'processing',
        amount: '50.00',
        isManual: false,
        createdAt: '2025-03-23T13:46:06.949Z',
        updatedAt: '2025-03-23T13:46:06.949Z',
      },
      {
        id: 23,
        userId: 747,
        stripeConnectedAccountId: 'acct_1QzGFn00sh3FZEYZ',
        stripePayoutId: 'po_1R4iOI00sh3FZEYZo2RfA8eP',
        status: 'processing',
        amount: '100.00',
        isManual: false,
        createdAt: '2025-03-20T12:40:07.003Z',
        updatedAt: '2025-03-20T12:40:07.003Z',
      },
      {
        id: 4,
        userId: 747,
        stripeConnectedAccountId: 'acct_1QzGFn00sh3FZEYZ',
        stripePayoutId: 'po_1Qzzef00sh3FZEYZ16lVtqUY',
        status: 'processing',
        amount: '10.00',
        isManual: false,
        createdAt: '2025-03-07T12:05:30.520Z',
        updatedAt: '2025-03-07T12:05:30.520Z',
      },
      {
        id: 3,
        userId: 747,
        stripeConnectedAccountId: 'acct_1QzGFn00sh3FZEYZ',
        stripePayoutId: 'po_1QzzVG00sh3FZEYZXod4eDYP',
        status: 'processing',
        amount: '10.00',
        isManual: false,
        createdAt: '2025-03-07T11:55:47.512Z',
        updatedAt: '2025-03-07T11:55:47.512Z',
      },
    ],
    pagination: {
      totalCount: 4,
      pageSize: 10,
      totalPage: 1,
      currentPage: '1',
      isMore: false,
    },
  },
};

//Notification Data
export const NotificationData = [
  {
    id: '1',
    image: Images.usaPng,
    description:
      'Lorem ipsum dolor sit amet consectetur. Habitasse commodo pharetra tellus ultrices cras diam.',
    time: 'Last Wednesday at 9:42 AM',
  },
  {
    id: '2',
    image: Images.addNameImg,
    description:
      'Lorem ipsum dolor sit amet consectetur. Habitasse commodo pharetra tellus ultrices cras diam.',
    time: 'Last Wednesday at 9:42 AM',
  },
  {
    id: '3',
    image: Images.Job,
    description:
      'Lorem ipsum dolor sit amet consectetur. Habitasse commodo pharetra tellus ultrices cras diam.',
    time: 'Last Wednesday at 9:42 AM',
  },
];
export const suggestions = [
  {
    id: '1',
    title: 'Captain',
  },
  {
    id: '2',
    title: 'mate',
  },
  {
    id: '3',
    title: 'stew',
  },
  {
    id: '4',
    title: 'mechanic',
  },
];
export const images = [
  {
    id: '1',
    images: Images.usaPng,
  },
  {
    id: '2',
    images: Images.usaPng,
  },
  {
    id: '1',
    images: Images.usaPng,
  },
];

export const fileTypes = {
  'image/jpeg': 'JPEG',
  'image/png': 'PNG',
  'image/heic': 'HEIC',
  'image/heif': 'HEIF',
  'application/pdf': 'PDF',
  'text/plain': 'TXT',
  'application/msword': 'DOC',
  'application/vnd.ms-excel': 'XLS',
  'application/vnd.ms-powerpoint': 'PPT',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
    'DOCX',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation':
    'PPTX',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'XLSX',
};

export const menuItems = [
  {label: 'startDateTime', name: translate('startDate', '')},
  {label: 'Date listed', name: translate('dateListed', '')},
  {label: 'Level', name: translate('level', '')},
  {label: 'Pay', name: translate('pay', '')},
];
export const employarCardData = [
  {
    id: '1',
    jobTitle: 'Software Engineer',
    userName: 'Tony Stark',
    rating: 4.6,
    reviews: 5548,
    skills: ['Software Develepoment', 'Software Develepoment', 'skill 3'],
    company: 'Company A',
    location: 'London, UK',
    dateRange: 'May 23, 2024 - July 23, 2024',
    posted: '3 weeks ago',
    salary: '$200/hr',
    skill1: 'Skill 1',
    skill2: 'Skill 2',
    skill3: 'Skill 3',
  },
  // {
  //   id: '1',
  //   jobTitle: 'Software Engineer',
  //   userName: 'Tony Stark',
  //   rating: 4.6,
  //   reviews: 5548,
  //   skills: ['skill 1', 'skill 2', 'skill 3'],
  //   company: 'Company A',
  //   location: 'London, UK',
  //   dateRange: 'May 23, 2024 - July 23, 2024',
  //   posted: '3 weeks ago',
  //   salary: '$200/hr',
  // },
  // {
  //   id: '1',
  //   jobTitle: 'Software Engineer',
  //   userName: 'Tony Stark',
  //   rating: 4.6,
  //   reviews: 5548,
  //   skills: ['skill 1', 'skill 2', 'skill 3'],
  //   company: 'Company A',
  //   location: 'London, UK',
  //   dateRange: 'May 23, 2024 - July 23, 2024',
  //   posted: '3 weeks ago',
  //   salary: '$200/hr',
  // },
];

export const seekerCardData = [
  {
    id: '1',
    jobTitle: 'Software Engineer',
    userName: 'Tony Stark',
    rating: 4.6,
    reviews: 5548,
    skills: ['software develepoment', 'communication', 'skill 3'],
    company: 'Company A',
    location: 'London, Uk',
    dateRange: 'May 23, 2024 - July 23, 2024',
    posted: '3 weeks ago',
    salary: '$200/hr',
    skill1: 'Skill 1',
    skill2: 'Skill 2',
    skill3: 'Skill 3',
    level: 'Level 3',
    Pro: true,
    description:
      'Lorem ipsum dolor sit amet consectetur. Eget lectus enim enim mi quis urna arcu enim nulla. Lorem ipsum dolor sit amet consectetur.',
  },
];
export const seekerDetailCardData = {
  id: '1',
  jobTitle: 'Software Engineer',
  firstName: 'Tony',
  lastName: 'Stark',
  rating: 4.6,
  reviews: 5548,
  skills: [{name: 'shipBuilding'}],
  company: 'Company A',
  location: 'London, Uk',
  dateRange: 'May 23, 2024 - July 23, 2024',
  posted: '3 weeks ago',
  salary: '$200/hr',
  skill1: 'Skill 1',
  skill2: 'Skill 2',
  skill3: 'Skill 3',
  level: 'Level 3',
  Pro: true,
  about:
    'Lorem ipsum dolor sit amet consectetur. Eget lectus enim enim mi quis urna arcu enim nulla. Lorem ipsum dolor sit amet consectetur.',
};

export const imageData = [
  {
    id: '1',
    url: Images.usaPng,
  },
  {
    id: '2',
    url: Images.usaPng,
  },
  {
    id: '2',
    url: Images.usaPng,
  },
];

export const payments = [
  {
    id: 1,
    title: 'connectBank',
    icon: 'payoutInfo',
    screen: 'Wallet',
    type: '',
  },
  {
    id: 2,
    title: 'payoutHistory',
    screen: 'AllHistory',
    icon: 'payout',
    type: '',
  },
  // {
  //   id: 3,
  //   title: 'paymentPreferences',
  //   screen: '',
  //   icon: 'payoutInfo',
  //   type: '',
  // },
];

export const otherSettings = [
  // {
  //   id: '4',
  //   title: 'tutorials',
  //   icon: 'Privacy',
  //   screen: 'VideoTutorial',
  //   type: 'VideoTutorial',
  // },
  {
    id: '4',
    title: 'faqs',
    icon: 'Privacy',
    screen: 'FAQs',
    type: 'FAQs',
  },
  // {
  //   id: '7',
  //   title: 'Harbor History',
  //   icon: 'briefcase',
  //   screen: 'HarborHistory',
  //   type: 'HarborHistory',
  // },
  // {
  //   id: '6',
  //   title: 'Pay Overview',
  //   icon: 'PrivacyPolicy',
  //   screen: 'PayOverView',
  //   type: 'PayOverView',
  // },
  {
    id: '2',
    title: 'Harbor Policies',
    icon: 'PrivacyPolicy',
    screen: 'HarborPolicy',
    type: 'HarborPolicy',
  },
  // {
  //   id: '3',
  //   title: 'privacyPolicy',
  //   icon: 'PrivacyPolicy',
  //   screen: 'WebViewScreen',
  //   type: 'PrivacyPolicy',
  // },

  {
    id: '3',
    title: 'changeLanguage',
    icon: 'flag',
    isPopup: 'language',
  },
  // Client removed
  // {
  //   id: '1',
  //   title: 'aboutApp',
  //   icon: 'AboutApp',
  //   screen: 'AboutScreen',
  //   type: 'AboutScreen',
  // },

  // {
  //   id: '5',
  //   title: 'logout',
  //   icon: 'Logout',
  //   type: 'logout',
  //   screen: undefined,
  // },
  // {
  //   id: '7',
  //   title: 'deleteAccount',
  //   icon: 'trash',
  //   isConfirmation: true,
  //   type: 'delete',
  // },
];
