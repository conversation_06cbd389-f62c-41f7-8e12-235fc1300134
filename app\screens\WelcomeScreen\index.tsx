import React, { useState } from 'react';
import {
  Dimensions,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Button from '../../components/UI/Button';
import { Images } from '../../config/images';
import { translate } from '@language/Translate';
import { BaseColors } from '../../config/theme';
import styles from './styles';
import AuthActions from '@redux/reducers/auth/actions';
import FastImage from 'react-native-fast-image';
import { useAppDispatch, useRedux } from '@components/UseRedux';

interface Props {
  navigation: any;
  route: any;
}

export default function WelcomeScreen({ navigation, route }: Props) {
  const name = route?.params?.name;
  const dispatch = useAppDispatch();
  const { useAppSelector } = useRedux();
  const { setUserProfileData } = AuthActions;
  const { userProfileData } = useAppSelector((state: any) => state.auth); // Use your RootState type

  // Local state for managing selected card
  const [selectedCard, setSelectedCard] = useState(
    userProfileData?.userRole || null,
  );

  const handleCardSelect = (position: 'seeker' | 'employer') => {
    setSelectedCard(position); // Update local state
    dispatch(setUserProfileData({ ...userProfileData, userRole: position }));
  };

  return (
    <View style={styles.container}>
      {/* <StatusBar barStyle="dark-content" backgroundColor={BaseColors.white} /> */}
      <KeyboardAwareScrollView
        bounces={false}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={false}>
        <View style={styles.innerContainer}>
          <View style={styles.mainImgView}>
            <View style={styles.imgView}>
              {/* Load SVG from local asset */}
              {/* <SvgUri width="170" height="170" source={Images.logo} /> */}
              <FastImage
                source={Images.harborLogo}
                resizeMode={FastImage.resizeMode.contain} // Ensure proper scaling
                style={{ width: '100%', height: '100%' }} // Match the image's natural dimensions
              />
            </View>
          </View>
          <View style={styles.subViewTxt}>
            <Text style={styles.txtStye}>
              {translate('Welcome', '')} {userProfileData?.firstName || name}
            </Text>
          </View>

          {/* Seeker Card */}
          <TouchableOpacity
            activeOpacity={1}
            style={[
              styles.cardViewSty,
              {
                marginTop: Dimensions.get('screen').height / 22,
                borderColor:
                  selectedCard === 'seeker'
                    ? BaseColors.primary
                    : BaseColors.textGrey,
                backgroundColor:
                  selectedCard === 'seeker'
                    ? BaseColors.primary
                    : BaseColors.textGrey,
              }, // Highlight selected card
            ]}
            onPress={() => handleCardSelect('seeker')}>
            <View
              style={[
                styles.insideView,
                {
                  borderColor:
                    selectedCard === 'seeker'
                      ? BaseColors.primary
                      : BaseColors.textGrey,
                },
              ]}>
              <View style={styles.mainImagecontainer}>
                <View style={styles.imageView}>
                  <FastImage
                    source={
                      selectedCard === 'seeker' ? Images.Jobfill : Images.Job
                    }
                    resizeMode="contain"
                    style={{ width: '100%', height: '100%' }}
                  />
                </View>
              </View>
              <Text
                style={[
                  styles.sekerTxtSty,
                  {
                    color:
                      selectedCard === 'seeker'
                        ? BaseColors.primary
                        : BaseColors.textGrey,
                  },
                ]}>
                {translate('Seeker', '')}
              </Text>
            </View>
            <View
              style={[
                styles.bottomView,
                {
                  backgroundColor:
                    selectedCard === 'seeker'
                      ? BaseColors.primary
                      : BaseColors.textGrey,
                  borderColor:
                    selectedCard === 'seeker'
                      ? BaseColors.primary
                      : BaseColors.textGrey,
                },
              ]}>
              <Text style={styles.jobTxtty}>{translate('lookingJob', '')}</Text>
            </View>
          </TouchableOpacity>

          {/* Employer Card */}
          <TouchableOpacity
            activeOpacity={1}
            style={[
              styles.cardViewSty,
              {
                borderColor:
                  selectedCard === 'employer'
                    ? BaseColors.primary
                    : BaseColors.textGrey,
                backgroundColor:
                  selectedCard === 'employer'
                    ? BaseColors.primary
                    : BaseColors.textGrey,
              }, // Highlight selected card
            ]}
            onPress={() => handleCardSelect('employer')}>
            <View
              style={[
                styles.insideView,
                {
                  borderColor:
                    selectedCard === 'employer'
                      ? BaseColors.primary
                      : BaseColors.textGrey,
                },
              ]}>
              <View style={styles.mainImagecontainer}>
                <View
                  style={[
                    styles.imageView,
                    { width: Dimensions.get('screen').width / 3.3 },
                  ]}>
                  <FastImage
                    source={
                      selectedCard === 'employer'
                        ? Images.Seekerfill
                        : Images.Seeker
                    }
                    resizeMode="contain"
                    style={{ width: '100%', height: '100%' }}
                  />
                </View>
              </View>
              <Text
                style={[
                  styles.sekerTxtSty,
                  {
                    color:
                      selectedCard === 'employer'
                        ? BaseColors.primary
                        : BaseColors.textGrey,
                  },
                ]}>
                {translate('Employer', '')}
              </Text>
            </View>
            <View
              style={[
                styles.bottomView,
                {
                  backgroundColor:
                    selectedCard === 'employer'
                      ? BaseColors.primary
                      : BaseColors.textGrey,
                  borderColor:
                    selectedCard === 'employer'
                      ? BaseColors.primary
                      : BaseColors.textGrey,
                },
              ]}>
              <Text style={styles.jobTxtty}>
                {translate('lookinghire', '')}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </KeyboardAwareScrollView>

      <View style={styles.btnViewSty}>
        <Button
          type="text"
          disablestyle={{
            backgroundColor:
              userProfileData?.userRole === ''
                ? BaseColors.disableColor
                : BaseColors.primary,
          }}
          disable={selectedCard === '' ? true : false} // Disable button until a card is selected
          onPress={() => {
            // Navigate to the next screen
            if (selectedCard) {
              navigation.navigate('ProfileSetUp', {
                redirection: 'BottomtabsNavigator',
              });
            }
          }}>
          {translate('Continue', '')}
        </Button>
      </View>
      <TouchableOpacity
        activeOpacity={0.8}
        style={styles.laterTxtView}
        onPress={() => {
          navigation.navigate('BottomTabsNavigator');
        }}>
        <Text style={styles.laterTxtSty}>{translate('later', '')}</Text>
      </TouchableOpacity>
    </View>
  );
}
