import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useState} from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import styles from './styles';
import {ActivityIndicator, Text, TouchableOpacity, View} from 'react-native';
import Header from '@components/Header';
import {BaseColors} from '@config/theme';
import AnimatedView from '@components/AnimatedView';
import {FontFamily} from '@config/typography';
import BaseSetting from '@config/setting';
import {getApiData} from '@app/utils/apiHelper';
import {formatDate} from '@app/utils/CommonFunction';
import {translate} from '@language/Translate';
import TextInput from '@components/UI/TextInput';
import Button from '@components/UI/Button';
import {useRedux} from '@components/UseRedux';
import {isEmpty} from 'lodash-es';
import Toast from 'react-native-simple-toast';
import {store} from '@redux/store/configureStore';
import {useDispatch, useSelector} from 'react-redux';
import authActions from '@redux/reducers/auth/actions';
import {RefreshControl} from 'react-native';

export default function Wallet({navigation, route}: any) {
  const dispatch = useDispatch();
  const [refreshing, setRefreshing] = useState(false);
  const {params} = route;
  const {useAppSelector} = useRedux();
  const {userData} = useAppSelector((state: any) => state.auth);


  const [birthdate, setBirthdate] = useState(undefined);
  const [ssnNumber, setSsnNumber] = useState('');
  // const { type, id, searchType } = params;
  // State Variables
  const [pageLoader, setPageLoader] = useState(true);
  // const [walletAmount, setWallet Amount] = useState(0);
  const [transactions, setTransactions] = useState<any>({
    data: [],
    pagination: {currentPage: 1, isMore: null},
  });

  const {data, pagination} = transactions;
  const [loader, setLoader] = useState(false);
  const [ssnLoader, setSsnLoader] = useState(false);
  // const [walletData, setWalletData1] = useState({});
  const [checkStatus, setCheckStatus] = useState(false);
  const [updateUrl, setUpdateUrl] = useState(false);
  const [birthdateErr, setBirthDateErr] = useState({
    err: false,
    txt: '',
  });
  const [ssnErr, setSsnErr] = useState({
    err: false,
    txt: '',
  });

  const tenYearsAgo = new Date();
  tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 15);
  const userId = userData?.id;

  const getUserDetails = useCallback(async () => {
    // setRefreshing(true); // Start refreshing state

    try {
      const resp = await getApiData({
        endpoint: `${BaseSetting.endpoints.usersList}`,
        method: 'GET',
        data: {userId, page: 1},
      });
      console.log('🚀 ~ getUserDetails ~ resp:', resp);
      if (resp?.data && resp?.status && resp?.data?.id) {
        dispatch(authActions.setUserData(resp?.data) as any);
        setRefreshing(false); // Stop refreshing
      } else {
        setRefreshing(false); // Stop refreshing

        // Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
      setRefreshing(false); // Stop refreshing

    } catch (error) {
      setRefreshing(false); // Stop refreshing

      console.error('Error fetching list:', error);
      // Toast.show('Failed to fetch data.', Toast.SHORT);
    }
  }, [userId, userData]);

  async function getTransactions() {
    try {
      setLoader(true);
      const endpoint = BaseSetting.endpoints.transactionHistory;
      const response = await getApiData({
        endpoint,
        method: 'GET',
        data: {
          page: 1,
          limit: 5,
        },
      });
      console.log(
        '🚀 ~ transactionHistory ~ response:',
        JSON.stringify(response),
      );
      if (response?.status) {
        setTransactions({data: response?.data?.items || []});
      }
      setLoader(false);
    } catch (er) {
      setLoader(false);
    }
  }

  const validateAndProceed = async (type?: string) => {
    let isValid = true;
    if (isEmpty(ssnNumber)) {
      setSsnErr({err: true, txt: translate('SsnNumberReurired', '')});
      isValid = false;
    } else {
      setSsnErr({err: false, txt: ''});
    }
    if (!birthdate) {
      setBirthDateErr({err: true, txt: translate('birthdateRequired', '')});
      isValid = false;
    } else {
      setBirthDateErr({err: false, txt: ''});
    }
    if (isValid) {
      handlePost();
    }
  };

  const handlePost = async () => {
    setSsnLoader(true);
    const formattedStartDate = birthdate ? formatDate(birthdate) : '';
    // Build the newObj with a dynamic description field
    const newObj: any = {
      dob: formattedStartDate || '',
      ssn_last_4: ssnNumber || '',
    };
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.updateKyc,
        method: 'POST',
        data: newObj,
      });
      if (res?.status === true) {
        setUpdateUrl(res?.data);
        setCheckStatus(res?.status);
      } else {
        setSsnLoader(false);
        Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      }

      setSsnLoader(false);
    } catch (err) {
      setSsnLoader(false);
      Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      getWalletData();
      getTransactions();
    }, []),
  );

  useFocusEffect(
    React.useCallback(() => {
      getUserDetails();
    }, []),
  );

  async function getWalletData() {
    try {
      setPageLoader(true);
      const endpoint = BaseSetting.endpoints.checkKyc;
      const response = await getApiData({
        endpoint,
        method: 'GET',
      });
      console.log('🚀 ~ getWalletData ~ response:', response);
      if (response?.status) {
        setPageLoader(false);
        setCheckStatus(response?.status);
        setUpdateUrl(response?.data);
      } else {
        setPageLoader(false);
      }
    } catch (er) {
      setPageLoader(false);
    }
  }
  return (
    <KeyboardAwareScrollView
      bounces={false}
      contentContainerStyle={styles.scrollContainer}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
      enableOnAndroid={false}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={getUserDetails} />
      }>
      <Header
        leftIcon="back-arrow"
        title={'Connect Bank Details'}
        onLeftPress={() => {
          navigation.goBack();
        }}
      />

      <View style={{...styles.container, paddingHorizontal: 15}}>
        {pageLoader ? (
          <View style={styles.centerMain}>
            <ActivityIndicator color={BaseColors.primary} size={'large'} />
          </View>
        ) : !checkStatus ? (
          <>
            <TextInput
              Date={true}
              selectedDate={birthdate}
              onDateChange={(date: any) => {
                setBirthdate(date); // Set the start date when changed
              }}
              placeholderText={translate('Date of Birth', '')}
              title={translate('Date of Birth', '')}
              maxDate={tenYearsAgo} // Set minDate as 10 days ago
              datetimemodal="Start Date"
              showError={birthdateErr?.err}
              errorText={birthdateErr?.txt}
            />
            <TextInput
              value={ssnNumber}
              onChange={(value: any) => {
                setSsnNumber(value);
              }}
              mandatory={true}
              title={translate('SsnNumber', '')}
              keyBoardType="Number"
              placeholderText={translate('SsnNumber', '')}
              maxLength={4}
              showError={ssnErr?.err}
              errorText={ssnErr?.txt}
            />
            <View style={{marginTop: 20}}>
              <Button
                loading={ssnLoader}
                onPress={validateAndProceed}
                type="text">
                {translate('Submit', '')}
              </Button>
            </View>
          </>
        ) : (
          <AnimatedView>
            {userData?.bankAccountVerified === 'notVerified' ? (
              <View style={styles.thirdRow}>
                <Text style={styles.warningText}>
                  {
                    'Your bank account setup is incomplete. Please add your bank details to enable transactions.'
                  }
                </Text>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() =>
                    navigation.navigate('KycCompleteScreen', {
                      uri: updateUrl?.url,
                    })
                  }>
                  <View style={styles.completeNowBtnStyle}>
                    <Text
                      style={[
                        styles.warningText,
                        {
                          fontSize: 18,
                          fontFamily: FontFamily.OpenSansBold,
                          marginTop: 4,
                        },
                      ]}>
                      Complete Setup
                    </Text>
                    <View style={styles.underline} />
                  </View>
                </TouchableOpacity>
              </View>
            ) : userData?.bankAccountVerified === 'pending' ? (
              <View style={styles.thirdRow}>
                <Text style={styles.warningText}>
                  {'Your Bank account verification is pending.'}
                </Text>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() =>
                    navigation.navigate('KycCompleteScreen', {
                      uri: updateUrl?.url || '',
                    })
                  }>
                  <View style={styles.completeNowBtnStyle}>
                    <Text style={[styles.warningText]}>Update Now</Text>
                    <View style={styles.underline} />
                  </View>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.thirdRow}>
                <Text style={styles.warningText}>
                  {
                    'Your Bank account has been verified, but you can update it if necessary.'
                  }
                </Text>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() =>
                    navigation.navigate('KycCompleteScreen', {
                      uri: updateUrl?.url || '',
                    })
                  }>
                  <View style={styles.completeNowBtnStyle}>
                    <Text style={[styles.warningText]}>Update Now</Text>
                    <View style={styles.underline} />
                  </View>
                </TouchableOpacity>
              </View>
            )}

            {/* <View style={{ ...styles.row, marginBottom: 5 }} >
              <Text style={[styles.recent]}>{translate('recent')}</Text>
              <TouchableOpacity onPress={() => {
                navigation.navigate('AllHistory');
              }}>
                <Text style={[styles.seeAll]}>{translate('seeAll')}</Text>
              </TouchableOpacity>
            </View>
            {loader ? (
              <View style={styles.centerMain}>
                <ActivityIndicator color={BaseColors.primary} />
              </View>
            ) : (
              <FlatList
                data={transactions?.data || []}
                keyExtractor={(item: any) => `${item.id}+1`}
                renderItem={({ item }) => (<TransactionHistory item={item} navigation={navigation} />)}
                ListEmptyComponent={
                  <View style={[styles.centerMain]}>
                    <NoRecord
                      title={'noJobs'}
                      description={'noJobsDesc'}
                      iconName="employer"
                    />
                  </View>
                }
                ItemSeparatorComponent={() => (<View style={styles.separator} />)}
                scrollEnabled={false} // Disable FlatList's scrolling
              />
            )} */}
          </AnimatedView>
        )}
      </View>
    </KeyboardAwareScrollView>
  );
}
