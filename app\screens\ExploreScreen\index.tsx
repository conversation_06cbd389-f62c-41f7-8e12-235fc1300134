import React from 'react';
import { View, Text, StatusBar } from 'react-native';
import styles from './styles';
import { translate } from '@language/Translate';
import { useTheme } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import Header from '@components/Header';
export default function ExploreScreen({ navigation }: any) {
  const { darkmode } = useSelector((state: any) => {
    return state.auth;
  });
  const colors = useTheme();
  const BaseColors: any = colors.colors;
  return (
    <>
      <Header
        leftIcon="back-arrow"
        title={translate('', '')}
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <View
        style={{
          ...styles.container,
          backgroundColor: BaseColors.backgroundColor,
        }}>
        {/*  <StatusBar barStyle={'dark-content'} /> */}

        <View style={styles.nosearchView}>
          <Text style={{ ...styles.noFound }}>{translate('comingSoon', '')}</Text>
        </View>
      </View>
    </>
  );
}
