import types from './actions';

const initialState: any = {
  filterData: {},
  harborFilterData: {},
  sort: { label: '', type: '' },
  updateJobData: false,
  isPaymentProcessing: false,
};

export default function reducer(state = initialState, action: any) {
  switch (action.type) {
  case types.SET_FILTER_DATA:
    return {
      ...state,
      filterData: action.filterData,
    };
  case types.SET_HARBOR_FILTER_DATA:
    return {
      ...state,
      harborFilterData: action.harborFilterData,
    };
  case types.SET_SORT:
    return {
      ...state,
      sort: action.sort,
    };
  case types.SET_UPDATE_DATA:
    return {
      ...state,
      updateJobData: action.updateJobData,
    };
  case types.SET_PAYMENT_PROCESSING:
    return {
      ...state,
      isPaymentProcessing: action.isProcessing,
    };
  case types.CLEAR_DATA:
    return {
      ...state,
      filterData: {},
    };
  default:
    return state;
  }
}
