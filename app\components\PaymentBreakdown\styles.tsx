import {Dimensions, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';
const IOS = Platform.OS === 'ios';

const {width, height} = Dimensions.get('window');
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  jobCostText: {
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 18,
  },
  paymentRequiredText: {
    color: BaseColors.lightTxtColor,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
    paddingTop: IOS ? 5 : 0,
  },
  totalSalaryText: {
    color: BaseColors.lightTxtColor,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
    // paddingTop: 10,
  },
  salaryAmountText: {
    color: BaseColors.lightTxtColor,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
  },
  totalsalaryAmountText: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
  },
  serviceChargeText: {
    color: BaseColors.lightTxtColor,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
  },
  jobCostContainer: {
    marginTop: 15,
    paddingTop: IOS ? 15 : 5,
    backgroundColor: '#ffff',
  },
  rowSpaceBetween: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    // paddingVertical: 10,
  },
  bottomBorder: {
    borderBottomWidth: 1,
    borderColor: '#e1e1e1',
    width: '100%',
    paddingTop: 15,
  },
  estimatedChargesText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.primary,
    paddingBottom: 5,
  },
});
