# HARBOR APP

## Description
🌊 Why Harbor?

✅ Verified Talent: All job seekers and employers are thoroughly vetted, ensuring trust and professionalism.  
✅ Enhanced Opportunities: Gain access to a broad network of marine jobs and top-tier professionals.  
✅ Secure Transactions: Payments are processed securely, giving you peace of mind.  
✅ Gamification & Rewards: Earn rewards and level up by completing jobs and engaging with the platform.  
✅ Last-Minute Booking: Need to fill a position fast or find work right away? Harbor’s got you covered.  

🚀 How It Works:  
Create a Profile: Sign up and get verified to ensure trust and credibility.  
Find Jobs or Talent: Browse and apply for jobs or connect with skilled professionals.  
Secure Payments: Get paid securely and on time.  
Get Rewarded: Earn points and unlock perks by completing jobs and building your profile.  
Stop searching. Start connecting.  

## Table of Contents

- [Installation](#installation)
- [Usage](#usage)
- [Package Details](#package-details)
- [Features](#features)
- [License](#license)

## Installation

1. Clone the repository:
   ```bash
   git clone https://<EMAIL>/HarborApp/Harbor/_git/harborApp
   ```
2. Navigate to the project directory:
   ```bash
   cd harborApp
   ```
3. Install the required dependencies:
   ```bash
   npm install
   ```
   or
   ```bash
   yarn install
   ```

4. For iOS, navigate to the `ios` directory and install the pods:
   ```bash
   cd ios
   pod install
   cd ..
   ```


## Usage

To run the project, use the following commands:

- For iOS:
  ```bash
  npx react-native run-ios
  ```

- For Android:
  ```bash
  npx react-native run-android
  ```

## Node and Gradle Version

- **Node Version**: Above v18.16.0
- **Gradle Version**: 8.7


## Package Details
<details>
<summary>Dependencies</summary>

- **@invertase/react-native-apple-authentication**: ^2.4.0
  - Usage: Provides Apple authentication for React Native apps.
- **@react-native-async-storage/async-storage**: ^2.0.0
  - Usage: Simple, unencrypted, asynchronous, persistent storage for React Native.
- **@react-native-clipboard/clipboard**: ^1.14.2
  - Usage: Access the clipboard for copying and pasting text.
- **@react-native-community/blur**: ^4.4.1
  - Usage: Adds a blur effect to views in React Native.
- **@react-native-community/datetimepicker**: ^8.2.0
  - Usage: A cross-platform DateTime picker for React Native.
- **@react-native-community/netinfo**: ^11.4.1
  - Usage: Get information about the device's network state.
- **@react-native-community/push-notification-ios**: ^1.11.0
  - Usage: Handles push notifications for iOS.
- **@react-native-firebase/app**: 21.6.1
  - Usage: Initializes Firebase in your React Native app.
- **@react-native-firebase/auth**: 21.6.1
  - Usage: Provides authentication services using Firebase.
- **@react-native-firebase/messaging**: ^21.6.1
  - Usage: Handles messaging and notifications with Firebase.
- **@react-native-google-signin/google-signin**: ^13.1.0
  - Usage: Google Sign-In for React Native apps.
- **@react-navigation/bottom-tabs**: ^6.6.1
  - Usage: Bottom tab navigation for React Navigation.
- **@react-navigation/native**: ^6.1.18
  - Usage: Core components for React Navigation.
- **@react-navigation/stack**: ^6.4.1
  - Usage: Stack navigation for React Navigation.
- **@sentry/react-native**: ^5.35.0
  - Usage: Error tracking and performance monitoring for React Native.
- **@stripe/stripe-react-native**: ^0.40.0
  - Usage: Stripe integration for payments in React Native.
- **@twotalltotems/react-native-otp-input**: ^1.3.11
  - Usage: OTP input component for React Native.
- **@types/i18n-js**: ^3.8.9
  - Usage: Type definitions for i18n-js.
- **@types/lodash-es**: ^4.17.12
  - Usage: Type definitions for lodash-es.
- **axios**: ^1.7.7
  - Usage: Promise-based HTTP client for the browser and Node.js.
- **dayjs**: ^1.11.13
  - Usage: A lightweight date library for parsing, validating, manipulating, and formatting dates.
- **i18n-js**: 3.9.2
  - Usage: Internationalization for JavaScript.
- **libphonenumber-js**: ^1.11.14
  - Usage: Phone number validation and formatting.
- **lodash-es**: ^4.17.21
  - Usage: A modern JavaScript utility library delivering modularity, performance, and extras.
- **lottie-react-native**: ^7.0.0
  - Usage: Render animations created with Adobe After Effects in React Native.
- **moment**: ^2.30.1
  - Usage: Parse, validate, manipulate, and display dates and times.
- **react**: 18.3.1
  - Usage: A JavaScript library for building user interfaces.
- **react-content-loader**: ^7.0.2
  - Usage: SVG placeholder loading animations for React.
- **react-hook-form**: ^7.53.2
  - Usage: Performant, flexible, and extensible forms with easy-to-use validation.
- **react-native**: 0.75.4
  - Usage: A framework for building native apps using React.
- **react-native-actionsheet**: ^2.4.2
  - Usage: Action sheet component for React Native.
- **react-native-awesome-gallery**: ^0.4.3
  - Usage: A gallery component for displaying images.
- **react-native-confetti-cannon**: ^1.5.2
  - Usage: A confetti cannon animation for React Native.
- **react-native-country-picker-modal**: ^2.0.0
  - Usage: Country picker modal for selecting countries.
- **react-native-device-info**: ^14.0.4
  - Usage: Get device information in React Native.
- **react-native-document-picker**: ^9.3.1
  - Usage: Document picker for selecting files.
- **react-native-dotenv**: ^3.4.11
  - Usage: Load environment variables from a .env file.
- **react-native-element-dropdown**: ^2.12.2
  - Usage: Dropdown component for React Native.
- **react-native-event-listeners**: ^1.0.7
  - Usage: Event listener management for React Native.
- **react-native-fast-image**: ^8.6.3
  - Usage: Fast image component for React Native.
- **react-native-file-viewer**: ^2.1.5
  - Usage: View files in React Native.
- **react-native-fs**: ^2.20.0
  - Usage: File system access for React Native.
- **react-native-geolocation-service**: ^5.3.1
  - Usage: Geolocation services for React Native.
- **react-native-gesture-handler**: ^2.20.1
  - Usage: Gesture handling for React Native.
- **react-native-get-random-values**: ^1.11.0
  - Usage: Get random values in React Native.
- **react-native-google-places-autocomplete**: ^2.5.7
  - Usage: Google Places autocomplete input for React Native.
- **react-native-image-crop-picker**: ^0.42.0
  - Usage: Image cropping and picking for React Native.
- **react-native-keyboard-aware-scroll-view**: ^0.9.5
  - Usage: Scroll view that automatically adjusts when the keyboard appears.
- **react-native-linear-gradient**: ^2.8.3
  - Usage: A linear gradient component for React Native.
- **react-native-material-menu**: ^2.0.0
  - Usage: Material design menu for React Native.
- **react-native-modal-datetime-picker**: ^18.0.0
  - Usage: Modal date and time picker for React Native.
- **react-native-permissions**: ^5.2.1
  - Usage: Manage permissions in React Native.
- **react-native-persona**: ^2.9.3
  - Usage: User authentication and identity verification.
- **react-native-progress**: ^5.0.1
  - Usage: Progress indicators for React Native.
- **react-native-push-notification**: ^8.1.1
  - Usage: Local and remote notifications for React Native.
- **react-native-ratings**: ^8.1.0
  - Usage: Rating component for React Native.
- **react-native-raw-bottom-sheet**: ^3.0.0
  - Usage: Raw bottom sheet component for React Native.
- **react-native-reanimated**: ^3.15.4
  - Usage: Advanced animations for React Native.
- **react-native-safe-area-context**: ^4.11.0
  - Usage: Safe area context for React Native.
- **react-native-screens**: ^3.34.0
  - Usage: Native screens for React Navigation.
- **react-native-simple-toast**: ^3.3.1
  - Usage: Simple toast notifications for React Native.
- **react-native-svg**: ^15.11.2
  - Usage: SVG support for React Native.
- **react-native-swipeable-list**: ^0.1.2
  - Usage: Swipeable list component for React Native.
- **react-native-swiper**: ^1.6.0
  - Usage: Swiper component for React Native.
- **react-native-switch**: ^1.5.1
  - Usage: Switch component for React Native.
- **react-native-vector-icons**: ^10.2.0
  - Usage: Customizable icons for React Native.
- **react-native-video**: ^6.7.0
  - Usage: Video player for React Native.
- **react-native-webview**: ^13.12.5
  - Usage: WebView component for React Native.
- **react-redux**: ^9.1.2
  - Usage: Official React bindings for Redux.
- **redux**: ^5.0.1
  - Usage: A predictable state container for JavaScript apps.
- **redux-persist**: ^6.0.0
  - Usage: Persist and rehydrate Redux state.
- **redux-thunk**: ^3.1.0
  - Usage: Middleware for handling asynchronous actions in Redux.
- **rn-range-slider**: ^2.2.2
  - Usage: Range slider component for React Native.
- **socket.io-client**: ^4.8.1
  - Usage: Socket.IO client for real-time communication.
- **yarn**: ^1.22.22
  - Usage: Package manager for JavaScript.
- **yup**: ^1.4.0
  - Usage: Object schema validation for JavaScript.

</details>

## Job Flow Scenarios

<details>
<summary>Job Flow Testing Scenarios</summary>

1. **Custom Harbor with Both Offers Off**
   - **Accept**
     - Employer posts a job under Custom Harbor with both offers turned off.
     - Seeker applies for the job.
     - Employer reviews and accepts the application.
     - Verify that the job status updates to "Accepted".
   - **Reject**
     - Employer reviews the application and rejects it.
     - Verify that the seeker receives a rejection notification.

2. **Custom Offer Only Job**
   - **Accept & Payment from Employer Side with Payment Confirmation**
     - Employer creates a job with only a Custom Offer.
     - Seeker applies and the employer accepts the application.
     - Employer makes the payment.
     - Verify that the seeker receives the payment confirmation.
   - **Reject**
     - Employer reviews the seeker’s application and rejects it.
     - Verify that the seeker is notified about the rejection.
   - **Send Custom**
     - Employer sends a custom offer to the seeker.
     - Verify that the seeker receives and can view the offer.
   - **Counter**
     - Seeker counters the custom offer.
     - Verify that the employer can see and respond to the counteroffer.
   - **Approve & Pay**
     - Employer approves the counteroffer and processes the payment.
     - Verify that the payment confirmation is sent to the seeker.
   - **Decline Employer**
     - Employer declines the counteroffer.
     - Verify that the seeker is notified.
   - **Decline Seeker**
     - Seeker declines the employer’s custom offer.
     - Verify that the employer is notified.
   - **Counter History**
     - Verify that all counteroffer exchanges are recorded in the system.

3. **Custom Harbor Flat Rate Only Job**
   - **Accept & Payment from Employer Side with Payment Confirmation**
     - Employer posts a flat-rate job.
     - Seeker applies, employer accepts, and pays.
     - Verify that payment confirmation is sent.
   - **Reject**
     - Employer reviews the application and rejects it.
     - Verify that the seeker is notified.

4. **Custom Harbor with Custom Offer & Flat Rate Job**
   - **Edit - Done**
     - Employer edits the job details.
     - Verify that the changes are reflected.
   - **Accept & Payment from Employer Side with Payment Confirmation**
     - Employer accepts the seeker’s application and pays.
     - Verify that the seeker receives confirmation.
   - **Reject**
     - Employer rejects the application.
     - Verify that the seeker is notified.
   - **Send Custom**
     - Employer sends a custom offer.
     - Verify that the seeker can view it.
   - **Counter**
     - Seeker counters the offer.
     - Verify that the employer is notified.
   - **Approve by Seeker**
     - Seeker approves the offer.
     - Verify that the employer can proceed with payment.
   - **Approve by Employer**
     - Employer approves the offer.
     - Verify that the seeker is notified.
   - **Pay**
     - Employer completes the payment.
     - Verify that confirmation is sent to the seeker.
   - **Decline by Employer**
     - Employer declines the offer.
     - Verify that the seeker is notified.
   - **Decline by Seeker**
     - Seeker declines the employer’s offer.
     - Verify that the employer is notified.
   - **Counter History**
     - Verify that all counteroffer exchanges are logged.

5. **Simple Job Creation - Job 2**
   - **Job Create**
     - Employer creates a job.
     - Verify that the job is visible in the job listings.
   - **Apply**
     - Seeker applies for the job.
     - Verify that the application is visible to the employer.
   - **Decline**
     - Employer declines the application.
     - Verify that the seeker is notified.
   - **Approve**
     - Employer approves the application.
     - Verify that the seeker receives confirmation.
   - **Pay**
     - Employer makes the payment.
     - Verify that the seeker receives the payment confirmation.
   - **Payment Confirmation**
     - Verify that the system logs the payment and sends a confirmation.

6. **Custom Offer**
   - **Job Create**
     - Employer creates a job with a custom offer.
     - Verify that the job is visible in the listings.
   - **Multi Apply**
     - Multiple seekers apply for the job.
     - Verify that all applications are visible to the employer.
   - **Counter**
     - Employer sends a counteroffer to a seeker.
     - Verify that the seeker is notified and can respond.
   - **Approve**
     - Employer approves a seeker’s application.
     - Verify that the seeker receives confirmation.
   - **Denied**
     - Employer denies an application.
     - Verify that the seeker is notified.
   - **Pay**
     - Employer completes the payment process.
     - Verify that the seeker is notified.
   - **Counter History**
     - Verify that all counters are logged.
   - **Accept & Payment from Employer Side with Payment Confirmation**
     - Employer accepts the seeker’s offer and pays.
     - Verify that the seeker receives payment confirmation.
   - **Auto Declined for Other Job Seekers Applied**
     - Once a seeker is hired, verify that other applicants are automatically declined.
   - **Approve by Seeker**
     - Seeker approves the offer.
     - Verify that the employer is notified.
   - **Approve by Employer**
     - Employer approves the seeker’s counteroffer.
     - Verify that the seeker is notified.
   - **Decline Employer**
     - Employer declines the counteroffer.
     - Verify that the seeker is notified.
   - **Decline Seeker**
     - Seeker declines the employer’s custom offer.
     - Verify that the employer is notified.

</details>


## License

    Groovy web

## Contact Information
For any inquiries, please reach out to us at: [<EMAIL>](mailto:<EMAIL>)
