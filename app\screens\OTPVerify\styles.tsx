import {StyleSheet, Dimensions} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const {width, height} = Dimensions.get('window');

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
  },

  mainView: {
    flex: 1,
    justifyContent: 'center',
  },
  titleView: {
    alignItems: 'center',
    justifyContent: 'center',
    // borderWidth: 2,
  },
  titleTxt: {
    fontSize: 26,
    color: BaseColors.titleTxt,
    fontFamily: FontFamily.OpenSansBold,
    marginBottom: 30,
  },
  subtitleTxt: {
    fontSize: 16,
    color: BaseColors.subTitleTxt,
    fontFamily: FontFamily.OpenSansRegular,
    textAlign: 'center',
    marginBottom: 40,
  },
  OTPTxt: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.titleTxt,
    borderWidth: 0,
    borderBottomWidth: 2,
    borderBottomColor: BaseColors.subTitleTxt,
    borderRadius: 0,
    width: 40,
    height: 40,
  },

  actionsView: {
    marginTop: 30,
    paddingHorizontal: 50,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  timeView: {
    // borderWidth: 2,
  },
  timeTxt: {
    fontSize: 14,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansMedium,
  },
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resendIcon: {
    color: BaseColors.primary,
    fontSize: 16,
    marginRight: 8,
  },
  resendTxt: {
    fontSize: 14,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
  },

  otpContainer: {
    width: '80%',
    height: 60,
  },
  textInput: {
    width: 40,
    height: 40,
    borderWidth: 1,
    borderColor: '#000',
    borderRadius: 5,
    textAlign: 'center',
  },
  otpInputView: {
    width: Dimensions.get('screen').width * 0.8,
    height: 60,
  },
  codeInputFieldStyle: {
    width: 37,
    height: 37,
    borderWidth: 1,
    borderColor: '#e2e2e2',
    borderRadius: 5,
    textAlign: 'center',
    color: '#000',
  },
  codeInputHighlightStyle: {
    borderColor: BaseColors.primary,
  },
  mainImgView: {
    alignSelf: 'center',
    paddingTop: height * 0.16,
  },
  imgView: {
    alignItems: 'center',
    justifyContent: 'center',
    width: Dimensions.get('screen').width / 2.5,
    height: Dimensions.get('screen').height / 11,
  },
  titleViewSty: {
    paddingTop: height * 0.08,
  },
  titleTxtSty: {
    textAlign: 'center',
    fontSize: Dimensions.get('screen').width * 0.077,
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansSemiBold,
    textTransform:"capitalize"
  },
  changePhnSty: {
    textAlign: 'center',
    fontSize: 16,
    color: BaseColors.inputColor,
    paddingTop: 10,
    fontFamily: FontFamily.OpenSansRegular,
    textTransform:"capitalize"

  },
  editTxtSty: {
    textAlign: 'center',
    fontSize: 16,
    color: BaseColors.primary,
    paddingTop: 10,
    fontFamily: FontFamily.OpenSansRegular,
  },
  otpBoxSty: {
    paddingHorizontal: width * 0.08, // 30 based on screen width
    marginTop: height * 0.048, // 30 based on screen height
    alignSelf: 'center',
  },
  sendCodeSty: {
    marginTop: 30,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  sendcodeTxtSty: {
    textAlign: 'center',
    fontSize: 16,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
    textTransform:"capitalize"

  },
  codenotreceiveViewSty: {
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 10,
  },
  codenotreceiveTxtSty: {
    textAlign: 'center',
    fontSize: 16,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  sendagainTxtSty: {
    paddingLeft: 10,
    textDecorationLine: 'underline',
    textAlign: 'center',
    fontSize: 16,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    textTransform:"capitalize"

  },
  btnViewSty: {
    paddingHorizontal: 40,
    marginTop: 70,
  },
  troubleloginSty: {
    marginTop: 20,
    marginHorizontal: 10,
    flexDirection: 'row',
  },
  troubleloginTxtSty: {
    textAlign: 'center',
    fontSize: Dimensions.get('screen').width * 0.045,
    color: BaseColors.dashColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  gethelpTxtSty: {
    paddingLeft: 10,
    textDecorationLine: 'underline',
    textAlign: 'center',
    fontSize: Dimensions.get('screen').width * 0.041,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
  },
  sendAgain: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
});
