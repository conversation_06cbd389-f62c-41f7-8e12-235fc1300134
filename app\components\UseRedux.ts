import { AppDispatch, RootState } from '@redux/store/configureStore';
import { Provider, TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import { PersistGate } from 'redux-persist/es/integration/react';

/**
 * Custom hook to use Redux methods globally.
 */
export const useRedux = () => {
  const dispatch = useDispatch(); // No need for AppDispatch
  const useAppSelector = useSelector;


  return { dispatch, useAppSelector, Provider, PersistGate  };
};
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
