import {Dimensions, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const {width, height} = Dimensions.get('window');
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    fontSize: 22,
    marginBottom: 16,
    color: BaseColors.textColor,
    textAlign: 'center',
    fontFamily: FontFamily.OpenSansSemiBold,
  },
  card: {
    backgroundColor: '#f5faff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: BaseColors.primary,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    paddingHorizontal: 16,
    paddingTop: 10,
    paddingBottom: 15,
  },
  breakDownCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: BaseColors.textColor,

    borderStyle: 'dashed',
  },
  featuresTitle: {
    fontSize: 18,
    marginBottom: 12,
    color: BaseColors?.textColor,
    fontFamily: FontFamily.OpenSansSemiBold,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingLeft: 8,
  },
  bulletPoint: {
    marginRight: 8,
    fontSize: 16,
    color: '#666',
  },
  featureText: {
    fontSize: 14,
    color: BaseColors?.textColor,
    flex: 1,
    fontFamily: FontFamily.OpenSansRegular,
  },
  sectionTitle: {
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
    fontFamily: FontFamily.OpenSansRegular,
  },
  freeText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    padding: 20,
  },
  upgradeSection: {
    marginBottom: 16,
    borderWidth: 1,
    padding: 20,
    borderRadius: 10,
    borderColor: '#dbdfe3',
    backgroundColor: '#f5faff',
  },
  upgradeSectionTitle: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansSemiBold,
    marginBottom: 12,
    color: '#333',
  },
  planCard: {
    // backgroundColor: '#fff',
    borderRadius: 12,
    // padding: 16,
    // marginBottom: 12,
    paddingVertical: 10,
  },

  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  planTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  planTitle: {
    fontSize: 16,
    color: '#333',
    marginRight: 8,
    fontFamily: FontFamily.OpenSansRegular,
  },
  verifiedBadge: {
    borderRadius: 12,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  verifiedText: {
    color: '#fff',
    fontSize: 12,
    fontFamily: FontFamily.OpenSansSemiBold,
  },
  proTag: {
    backgroundColor: BaseColors.primary,
    paddingHorizontal: 15,
    borderRadius: 4,
    borderColor: BaseColors.primary,
    borderWidth: 1,
    paddingBottom: 4,
  },
  proTagText: {
    color: BaseColors.white,
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
  },
  planDescription: {
    fontSize: 14,
    color: BaseColors?.inputColor,
    lineHeight: 20,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: BaseColors.lightskyColor,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: BaseColors.lightskyColor,
  },
  confirmButton: {
    backgroundColor: BaseColors.lightskyColor,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: FontFamily.OpenSansSemiBold,
  },
  divider: {
    width: width * 0.3,
    borderWidth: 0.2,
    height: 0.5,
    borderColor: '#BDBDBD',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // marginVertical: height * 0.048,
  },
  cancelAmountSty: {
    fontSize: 12,
    textDecorationLine: 'line-through',
    paddingLeft: 5,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.black,
  },
  paySty: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansSemiBold,
    color: BaseColors.primary,
  },
  payView: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  amountViewSty: {
    marginRight: 8,
    height: 16,
    width: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  paymnthSty: {
    color: BaseColors.white,
    textAlign: 'center',
    fontFamily: FontFamily.OpenSansBold,
    paddingBottom: 4,
  },
  paymnthView: {
    marginRight: 8,
    height: 16,
    width: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: BaseColors.white,
  },
  yearViewSty: {
    borderWidth: 1,
    padding: 10,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    width: '49%',
  },
  monthViewSty: {},
  proTxtColor: {
    color: BaseColors.primary,
  },
  proViewSty: {
    backgroundColor: BaseColors.activeColor,
    marginLeft: 10,
    padding: 5,
    borderRadius: 5,
  },
  btnViewSty: {
    paddingHorizontal: 20,
  },
});
