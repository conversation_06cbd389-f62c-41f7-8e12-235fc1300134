/**
 * Exports Image Paths
 * @function Images
 */
export const Images = {
  splashScreenVideo1: require('../assets/videos/Splashscreen1.mp4'),
  harborLogo: require('../assets/images/harborUpdatedLogo.png'),
  addNameImg: require('../assets/images/womanCapital.png'),
  bronze: require('../assets/images/bronze.png'),
  gold: require('../assets/images/gold.png'),
  silver: require('../assets/images/silver.png'),
  googleLogo: require('../assets/images/google.png'),
  platinum: require('../assets/images/platinum.png'),
  Diamond: require('../assets/images/diamond.png'),
  usaPng: require('../assets/images/USA.png'),
  SpanishPng: require('../assets/images/Spanish.png'),
  Job: require('../assets/images/Job.png'),
  Jobfill: require('../assets/images/Jobfill.png'),
  Seeker: require('../assets/images/Seeker.png'),
  Seekerfill: require('../assets/images/Seekerfill.png'),
  shortLogo: require('../assets/images/shortLogo.png'),
  profileDummy: require('../assets/images/profildummy.jpeg'),
  verified: require('../assets/images/verified.png'),

  jobNotFound: require('../assets/images/jobNotFound.png'),
  newConversation: require('../assets/images/newConversation.png'),
  noJobsAvailable: require('../assets/images/noJobsAvailable.png'),
  noJobSeeker: require('../assets/images/noJobSeeker.png'),
  noProfileFound: require('../assets/images/noProfileFound.png'),
  female: require('../assets/images/female.png'),
  user: require('../assets/images/user.png'),
  AddScreen: require('../assets/images/AddScreen.png'),

  Technician: require('../assets/images/Technician.png'),
  Engineer: require('../assets/images/Engineer.png'),
  Stew: require('../assets/images/Stew.png'),
  Captain: require('../assets/images/Captain.png'),
  Mate: require('../assets/images/Mate.png'),

  // Lottie Animations
  introscreenone: require('../assets/lotties/initialIcon.json'),
  introscreenTwo: require('../assets/lotties/seekerMatch.json'),
  introscreenThree: require('../assets/lotties/levelUp.json'),
  noInternet: require('../assets/lotties/noInternet.json'),
  noData: require('../assets/lotties/noData.json'),
  declined: require('../assets/lotties/declined.json'),
  approved: require('../assets/lotties/approved.json'),
  waiting: require('../assets/lotties/waiting.json'),
  pending: require('../assets/lotties/pending.json'),
  loading: require('../assets/lotties/loaderAnimation.json'),
  batch: require('../assets/lotties/batchAnimation.json'),
};
