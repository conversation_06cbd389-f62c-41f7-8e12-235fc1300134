/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import {BaseColors} from '../../config/theme';
import {translate} from '../../lang/Translate';
import React, {useCallback, useState} from 'react';
import {
  Modal,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {CustomIcon} from '@config/LoadIcons';
import {useDispatch} from 'react-redux';
import authAction from '@redux/reducers/auth/actions';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {getData} from './apiFunctions';
import {useFocusEffect} from '@react-navigation/native';
import {FontFamily} from '@config/typography';
import {isEmpty} from '@app/utils/lodashFactions';
import {Images} from '@config/images';
import TextInput from '@components/UI/TextInput';
import Button from '@components/UI/Button';

const FastImage = require('react-native-fast-image');

const Animated = require('react-native-reanimated').default;
const FadeInDown = require('react-native-reanimated').FadeInDown;

const {setModalData} = authAction;

const DeleteReasonSchema = yup
  .object()
  .shape({deleteReason: yup.string().required('selectReason')});

const DeleteOtherReasonSchema = yup.object().shape({
  deleteReason: yup.string().required('selectReason'),
  deleteReasonText: yup
    .string()
    .required('reasonIsRequired')
    .trim('whiteSpaceErrMsg'),
});

const logOutSchema = yup.object().shape({});

const ModalComponent = ({
  visible,
  modalTitle,
  modalDescription,
  buttonTxt,
  highLightText,
  onClickSaveBtn,
  showDropDown = false,
  cancelText = translate('cancel'),
  showCancelBtn = true,
  style,
  centerIconName,
  dropDown = false,
  loading = false,
  logoutModal = false,
  setModalVisible = () => {},
  modalHeder = '',
  type = '',
}) => {
  const [showReasonInput, setShowReasonInput] = useState('');
  const [reasonList, setReasonList] = useState([]);

  const {
    control,
    handleSubmit,
    reset,
    formState: {errors},
  } = useForm({
    resolver: yupResolver(
      dropDown
        ? !isEmpty(showReasonInput) && showReasonInput === 'Other'
          ? DeleteOtherReasonSchema
          : DeleteReasonSchema
        : logOutSchema,
    ),
  });

  const dispatch = useDispatch();

  // state

  const handleOverlayClick = e => {
    // Check if the click event target is the overlay
    if (e.target === e.currentTarget) {
      if (logoutModal) {
        dispatch(setModalData({}));
      } else {
        setModalVisible(false);
      }
    }
  };

  const getIntroScreenData = async () => {
    const mainData = await getData();
    const d = mainData?.map(val => {
      return {label: val?.title, value: val?.title};
    });
    setReasonList(d);
  };

  useFocusEffect(
    useCallback(() => {
      reset();
      setShowReasonInput('');
      if (visible && type === 'sideBar' && dropDown) {
        getIntroScreenData();
      }
    }, [visible]),
  );

  return (
    <View style={{...styles.centeredView, ...style}}>
      <Modal
        animationType="fade"
        transparent={true}
        visible={visible}
        style={{...style}}
        onRequestClose={() => {
          if (logoutModal) {
            dispatch(setModalData({}));
          } else {
            setModalVisible(false);
          }
        }}>
        <TouchableWithoutFeedback onPress={handleOverlayClick}>
          <View style={styles.ovarlayStyle}>
            {!showDropDown && (
              <View style={styles.modalView}>
                <Animated.View
                  entering={FadeInDown}
                  duration={7000}
                  style={styles.closeIconSet}>
                  <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() =>
                      logoutModal
                        ? dispatch(setModalData({}))
                        : setModalVisible(false)
                    }>
                    <FastImage
                      source={Images.closeImg}
                      style={{
                        width: 24,
                        height: 24,
                      }}
                    />
                  </TouchableOpacity>
                </Animated.View>
                <Animated.View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 10,
                  }}
                  entering={FadeInDown}
                  duration={7000}>
                  <CustomIcon
                    name={centerIconName}
                    size={50}
                    color={BaseColors.activeTab}
                  />
                </Animated.View>
                {!isEmpty(modalHeder) ? (
                  <Animated.View entering={FadeInDown} duration={7000}>
                    <Text style={styles.modalHeder}>{modalHeder}</Text>
                  </Animated.View>
                ) : null}
                <Animated.View entering={FadeInDown} duration={7000}>
                  <Text style={styles.modalTitleText}>{modalTitle}</Text>
                </Animated.View>
                {/* {dropDown ? (
                  <View style={{marginBottom: 10}}>
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="deleteReason"
                      render={({field: {onChange, value}}) => (
                        <Animated.View
                          entering={FadeInDown}
                          style={styles.commonView}>
                          <CDropdown
                            labelplaceholder={translate('deleteReason')}
                            data={reasonList}
                            // setItem={(e) => onChange(e?.value)}
                            setItem={e => {
                              if (e?.value === 'Other') {
                                setShowReasonInput(e?.value);
                              } else {
                                setShowReasonInput('');
                              }
                              onChange(e?.value);
                            }}
                            value={value}
                            isError={errors?.deleteReason}
                            isErrorMsg={errors?.deleteReason?.message}
                          />
                        </Animated.View>
                      )}
                    />
                  </View>
                ) : null} */}

                {!isEmpty(showReasonInput) && showReasonInput === 'Other' ? (
                  <View style={{marginBottom: 10}}>
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="deleteReasonText"
                      render={({field: {onChange, value}}) => (
                        <Animated.View
                          entering={FadeInDown}
                          style={styles.commonView}>
                          <TextInput
                            placeholderText={'Type here your reason...'}
                            containerStyle={styles.textInputStyle}
                            onChange={onChange}
                            value={value}
                            isError={errors?.deleteReasonText}
                            isErrorMsg={errors?.deleteReasonText?.message}
                          />
                        </Animated.View>
                      )}
                    />
                  </View>
                ) : null}
                {modalDescription && (
                  <Text style={styles.modalText}>
                    {modalDescription}{' '}
                    <Text
                      style={{
                        ...styles.modalText,
                        color: BaseColors.primary,
                      }}>
                      {highLightText}
                    </Text>
                  </Text>
                )}
                {!showDropDown && (
                  <Animated.View
                    entering={FadeInDown}
                    duration={7000}
                    style={styles.backContainer}>
                    <View style={{width: '48%'}}>
                      <Button
                        onPress={() =>
                          logoutModal
                            ? dispatch(setModalData({}))
                            : setModalVisible(false)
                        }>
                        {cancelText}
                      </Button>
                    </View>
                    {showCancelBtn && (
                      <View style={{width: '48%'}}>
                        <Button
                          type="outlined"
                          onPress={handleSubmit(s => onClickSaveBtn(s))}
                          loading={loading}>
                          {buttonTxt}
                        </Button>
                      </View>
                    )}
                  </Animated.View>
                )}
              </View>
            )}
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    // flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeIconSet: {
    alignItems: 'flex-end',
    marginBottom: 10,
  },
  ovarlayStyle: {
    backgroundColor: 'rgba(0,0,0,0.2)',
    flex: 1,
    justifyContent: 'center',
  },
  modalView: {
    margin: 20,
    backgroundColor: BaseColors.white,
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    // alignItems: 'center',
    // width: '100%',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    marginVertical: 150,
  },
  btnStyle: {
    borderRadius: 10,
    backgroundColor: BaseColors.secondary,
  },
  modalTitleText: {
    fontSize: 20,
    color: BaseColors.textInputLabel,
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalHeder: {
    fontSize: 20,
    color: BaseColors.black,
    fontFamily: FontFamily.InterBold,
    textAlign: 'center',
    marginBottom: 10,
  },
  modalButtonText: {
    color: BaseColors.white,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    padding: 6,
    textAlign: 'center',
  },

  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    color: BaseColors.black,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  backContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
  },
  dropdownItemView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E6EFFE',
    marginTop: 10,
  },
  dropdownItem: {
    fontSize: 16,
  },
  checkIcon: {
    width: 18,
    height: 18,
  },
  emptyAddressLottieView: {
    height: 175,
    width: '100%',
    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    borderRadius: 200,
  },
  centerImg: {
    width: 200,
    height: 200,
    borderRadius: 10,
    marginVertical: 24,
  },
});

export default ModalComponent;
