/* eslint-disable react-native/no-inline-styles */
import React, {useMemo} from 'react';
import PropTypes from 'prop-types';
import {StyleSheet, Text } from 'react-native';
import {useTheme} from '@react-navigation/native';

/**
 * Component for Displaying Text
 * @function Text
 */
export default function Text(props: {
  size: Number;
  color: string;
  type: string;
  bold: Boolean;
  style: object;
  children: any;
  numberOfLines: number;
  testID?: any;
  lineBreakMode?: any;
  onPress?: any;
}) {
  const {
    size,
    color,
    type,
    bold,
    style,
    children,
    testID,
    lineBreakMode,
    onPress,
    ...rest
  } = props;

  const colors = useTheme();
  const BaseColors: any = colors.colors;

  const fontColor = useMemo(() => {
    if (['primary', 'secondary', 'gray', 'black', 'white'].includes(color)) {
      return styles[color]?.color;
    }
    return color;
  }, [color]);

  return (
    <Text
      lineBreakMode={lineBreakMode}
      style={[
        styles.baseFont,
        {
          color: fontColor,
          fontSize: size,
          ...styles[type],
        },
        {...style},
      ]}
      {...rest}
      testID={testID}
      allowFontScaling={false}
      onPress={onPress}>
      {children}
    </Text>
  );
}

const styles: any = StyleSheet.create({
  title: {
    fontSize: 22,
  },
  heading: {
    fontSize: 20,
  },
  subHeading: {
    fontSize: 18,
  },
  normal: {
    fontSize: 14,
  },
});

Text.propTypes = {
  type: PropTypes.oneOf([
    'normal',
    'heading',
    'subHeading',
    'normal',
    'heading',
    'title',
  ]),
  color: PropTypes.string,
  bold: PropTypes.bool,
  size: PropTypes.number,
  style: PropTypes.object,
  numberOfLines: PropTypes.number,
};

Text.defaultProps = {
  type: 'normal',
  color: 'black', // "primary"  | "secondary" | "gray" | "black" | "white" | "#43421"
  bold: false, // true | false
  size: 14,
  style: {},
  numberOfLines: 100000000000000,
};
