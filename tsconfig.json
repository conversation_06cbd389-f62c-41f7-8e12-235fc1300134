{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
   // ... other configs, if any
   "baseUrl": "." ,
   "paths": {
    "@config": ["app/config"],
    "@config/*": ["app/config/*"],
    "@components": ["app/components"],
    "@components/*": ["app/components/*"],
    "@assets": ["app/assets"],
    "@assets/*": ["app/assets/*"],
    "@redux": ["app/redux"],
    "@redux/*": ["app/redux/*"],
    "@screens": ["app/screens"],
    "@screens/*": ["app/screens/*"],
    "@navigation": ["app/navigation"],
    "@navigation/*": ["app/navigation/*"],
    "@language/": ["app/lang/"],
    "@language/*": ["app/lang/*"],
    "@app": ["app"],
    "@app/*": ["app/*"],
   },
}
}
