import {Dimensions, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const {width, height} = Dimensions.get('window');

export default StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  headerText: {
    fontSize: 16,
    fontWeight: '700',
    color: BaseColors.textColor,
    marginBottom: 2,
    fontFamily: FontFamily.OpenSansBold,
  },
  subHeaderText: {
    fontSize: 14,
    color: BaseColors.inputColor,
    marginBottom: 20,
    fontFamily: FontFamily.OpenSansRegular,
    textAlign: 'left',
  },
  planContainer: {
    borderWidth: 1,
    borderColor: BaseColors.boxTxtColor,
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    borderStyle: 'dashed', // Add dashed border
    backgroundColor: BaseColors.white, // Default background color
  },
  selectedPlanContainer: {
    backgroundColor: BaseColors.primary,
    borderColor: BaseColors.primary,
  },
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  radioCircle: {
    height: 26,
    width: 26,
    borderRadius: 13,
    borderWidth: 1, // Bolder outer border
    borderColor: BaseColors.primary,
    alignItems: 'center', // Center items within the circle
    justifyContent: 'center',
    backgroundColor: BaseColors.white,
    marginRight: 10,
  },
  innerradioCircle: {
    height: 21, // Slightly smaller than `radioCircle`
    width: 21,
    borderRadius: 21 / 2, // Ensures it’s perfectly round
    borderWidth: 1,
    borderColor: BaseColors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: BaseColors.white,
  },
  radioCircleSelected: {
    backgroundColor: BaseColors.white,
  },
  innerradioCircleSelected: {
    // backgroundColor: BaseColors.primary,
  },
  radioDot: {
    height: 10,
    width: 10,
    borderRadius: 5,
    backgroundColor: BaseColors.white,
  },

  planTitle: {
    fontSize: 17,
    color: BaseColors.textColor,
    // fontFamily: FontFamily.OpenSansMedium,
    paddingBottom: 2,
  },
  selectedPlanName: {
    color: BaseColors.white,
  },
  tagContainer: {
    backgroundColor: '#b1cbe2',
    borderRadius: 5,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginLeft: 10,
    borderWidth: 1,
    borderColor: BaseColors.primary,
  },
  selectagContainer: {
    backgroundColor: BaseColors.white,
  },
  tagText: {
    fontSize: 12,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansMedium,
    paddingBottom: 4,
  },
  selectTagText: {
    color: BaseColors.primary,
  },
  proTagContainer: {
    backgroundColor: BaseColors.white,
    borderRadius: 5,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginLeft: 5,
    borderColor: BaseColors.primary,
    borderWidth: 1,
  },
  proTagText: {
    fontSize: 12,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansMedium,
    paddingBottom: 4,
  },
  selectedPlanDetails: {
    backgroundColor: BaseColors.inputBackground,
    paddingHorizontal: 15,
    paddingTop: 10,
    paddingBottom: 10,
    borderRadius: 10,
    // marginTop: 10,
  },
  planDetails: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 10,
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  planPrice: {
    fontSize: 14,
    fontWeight: '500',
    color: BaseColors.textColor,
    marginBottom: 5,
    fontFamily: FontFamily.OpenSansMedium,
  },
  featuresTitle: {
    fontSize: 14,
    color: BaseColors.inputColor,
    marginBottom: 5,
    fontFamily: FontFamily.OpenSansRegular,
  },
  featureItem: {
    fontSize: 14,
    color: BaseColors.textColor,
    marginLeft: 10,
    marginVertical: 5,
    fontFamily: FontFamily.OpenSansMedium,
  },
  chooseplanSty: {
    paddingHorizontal: 20,
    paddingTop: 5,
  },
  priceViewSty: {
    backgroundColor: BaseColors.inputBackground,
    marginTop: 10,
    borderRadius: 10,
  },
  nextViewSty: {
    margin: 20,
  },
  containerSty: {
    flex: 1,
  },
  underLineView: {
    borderBottomWidth: 1,
    marginHorizontal: 17,
    borderColor: BaseColors.borderBottomColor,
  },
});
