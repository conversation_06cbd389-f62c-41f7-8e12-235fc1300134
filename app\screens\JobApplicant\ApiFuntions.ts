import { getApiData } from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import { translate } from '@language/Translate';
import { AxiosResponse } from 'axios';
import Toast from 'react-native-simple-toast';

// Define the response interface
  interface ApiDataResponse {
    success?: boolean;
    status?: boolean;
    error?: string;
    message?: string;
    action?: boolean;
    data?: any;
  }

  interface ErrorResponse {
    status?: any;
    data?: any;
    message?: string;
  }

export const updateJobStatus = async (type: string,
  { jobId, userId }: any
): Promise<AxiosResponse<ApiDataResponse> | ErrorResponse> => {
  try {
    const resp = await getApiData({
      endpoint: BaseSetting.endpoints.updateuserJob,
      method: 'POST',
      data: { jobId: jobId, status: type, userId: userId },
    });
    if (resp?.status) {
      // Toast.show(
      //   resp?.message ||
      //     translate(
      //       type === 'Approved' ? 'successApplied' : 'declinedApplied',
      //       '',
      //     ),
      //   Toast.SHORT,
      // );
      return { ...resp, status: resp?.status };
    } else {
      // Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      return resp;
    }
  } catch (e) {
    return e;
  }
};

