// usePayment.js
import { useState } from 'react';
import { useStripe } from '@stripe/stripe-react-native';
// import Toast from 'react-native-toast-message';
import BaseSetting from '@config/setting';
import { getApiData } from '@utils/apiHelper';

export const usePayment = () => {
  const { initPaymentSheet, presentPaymentSheet } = useStripe();
  const [loading, setLoading] = useState(false);

  const fetchPaymentSheetParams = async ({
    amount,
    payment_type,
    payment_choice_type,
    ref_meta,
    wallet_amount,

  }) => {
    let data = {};
    if (Number(amount) > 0) {
      data.amount = Number(amount);
    }
    if (Number(wallet_amount) > 0) {
      data.wallet_amount = Number(wallet_amount);
    }

    data.payment_type = payment_type;
    data.payment_choice_type = payment_choice_type;
    if (payment_type == 'vendor_payment') {
      data.ref_meta = {
        vendor_id: ref_meta.vendor_id, // expense UUID
        service_request_id: ref_meta.service_request_id, // split owner UUID
      };
    } else {
      data.ref_meta = {
        expense_id: ref_meta.expense_id, // expense UUID
        receiver_id: ref_meta.receiver_id, // split owner UUID
      };
    }

    const endpoint = BaseSetting.endpoints.getStripIntentId;
    const response = await getApiData(endpoint, 'POST', data);

    const { paymentIntent, ephemeralKey, customer } = await response.data;

    return {
      paymentIntent,
      ephemeralKey,
      customer,
    };
  };

  const initializePaymentSheet = async ({
    amount,
    payment_type = 'split_payment',
    payment_choice_type = 'stripe' | 'partial',
    wallet_amount = 0,
    ref_meta = {
      expense_id: null, // expense UUID
      receiver_id: null, // split owner UUID
      vendor_id: null, // vendor (user) UUID
      service_request_id: null, // service ID UUID
    },
  }) => {



    const { paymentIntent, ephemeralKey, customer } =
      await fetchPaymentSheetParams({
        amount,
        payment_type,
        payment_choice_type,
        ref_meta: {
          vendor_id: ref_meta?.vendor_id,
          service_request_id: ref_meta?.service_request_id,
          expense_id: ref_meta.expense_id, // expense UUID
          receiver_id: ref_meta.receiver_id, // split owner UUID
        },
        wallet_amount,
      });
    if (paymentIntent && ephemeralKey && customer) {
      const { error } = await initPaymentSheet({
        merchantDisplayName: 'The Harbor App',
        customerId: customer,
        customerEphemeralKeySecret: ephemeralKey,
        paymentIntentClientSecret: paymentIntent,
        allowsDelayedPaymentMethods: true,
        // defaultBillingDetails: {
        //   name: 'Jane Doe',
        // },
        googlePay: {
          merchantCountryCode: 'US',
          testEnv: false, // use test environment
        },
        applePay: {
          merchantCountryCode: 'US',
        },
        returnURL: 'com.harbor.app://stripe-redirect',
      });
      if (!error) {
        setLoading(true);
      }
      return openPaymentSheet();
    }
  };

  const openPaymentSheet = async () => {
    const { error } = await presentPaymentSheet();

    if (error) {
      if (error.code != 'Canceled') {
        // Toast.show({
        //   type: 'error',
        //   text2: `Error code: ${error.code} ${error.message}`,
        // });
      }
      return false;
    } else {
      // Alert.alert('Success', 'Your order is confirmed!');
      return true;
    }
  };

  return {
    initializePaymentSheet,
    openPaymentSheet,
    loading,
  };
};
