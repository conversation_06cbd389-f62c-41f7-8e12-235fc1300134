import {
  camelCase as lodashCamel<PERSON><PERSON>,
  cloneDeep as lodashClone<PERSON>eep,
  debounce as lodashDebounce,
  flattenDeep as lodashFlattenDeep,
  has as lodashHas,
  isArray as lodashIsArray,
  isEmpty as lodashIsEmpty,
  isNull as lodashIsNull,
  isNumber as lodashIsNumber,
  isObject as lodashIsObject,
  isString as lodashIsString,
  isUndefined as lodashIsUndefined,
  lowerCase as lodashLowerCase,
  reverse as lodashReverse,
  startCase as lodashStartCase,
  trim as lodashTrim,
  uniqBy as lodashUniqBy,
} from 'lodash-es';

export const uniqBy = <T>(array: T[], iteratee: string | ((value: T) => any)): T[] =>
  lodashUniqBy(array, iteratee);

export const isEmpty = (value: unknown): boolean => lodashIsEmpty(value);

export const isArray = (value: unknown): value is any[] => lodashIsArray(value);

export const isObject = (value: unknown): value is Record<string, any> => lodashIsObject(value);

export const isUndefined = (value: unknown): value is undefined => lodashIsUndefined(value);

export const isNull = (value: unknown): value is null => lodashIsNull(value);

export const lowerCase = (value: string): string => lodashLowerCase(value);

export const startCase = (value: string): string => lodashStartCase(value);

export const isString = (value: unknown): value is string => lodashIsString(value);

export const debounce = <F extends (...args: any[]) => any>(
  func: F,
  wait: number,
): ((...args: Parameters<F>) => void) => lodashDebounce(func, wait);

export const isNumber = (value: unknown): value is number => lodashIsNumber(value);

export const flattenDeep = <T>(value: T[]): T[] => lodashFlattenDeep(value);

export const cloneDeep = <T>(value: T): T => lodashCloneDeep(value);

export const reverse = <T>(array: T[]): T[] => lodashReverse([...array]);

export const camelCase = (value: string): string => lodashCamelCase(value);

export const trim = (value: string): string => lodashTrim(value);

export const has = <T extends object>(object: T, path: string | (string | number)[]): boolean => lodashHas(object, path);


