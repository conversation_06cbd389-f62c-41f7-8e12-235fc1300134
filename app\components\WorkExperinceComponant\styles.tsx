import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {Dimensions, Platform, StyleSheet} from 'react-native';

const {width, height} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

const dimensions = {
  imgHeight: height / 2.3,
  imgWidth: width / 1.35,
  dotWidth: width * 0.09,
  dotHeight: height * 0.005,
  titleFontSize: 30,
  bodyFontSize: 16,
};

export default StyleSheet.create({
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButtonContainer: {
    flexDirection: 'row',
    alignContent: 'center',
    marginRight: 20,
    marginTop: 15,
  },
  radioCircle: {
    height: 15,
    width: 15,
    borderRadius: 15 / 2,
    borderColor: BaseColors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioOuterCircle: {
    height: 20,
    width: 20,
    borderRadius: 20 / 2,
    borderWidth: 1,
    borderColor: BaseColors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioSelected: {
    backgroundColor: BaseColors.primary,
  },
  radioText: {
    marginLeft: 5,
    fontSize: 16,
  },
  btnView: {
    width: '48%',
  },
  label: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors?.lightTxtColor,
  },
  marginTop: {
    marginTop: 10,
  },
  mainView: {
    marginHorizontal: 15,
    marginTop: 10,
    marginBottom: 10,
  },
  viewContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  btnViewSty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    marginHorizontal: 15,
  },
  textStyle: {
    color: BaseColors?.textColor,
    fontSize: 21,
    fontFamily: FontFamily?.OpenSansRegular,
    marginBottom: 10,
  },
  boxStyle: {
    borderWidth: 1,
    borderColor: BaseColors?.white20,
    borderRadius: 10,
    marginTop: 10,
    height:"88%"
  },
  addExperinceSty: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.primary,
  },
  addExpeinceViewSty: {
    marginHorizontal: 15,
    marginVertical: 15,
  },
});
