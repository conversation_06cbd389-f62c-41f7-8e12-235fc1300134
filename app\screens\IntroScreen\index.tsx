import React, {useRef, useState} from 'react';
import {Dimensions, Text, TouchableOpacity, View} from 'react-native';
import Swiper from 'react-native-swiper';
import styles from './styles';
import {BaseColors} from '../../config/theme';
import Button from '../../components/UI/Button';
import {initTranslate, translate} from '../../lang/Translate';
import {Images} from '../../config/images';
import LanguageModal from '@components/LanguageModal';
import {useDispatch, useSelector} from 'react-redux';
import LottieView from 'lottie-react-native';
import {Platform} from 'react-native';
import langActions from '../../redux/reducers/language/actions';
import {store} from '../../redux/store/configureStore';
import FastImage from 'react-native-fast-image';

interface IntroDetail {
  id: number;
  title: string;
  body: string;
  imgesUrl?: any;
}

interface IntroScreenProps {
  navigation: any;
  type: any;
}
const IOS = Platform.OS === 'ios';

export default function IntroScreen({navigation}: IntroScreenProps) {
  const {languageData} = useSelector((state: any) => {
    return state.language;
  });
  const dispatch = useDispatch();

  const {setLanguage} = langActions;
  const [selectedLanguage, setSelectedLanguage] =
    useState<string>(languageData);

  const [index, setIndex] = useState<number>(0);
  const [openBottomSheet, setOpenBottomSheet] = useState<boolean>(false);
  const swiperRef = useRef<Swiper>(null);
  const refRBSheet = useRef<RBSheet>(null);
  const handleLanguageChange = () => {
    dispatch(setLanguage(selectedLanguage) as any);
    initTranslate(store, true);
    refRBSheet?.current?.close();
  };
  const introDetails: IntroDetail[] = [
    {
      id: 0,
      title: translate('welcomeaboard', ''),
      body: translate('discovertitle', ''),
      imgesUrl: Images.introscreenone,
    },
    {
      id: 1,
      title: translate('boardtwotitle', ''),
      body: translate('boardtwodescription', ''),
      imgesUrl: Images.introscreenTwo,
    },
    {
      id: 2,
      title: translate('boardthreetitle', ''),
      body: translate('boardthreedescription', ''),
      imgesUrl: Images.introscreenThree,
    },
  ];

  return (
    <View style={styles.screenContainer}>
      {/* <StatusBar barStyle="dark-content" backgroundColor={BaseColors.white} /> */}

      <TouchableOpacity
        onPress={() => {
          setOpenBottomSheet(true);
          refRBSheet.current?.open();
        }}
        style={{
          ...styles.skipButton,
          // backgroundColor: openBottomSheet
          //   ? BaseColors.primary
          //   : BaseColors.activeColor,
        }}>
        <View style={styles.imageView}>
          <FastImage
            source={Images.usaPng}
            style={{width: '100%', height: '100%'}}
            resizeMode="contain"
          />
        </View>
        {/* <MIcon
          name="translate"
          size={30}
          color={openBottomSheet ? BaseColors.white : BaseColors.secondaryBule}
        /> */}
      </TouchableOpacity>

      <Swiper
        ref={swiperRef}
        loop={false}
        containerStyle={styles.swiperContainer}
        index={index}
        onIndexChanged={(i: number) => setIndex(i)}
        showsPagination={false}
        autoplay={true}
        autoplayTimeout={4.55}
        autoplayDirection={true} // true is forward, optional
      >
        {introDetails.map((item, i) => (
          <View style={styles.container} key={item.id}>
            <View style={styles.imgContainer}>
              <View style={styles.imgView}>
                <LottieView
                  resizeMode="contain"
                  source={item?.imgesUrl}
                  autoPlay={true}
                  loop={true}
                  onAnimationFinish={() => {
                    if (i < introDetails.length - 1) {
                      setTimeout(() => {
                        swiperRef.current?.scrollBy(1);
                      }, 2000); // Small delay after animation ends
                    }
                  }}
                  style={{
                    width: Dimensions.get('screen').width,
                    height: IOS
                      ? Dimensions.get('screen').width - 80
                      : Dimensions.get('screen').width - 130,
                  }}
                />
              </View>
            </View>
          </View>
        ))}
      </Swiper>

      <View style={styles.dotsContainer}>
        {introDetails.map((_, dotIndex) => (
          <View
            key={dotIndex}
            style={[
              styles.dot,
              {
                backgroundColor:
                  dotIndex === index
                    ? BaseColors.primary
                    : BaseColors.inactiveswiper,
              },
            ]}
          />
        ))}
      </View>

      <View style={styles.textContainer}>
        <Text style={styles.titleText}>{introDetails[index].title}</Text>
        <Text style={styles.bodyText}>{introDetails[index].body}</Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          containerStyle={styles.nextButton}
          onPress={() => {
            // if (index < 2) {
            //   swiperRef.current?.scrollBy(1);
            // } else {
            //   dispatch(authActions?.setShowIntro(false) as any);
            //   navigation.replace('AuthScreen', {type: 'login'});
            // }
            navigation.replace('AuthScreen', {type: 'signup'});
          }}
          type="text">
          {translate('signUp', '')}
        </Button>
      </View>

      <View style={{...styles.logInContainer, marginTop: 0}}>
        <Button
          // containerStyle={styles.nextButton}
          onPress={() => {
            navigation.replace('AuthScreen', {type: 'login'});
            // if (index < 2) {
            //   swiperRef.current?.scrollBy(1);
            // } else {
            //   dispatch(authActions?.setShowIntro(false) as any);
            //   navigation.replace('AuthScreen', {type: 'login'});
            // }
          }}
          type="outlined">
          {translate('signIn')}
        </Button>
      </View>
      <LanguageModal
        refRBSheet={refRBSheet}
        setOpenBottomSheet={setOpenBottomSheet}
        title={translate('changeLanguage', '')}
        openLanguage={true}
        cancelProp={translate('Cancel', '')}
        selectedLanguage={selectedLanguage}
        setSelectedLanguage={setSelectedLanguage}
        onClick={handleLanguageChange}
        doneProp={translate('Done', '')}
        showDescription={false}
      />
    </View>
  );
}
