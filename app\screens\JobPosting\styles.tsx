import {Dimensions, Platform, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: BaseColors?.white,
    // paddingBottom: Dimensions.get('screen').height / 9.8,
  },
  scrollContainer: {
    flexGrow: 1,
  },

  nosearchView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: Dimensions.get('window').height / 1.6,
  },
  noFound: {
    fontSize: 26,
    fontFamily: FontFamily.OpenSansMedium,
    lineHeight: 51,
    marginVertical: 20,
  },
  jobPostingView: {
    borderWidth: 1,
    borderColor: '#e1e1e1',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 10,
  },
  jobDetailText: {
    fontSize: 21,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textColor,
  },
  paddingTop: {
    paddingTop: 20,
    paddingBottom: 10,
  },
  locationView: {
    paddingTop: IOS ? 15 : 10,
    paddingBottom: 10,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'baseline',
    gap: 5,
  },
  addSkillsText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: 5,
    color: BaseColors?.lightTxtColor,
    paddingTop: IOS ? 10 : 0,
  },
  marginVertical: {
    marginTop: IOS ? 7 : 1,
    marginBottom: 10,
  },
  suggestionsText: {
    fontSize: 14,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
    paddingHorizontal: 5,
  },
  jobDurationText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: 10,
    color: BaseColors.textColor,
    paddingTop: IOS ? 5 : 0,
  },
  durationContainer: {
    // borderWidth: 1,
    borderColor: '#e1e1e1',
    // padding: 15,
    marginBottom: 15,
    // borderRadius: 7,
    // elevation: 0.9,
    backgroundColor: '#ffff',
  },
  rowSpaceBetween: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    // paddingVertical: 10,
  },
  inputHalfWidth: {
    width: '48%',
  },
  moreClarificationContainer: {
    // borderWidth: 1,
    borderColor: '#e1e1e1',
    paddingVertical: IOS ? 12 : 5,
    // paddingHorizontal: 15,
    // elevation: 1,
    backgroundColor: '#ffff',
    borderRadius: 7,
  },
  moreClarificationText: {
    fontSize: 21,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textColor,
    paddingBottom: 10,
  },
  flatRate: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textColor,
    alignSelf: 'center',
    paddingTop: 5,
    marginLeft: 10,
  },
  row: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 5,
  },
  aboutContainer: {
    borderWidth: 1,
    borderColor: '#e1e1e1',
    marginTop: 15,
    paddingHorizontal: 15,
    // elevation: 1,
    backgroundColor: '#ffff',
    borderRadius: 7,
  },
  aboutView: {
    marginTop: 10,
  },
  aboutText: {
    fontSize: 21,
    fontWeight: '400',
    color: BaseColors.textColor,
    fontFamily: FontFamily?.OpenSansMedium,
  },
  optionalTxt: {
    fontSize: 14,
    color: BaseColors.lightGrey,
    fontFamily: FontFamily?.OpenSansRegular,
    paddingHorizontal: 5,
    fontStyle: 'italic', // Add this line
  },
  selfText: {
    fontSize: 16,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  briefView: {
    marginTop: 5,
  },
  briefText: {
    fontSize: 14,
    fontFamily: FontFamily?.OpenSansRegular,
    color: BaseColors.inputColor,
  },
  marginTop: {
    marginTop: 15,
    marginBottom: 10,
  },
  addImageContainer: {
    marginTop: 15,
  },
  addImageText: {
    fontSize: 16,
    color: BaseColors.textColor,
    paddingTop: 5,
    paddingBottom: 2,
    fontFamily: FontFamily.OpenSansRegular,
    textTransform: 'capitalize',
  },
  optionalImgTxt: {
    fontSize: 14,
    color: BaseColors.lightGrey,
    paddingTop: 5,
    paddingBottom: 2,
    paddingHorizontal: 5,
    fontFamily: FontFamily.OpenSansRegular,
    fontStyle: 'italic',
  },
  maxTxtSty: {
    fontSize: 14,
    color: BaseColors.lightTxtColor,
    textTransform: 'capitalize',
    fontFamily: FontFamily.OpenSansRegular,
  },
  chooseFilesContainer: {
    marginTop: 8,
  },
  chooseFilesButton: {
    borderWidth: 1,
    backgroundColor: '#F5FAFF',
    width: Dimensions.get('screen').width * 0.25,
    height: Dimensions.get('screen').height / 8,
    borderRadius: 5,
    borderColor: '#e1e1e1',
    justifyContent: 'center', // Ensure content is centered vertically
    alignItems: 'center', // Ensure content is centered horizontally
  },
  chooseFilesText: {
    fontSize: 16,
    color: BaseColors.primary,
    textAlign: 'center', // Center the text horizontally
    justifyContent: 'center', // Ensure it's centered vertically as well
    alignItems: 'center',
    padding: 5,
    fontFamily: FontFamily.OpenSansRegular,
  },
  jobCostContainer: {
    borderWidth: 1,
    borderColor: '#e1e1e1',
    marginTop: 15,
    paddingHorizontal: 15,
    paddingTop: IOS ? 15 : 5,
    elevation: 1,
    backgroundColor: '#ffff',
    borderRadius: 7,
  },
  jobCostText: {
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 18,
    textTransform: 'capitalize',
  },
  paymentRequiredText: {
    color: BaseColors.lightTxtColor,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
    paddingTop: IOS ? 5 : 0,
    // textTransform: 'capitalize',
  },
  totalSalaryText: {
    color: BaseColors.lightTxtColor,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
    textTransform: 'capitalize',

    // paddingTop: 10,
  },
  salaryAmountText: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
    
  },
  salaryText: {
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
  },
  serviceChargeText: {
    color: BaseColors.lightTxtColor,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
    textTransform: 'capitalize',
  },
  bottomBorder: {
    borderBottomWidth: 1,
    borderColor: '#e1e1e1',
    width: '100%',
    paddingTop: 15,
  },
  estimatedChargesText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.primary,
    paddingBottom: 5,
    textTransform: 'capitalize',
  },
  btnView: {
    width: '47%',
  },
  btnSty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: IOS ? 25 : 20,
    marginHorizontal: 20,
    paddingBottom: IOS ? 20 : 0,
  },
  imagesRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  imageWrapper: {
    position: 'relative',
    marginRight: 10,
    marginBottom: 10,
  },
  uploadedImage: {
    width: Dimensions.get('screen').width * 0.25,
    height: Dimensions.get('screen').height / 8,
    borderRadius: 5,
  },
  deleteIcon: {
    position: 'absolute',
    top: 5,
    right: 5,
    borderRadius: 6,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: BaseColors?.red,
  },
  deleteText: {
    color: BaseColors.red,
    fontSize: 12,
  },
  cntrlTtlSty: {
    paddingTop: IOS ? 15 : 5,
  },
  loaderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.7)', // Optional: semi-transparent background
  },
  iconViewSty: {
    alignSelf: 'center',
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BaseColors.whiteColor,
    borderRadius: 10,
    paddingHorizontal: IOS ? 10 : 6,
    paddingVertical: IOS ? 6 : 4,
    borderWidth: 1,
    borderColor: BaseColors.primary,
    margin: 5,
    justifyContent: 'center',
    textAlign: 'center',
    alignSelf: 'center',
  },
  tagText: {
    color: BaseColors.primary,
    marginRight: 5,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 13,
    paddingBottom: IOS ? 0 : 5,
  },
  iconStyle: {paddingTop: 2},
  placeholderText: {
    color: '#888',
    fontSize: 14,
  },

  errView: {
    paddingTop: 6,
  },
  checkboxErr: {
    fontSize: 14,
    color: BaseColors.red,
    fontFamily: FontFamily.OpenSansRegular,
    // alignSelf: 'center',
    marginHorizontal: 35,
  },
  txtSty: {
    borderBottomColor: BaseColors.primary,
    borderBottomWidth: 1,
    color: BaseColors.primary,
    // textAlign: 'center',
    // backgroundColor: 'pink',
  },
  clickSty: {
    borderWidth: 1,
    borderColor: BaseColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    height: 17,
    width: 17,
    marginTop: 2,
  },
  checkboxView: {
    paddingTop: 10,
    alignContent: 'center',
    // justifyContent: 'center',
    flexDirection: 'row',
    marginHorizontal: 15,
  },
  txtView: {
    flexDirection: 'row',
    flexWrap: 'wrap', // Allow text to wrap to the next line
    alignItems: 'center', // Vertically align items
  },
  mView: {
    flex: 1, // Allow the container to take available width
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
  },
  customView: {
    backgroundColor: BaseColors.secondary,
    borderRadius: 10,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  customViewText: {
    fontSize: 14,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    // textTransform: 'capitalize',
  },
  optionsContainer: {
    // width: nWidth,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 5,
  },
  fileContainer: {
    width: Dimensions.get('screen').width * 0.25,
    height: Dimensions.get('screen').height / 8,
    borderRadius: 5,
    borderWidth:1
  },
  fileIcon: {
    fontSize: 30,
  },
  fileName: {
    fontSize: 12,
    marginTop: 5,
    textAlign: 'center',
  },
});
