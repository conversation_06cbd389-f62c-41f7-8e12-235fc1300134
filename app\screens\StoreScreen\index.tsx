import React from 'react';
import { View, Text, StatusBar } from 'react-native';
import styles from './styles';
import { translate } from '@language/Translate';
import { useTheme } from '@react-navigation/native';
import { useSelector } from 'react-redux';
export default function StoreScreen({ navigation }: { navigation: any }) {
  const { darkmode } = useSelector((state: any) => {
    return state.auth;
  });
  const colors = useTheme();
  const BaseColors: any = colors.colors;
  return (
    <View
      style={{
        ...styles.container,
      }}>
      {/*  <StatusBar barStyle={'dark-content'} /> */}

      <View style={styles.nosearchView}>
        <Text style={{ ...styles.noFound, color: BaseColors.textGrey }}>
          {translate('comingSoon', '')}
        </Text>
      </View>
    </View>
  );
}
