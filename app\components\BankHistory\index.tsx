import React from 'react';
import {copyToClipBoard, downloadFile} from '@app/utils/CommonFunction';
import moment from 'moment';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {Text, TouchableOpacity, View} from 'react-native';
import styles from './styles';
import {BaseColors} from '@config/theme';
import {translate} from '@language/Translate';
import Button from '@components/UI/Button';

export default function BankHistory({item, navigation}: any) {
  const isCredited = item?.transaction === 'credited';
  return (
    <TouchableOpacity activeOpacity={0.8} style={styles.renderView}>
      <View style={styles.row}>
        <View>
          <Text style={styles.name} numberOfLines={1}>
            {translate('status', '')} -{' '}
            <Text style={styles.name}>{item?.status}</Text>
          </Text>
        </View>

        <Text
          numberOfLines={1}
          style={{
            ...styles.amount,
            color: BaseColors.green,
          }}>
          ${item?.amount || '0'}
        </Text>
      </View>

      <View style={[styles.row, {paddingVertical: 3}]}>
        <TouchableOpacity
          style={[styles.clipBoard, styles.row]}
          activeOpacity={0.8}
          onPress={() => {
            copyToClipBoard(item?.stripePayoutId);
          }}>
          <Text style={styles.id}>{item?.stripePayoutId}</Text>
          <AntDesign name="copy1" />
        </TouchableOpacity>

        <Text style={styles.date}>
          {moment(item?.createdAt).format('DD MMM YYYY')}
        </Text>

        {/* <TouchableOpacity
          style={styles.clipBoard}
          activeOpacity={0.8}
          onPress={() => {
            if (item?.invoice) {
              downloadFile(item?.invoice);
            }
          }}>
          <AntDesign name="download" size={20} color={BaseColors.lightGrey} />
        </TouchableOpacity> */}
      </View>
    </TouchableOpacity>
  );
}
