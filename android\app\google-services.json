{"project_info": {"project_number": "735633804275", "project_id": "totemic-bonus-441515-k9", "storage_bucket": "totemic-bonus-441515-k9.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:735633804275:android:c2e27f7fb9dd3903ef2086", "android_client_info": {"package_name": "com.harbor.app"}}, "oauth_client": [{"client_id": "735633804275-32gpbosia7jpce7vonpjffo803ntn3sk.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.harbor.app", "certificate_hash": "963846ea1b8a424b5d4db51bd71f1d61fc040006"}}, {"client_id": "735633804275-n738f6nk19ggqb904fa6n59li62mucum.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.harbor.app", "certificate_hash": "5e8f16062ea3cd2c4a0d547876baa6f38cabf625"}}, {"client_id": "735633804275-gfarin446qoc0du0bk9c3hm5ianfgfn1.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCQpkKxhxr6Ff4sglMo1vg-PI_ggshfzMs"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "735633804275-gfarin446qoc0du0bk9c3hm5ianfgfn1.apps.googleusercontent.com", "client_type": 3}, {"client_id": "735633804275-0pb1enuqo12kk7bucpnhql2k5eantqsa.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.harbor.newapp", "app_store_id": "6746108776"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:735633804275:android:e728c27d5ea1aeb5ef2086", "android_client_info": {"package_name": "com.harbor.newapp"}}, "oauth_client": [{"client_id": "735633804275-stg8g6vhfn9q4igl29gftq9t6tsi30da.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.harbor.newapp", "certificate_hash": "963846ea1b8a424b5d4db51bd71f1d61fc040006"}}, {"client_id": "735633804275-u6cu8kh1ptn8c1gp6iaov44ttaj268ca.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.harbor.newapp", "certificate_hash": "5e8f16062ea3cd2c4a0d547876baa6f38cabf625"}}, {"client_id": "735633804275-gfarin446qoc0du0bk9c3hm5ianfgfn1.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCQpkKxhxr6Ff4sglMo1vg-PI_ggshfzMs"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "735633804275-gfarin446qoc0du0bk9c3hm5ianfgfn1.apps.googleusercontent.com", "client_type": 3}, {"client_id": "735633804275-0pb1enuqo12kk7bucpnhql2k5eantqsa.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.harbor.newapp", "app_store_id": "6746108776"}}]}}}], "configuration_version": "1"}