import {getApiData} from '@app/utils/apiHelper';
import AlertModal from '@components/AlertModal';
import Header from '@components/Header';
import {CustomIcon} from '@config/LoadIcons';
import BaseSetting from '@config/setting';
import {BaseColors, BaseStyles} from '@config/theme';
import {FontFamily} from '@config/typography';
import {translate} from '@language/Translate';
import React, {useCallback, useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {useFocusEffect} from '@react-navigation/native';
import {isEmpty} from '@app/utils/lodashFactions';
import ApplicantDetailComponant from '@components/ApplicantDetailComponant';
import AnimatedView from '@components/AnimatedView';
import Toast from 'react-native-simple-toast';
import {useSelector} from 'react-redux';

export default function ApplicantDetails({
  navigation,
  route,
}: {
  navigation: any;
  route: any;
}) {
  const {params} = route;
  const {applicantData, job, Id, userId, type, reviewType, seekerProfile} =
    params;

  const [toggle, setToggle] = useState(false);
  const [jobList, setJobList] = useState<any>('');
  const [loader, setLoader] = useState(false);
  const [userDetails, setUserDetails] = useState({});
  const {userData} = useSelector((s: any) => s.auth);
  const [refreshing, setRefreshing] = useState(false);

  let data = !isEmpty(applicantData) ? applicantData : userDetails;

  const [state, setState] = useState({
    bottomLoading: false,
    refreshing: false,
    loader: false,
    saveLoader: false,
    applicantsLoader: false,
    confirmationModal: false,
    applicant: {},
    job: {},
  });

  const getList = useCallback(async () => {
    if (Id) {
      try {
        const resp = await getApiData({
          endpoint: `${BaseSetting.endpoints.jobDetail}/${Id}`,
          method: 'GET',
        });
        if (resp?.data && resp?.status) {
          setJobList(resp.data); // Update list with new data
        } else {
          Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
        }
      } catch (error) {
        console.error('Error fetching list:', error);
        // Toast.show('Failed to fetch data.', Toast.SHORT);
      }
    }
  }, [Id]);

  const updateAvailableStatus = useCallback(
    async (p: any) => {
      try {
        const resp = await getApiData({
          endpoint: `${BaseSetting.endpoints.updateAvaialble}`,
          method: 'POST',
          data: {isAvailable: p},
        });
        if (resp?.data && resp?.status) {
          // setJobList(resp.data); // Update list with new data
        } else {
          Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
        }
      } catch (error) {
        console.error('Error fetching list:', error);
        // Toast.show('Failed to fetch data.', Toast.SHORT);
      }
    },
    [toggle],
  );

  useFocusEffect(
    useCallback(() => {
      console.log('useFocusEffect triggered');
      if (type === 'seeker' || type === 'viewEmployer') {
        null;
      } else {
        getList();
      }
      return () => {
        console.log('Cleanup on screen leave (if needed)');
      };
    }, []),
  );

  const getUserDetails = useCallback(
    async (type?: any = '') => {
      if (type !== 'silently') {
        if (type === 'refresh') {
          setRefreshing(true);
        } else {
          setLoader(true);
        }
      }
      try {
        const resp = await getApiData({
          endpoint: `${BaseSetting.endpoints.usersList}`,
          method: 'GET',
          data: {userId, page: 1},
        });
        console.log('🚀 ~ resp:', resp);
        if (resp?.data && resp?.status) {
          setUserDetails(resp?.data); // Update user details
          if (type === 'refresh') {
            setRefreshing(false);
          } else {
            setLoader(false);
          }
        } else {
          if (type === 'refresh') {
            setRefreshing(false);
          } else {
            setLoader(false);
          }
          Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
        }
      } catch (error) {
        if (type === 'refresh') {
          setRefreshing(false);
        } else {
          setLoader(false);
        }
        console.error('Error fetching list:', error);
        // Toast.show('Failed to fetch data.', Toast.SHORT);
      }
    },
    [userId],
  );

  useEffect(() => {
    getUserDetails();
  }, [userId]);

  const onRefresh = React.useCallback(() => {
    if (!loader) {
      getUserDetails('refresh');
    }
  }, [loader]);

  const handleClick = useCallback(async (type: string) => {
    if (type === 'Approved') {
      navigation.navigate('JobDetailScreen', {
        applicant: data,
        type,
        job,
      });
    } else {
      setState((p: any) => ({
        ...p,
        confirmationModal: true,
        applicant: data,
        type,
        job,
      }));
    }
  }, []);

  const updateJobStatus = useCallback(
    async (type: string) => {
      setState((p: any) => ({...p, applicantsLoader: true}));
      try {
        const resp = await getApiData({
          endpoint: BaseSetting.endpoints.updateuserJob,
          method: 'POST',
          data: {jobId: job?.id, status: type, userId: state?.applicant?.id},
        });
        if (resp?.status) {
          setState((p: any) => ({
            ...p,
            confirmationModal: false,
            applicant: {},
            job: {},
          }));
          // Toast.show(
          //   resp?.message ||
          //     translate(
          //       type === 'Approved' ? 'successApplied' : 'declinedApplied',
          //       '',
          //     ),
          //   Toast.SHORT,
          // );
        } else {
          Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
        }
        setState((p: any) => ({
          ...p,
          applicantsLoader: false,
        }));
      } catch (e) {
        setState((p: any) => ({
          ...p,
          applicantsLoader: false,
        }));
        console.log('ERRR', e);
      }
    },
    [state],
  );
  const [saveLoader, setSaveLoader] = useState(false);

  const handleSave = async () => {
    setSaveLoader(data?.id);
    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.seekerSave,
        method: 'POST',
        data: {
          userId: userId,
        },
      });
      if (resp?.status) {
        userDetails.savedUser =
          resp?.data === 'created' ? {id: userData?.id} : null;
        if (!isEmpty(data)) {
          data.savedUser = resp?.data === 'created' ? {id: userData?.id} : null;
        }
        setUserDetails(userDetails);
        setSaveLoader(false);
      } else {
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
      setSaveLoader(false);
    } catch (e) {
      setSaveLoader(false);

      console.log('ERRR', e);
    }
  };
  return (
    <SafeAreaView style={{flex: 1, backgroundColor: BaseColors.white}}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        leftIcon="back-arrow"
        title={''}
        rightIcons={
          userData?.id === userDetails?.id
            ? null
            : [
                {
                  icon: !isEmpty(data.savedUser)
                    ? 'bookmark-sharp'
                    : 'bookmark-outline',
                  onPress: () => {
                    handleSave();
                  },
                  saveLoader: saveLoader,
                  type: 'saveIcon',
                  wrapStyle: BaseStyles.notificationIconStyle,
                },
              ]
        }
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      {/* <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[BaseColors.primary]} // Customize refresh indicator color
              tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
            />
          }> */}
      {loader ? (
        <View style={[styles.container, styles.loader]}>
          <ActivityIndicator color={BaseColors.primary} size={'large'} />
        </View>
      ) : (
        <View style={{flex: 1, marginHorizontal: 15}}>
          <AnimatedView>
            <ApplicantDetailComponant
              navigation={navigation}
              data={data}
              applicantData={data}
              job={job}
              Id={Id}
              userId={userId}
              type={type}
              jobList={jobList}
              toggle={toggle}
              updateAvailableStatus={updateAvailableStatus}
              setToggle={setToggle}
              handleClick={handleClick}
              reviewType={reviewType}
              userDetails={userDetails}
              refreshing={refreshing}
              onRefresh={onRefresh}
              seekerProfile={seekerProfile}
              id={
                type === 'seeker'
                  ? userDetails?.id || job?.userId
                  : job?.approvedApplicant?.userId || userDetails?.id
              }
            />
          </AnimatedView>
        </View>
      )}
      {state.confirmationModal && (
        <AlertModal
          image
          title="Are you sure to decline this aplicant for this job?"
          visible={state.confirmationModal}
          setVisible={(val: any) =>
            setState((p: any) => ({...p, confirmationModal: false}))
          }
          btnYPress={() => {
            updateJobStatus('Declined');
          }}
          loader={state?.applicantsLoader}
          btnYTitle={translate('YES')}
          btnNTitle={translate('CANCEL')}
          btnNPress={() => {
            setState((p: any) => ({...p, confirmationModal: false}));
          }}
          confirmation
        />
      )}
      {/* </ScrollView> */}
    </SafeAreaView>
  );
}

const Section = ({title, children}) => (
  <View style={styles.sectionContainer}>
    <Text style={styles.sectionTitle}>{title}</Text>
    {children}
  </View>
);

const ExperienceCard = () => (
  <View style={styles.card}>
    <View style={styles.cardRow}>
      <FastImage
        source={{uri: 'https://via.placeholder.com/40'}}
        style={styles.cardIcon}
      />
      <View>
        <Text style={styles.cardTitle}>Company Name</Text>
        <Text style={styles.cardSubtitle}>Lorem ipsum</Text>
      </View>
    </View>
    <Text style={styles.cardDescription}>
      Lorem ipsum dolor sit amet consectetur. Risus quisque aliquet tellus a
      lacus.
    </Text>
  </View>
);

const CertificateCard = () => (
  <View style={styles.card}>
    <View style={styles.cardRow}>
      <FastImage
        source={{uri: 'https://via.placeholder.com/40'}}
        style={styles.cardIcon}
      />
      <View>
        <Text style={styles.cardTitle}>Course name will be here</Text>
      </View>
      <View style={styles.badgeIcon}>
        <CustomIcon name="verified" size={20} color={BaseColors.primary} />
      </View>
    </View>
    <Text style={styles.cardDescription}>
      Lorem ipsum dolor sit amet consectetur. Risus quisque aliquet tellus a
      lacus.
    </Text>
    <View style={styles.placeholderImage} />
  </View>
);
const CertificateCards = () => (
  <View style={styles.card}>
    <View style={styles.cardRow}>
      <FastImage
        source={{uri: 'https://via.placeholder.com/40'}}
        style={styles.cardIcon}
      />
      <View>
        <Text style={styles.cardTitle}>Course name will be here</Text>
      </View>
    </View>
    <Text style={styles.cardDescription}>
      Lorem ipsum dolor sit amet consectetur. Risus quisque aliquet tellus a
      lacus.
    </Text>
  </View>
);

const ProjectCard = () => (
  <View style={styles.card}>
    <View style={styles.cardRow}>
      <FastImage
        source={{uri: 'https://via.placeholder.com/40'}}
        style={styles.cardIcon}
      />
      <View>
        <Text style={styles.cardTitle}>Project title will be here</Text>
      </View>
    </View>
    <Text style={styles.cardDescription}>
      Lorem ipsum dolor sit amet consectetur. Risus quisque aliquet tellus a
      lacus.
    </Text>
  </View>
);

const IOS = Platform.OS === 'ios';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  loader: {justifyContent: 'center'},
  profileContainer: {
    flex: 1,
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  profileImageWrapper: {
    borderWidth: 1,
    width: 110,
    height: 110,
    borderRadius: 110 / 2,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: 'gray',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#DDD',
  },
  verificationBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,

    borderRadius: 12,
    padding: 3,
  },
  badgeText: {
    color: '#FFF',
    fontWeight: 'bold',
    fontSize: 10,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 10,
    color: BaseColors.textGrey,
  },
  switchView: {
    alignItems: 'center',
    backgroundColor: BaseColors.inputBackground,
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 5,
    marginBottom: 10,
    flexDirection: 'row',
  },
  available: {
    marginLeft: 20,
  },
  icon: {
    marginLeft: 10,
  },
  status: {
    fontSize: 14,
    marginVertical: 5,
    color: '#666',
  },
  onlineDot: {
    color: '#4CAF50',
    fontSize: 16,
  },
  location: {
    color: BaseColors.placeHolderColor,
    textDecorationLine: 'underline',
    fontSize: 14,
    marginTop: 5,
  },
  rating: {
    color: BaseColors.primary,
    fontSize: 14,
    marginVertical: 5,
  },
  skillsWrapper: {
    flexDirection: 'row',
    marginVertical: 5,
    flexWrap: 'wrap',
    gap: 10,
  },
  skillBadge: {
    backgroundColor: '#EFEFEF',
    borderRadius: 20,
    paddingVertical: 5,
    paddingHorizontal: 15,
    marginHorizontal: 5,
  },
  skillText: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: 2,
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 10,
    color: '#666',
  },
  messageButton: {
    backgroundColor: '#007BFF',
    padding: 10,
    borderRadius: 5,
    marginVertical: 10,
  },
  messageButtonText: {
    color: '#FFF',
    fontWeight: 'bold',
    fontSize: 14,
  },
  sectionContainer: {
    padding: 15,
    backgroundColor: '#F5FAFF',
    marginTop: 10,
    borderRadius: 10,
    marginHorizontal: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  card: {
    marginBottom: 10,
    padding: 10,
    borderRadius: 5,
  },
  cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardIcon: {
    width: 40,
    height: 40,
    marginRight: 10,
    borderRadius: 20,
  },
  badgeIcon: {
    marginLeft: 'auto',
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  cardSubtitle: {
    fontSize: 12,
    color: '#666',
  },
  cardDescription: {
    fontSize: 12,
    color: '#666',
    marginVertical: 5,
    marginLeft: 52,
  },
  placeholderImage: {
    width: 80,
    height: 80,
    backgroundColor: '#EFEFEF',
    marginTop: 10,
    alignSelf: 'center',
  },
  moreLink: {
    color: BaseColors.primary,
    fontSize: 12,
    marginTop: 10,
    textAlign: 'center',
  },
  msgIconStyle: {
    right: 0,
    width: 35,
    height: 35,
    // borderWidth: 1,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColors.primary,
  },
  proBadge: {
    backgroundColor: BaseColors.activeColor,
    color: BaseColors.primary,
    fontSize: 10,
    borderRadius: 5,
    paddingHorizontal: 10,
    marginLeft: 5,
    padding: 3,
  },
  levelBadge: {
    backgroundColor: BaseColors.activeColor,
    color: BaseColors.primary,
    fontSize: 10,
    borderRadius: 5,
    paddingHorizontal: 5,
    marginLeft: 5,
    paddingTop: 3,
  },
  approveBtn: {
    backgroundColor: BaseColors.completedColor,
    width: '47%',
  },
  declineBtn: {
    borderColor: BaseColors.btnRed,
    width: '47%',
  },
  applicationsBtns: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginBottom: 10,
    marginTop: 20,
  },
});
