import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';

const { StyleSheet, Dimensions, Platform } = require('react-native');

const HEIGHT = Dimensions.get('window').height;
const sHeight = Dimensions.get('screen').height;

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: BaseColors.white,
    marginBottom: HEIGHT / 12,
  },
  sendBtnMain: {
    backgroundColor: BaseColors.activeTab,
    height: 45,
    width: 45,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  unblockBtnView: {
    paddingHorizontal: 14,
    paddingVertical: 5,
    borderRadius: 5,
    backgroundColor: BaseColors.white,
    borderWidth: 0.5,
    elevation: 5,
    borderColor: '#D9D9D9',
  },
  dotMainView: {
    width: 10 ,
  },
  dotView: {
    width: 8,
    height: 8,
    borderRadius: 5,
    backgroundColor: BaseColors.primary,
  },
  unblockBtnText: {
    color: BaseColors.black,
    fontFamily: FontFamily.OpenSansMedium,
  },
  unblockBtn: {
    position: 'absolute',
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerMain: {
    flex: 1,
    paddingHorizontal: 10,
    justifyContent: 'center',
    height: sHeight / 1.8,
    marginBottom: 15,
  },
  tabComponantSty: {
    marginTop: 3,
  },
  loaderStyle: {
    height: sHeight / 1.5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  flatListContainer: {
    marginBottom: Platform.OS === 'ios' ? 20 : 40,
    flex: 1,
    // marginTop: isIOS ? 25 : 10,
  },
  tickAndMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 3,
  },
  secondMainScreen: {
    flex: 1,
    marginHorizontal: 6,
    marginTop: 12,
  },
  searchView: {
    marginHorizontal: 20,
    marginVertical: 10,
  },
  mainMsgView: {
    marginVertical: 5,
    matginRight: 5,
    // marginHorizontal: 5,
  },
  nameTimeStyle: {
    paddingLeft: 20,
    flex: 1,
  },
  renderItemMainView: {
    flex: 1,
    flexDirection: 'row',
    // paddingVertical: 10,
    alignItems: 'center',
  },
  imgMainView: {
    marginLeft: 5,
  },
  imgView: {
    height: 50,
    width: 50,
    borderRadius: 33,
  },
  titleView: {
    flex: 1,
    justifyContent: 'flex-start',
    flexDirection: 'row',
  },
  userNameViewText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.textColor,
    textTransform: 'capitalize',
    flex: 1,
  },
  messageText: {
    fontSize: 13.5,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.gray6,
  },
  typingText: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.activeTab,
  },
  timeText: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textBlack,
    marginLeft: 10,
  },
  messageCount: {
    flex: 1,
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  messageCountText: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 11.5,
    color: BaseColors.textBlack,
    flex: 1,
  },
  emptyAddressLottieView: {
    height: 270,
  },
  greenDot: {
    position: 'absolute',
    right: -5,
    height: 15,
    width: 15,
    backgroundColor: BaseColors.green,
    borderRadius: 25,
    bottom: 0,
    top: 0,
    borderWidth: 3,
    borderColor: BaseColors.white,
  },
  underLine: {
    borderBottomWidth: 0.7,
    borderColor: BaseColors?.timeColor,
    marginLeft: 5,
  },
});
export default styles;
