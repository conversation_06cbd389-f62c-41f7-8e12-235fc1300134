import React, {useEffect, useState} from 'react';
import {
  Dimensions,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Modal,
  Platform,
  TouchableWithoutFeedback, // <-- Import TouchableWithoutFeedback
} from 'react-native';
import {BaseColors} from '@config/theme';
import Button from '@components/UI/Button';
import styles from './styles';
import {useDispatch, useSelector} from 'react-redux';
import userConfigActions from '@redux/reducers/userConfig/actions';
import {translate} from '@language/Translate';
import {CustomIcon} from '@config/LoadIcons';

interface MenuItem {
  label: string;
  onPress?: () => void;
}

interface ModalComponantProps {
  visible: boolean;
  hideMenu: () => void;
  position?: {
    right: number;
    top: number;
  };
  getLstData: () => void;
  selectedTab: any;
}

const ModalComponant: React.FC<ModalComponantProps> = ({
  visible,
  hideMenu,
  position,
  getLstData,
  selectedTab,
}) => {
  const menuItems =
    selectedTab === 'Seeker'
      ? [
          {label: 'Level', name: translate('level', '')},
          {label: 'Review', name: translate('Reviews', '')},
        ]
      : [
          {
            label: 'startDateTime',
            name: translate('startDate', ''),
          },
          {label: 'Date listed', name: translate('dateListed', '')},
          {label: 'Level', name: translate('level', '')},
          {label: 'Pay', name: translate('pay', '')},
        ];

  const dispatch = useDispatch();
  const {setSort} = userConfigActions;
  const {languageData} = useSelector((state: any) => {
    return state.language;
  });

  const {sort} = useSelector((state: any) => state.userConfig);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [submenuVisibleIndex, setSubmenuVisibleIndex] = useState<number | null>(
    null,
  );
  const [selectedSubmenu, setSelectedSubmenu] = useState<string | null>(null);

  const handleRadioSelect = (index: number) => {
    setSelectedIndex(index);
    setSubmenuVisibleIndex(submenuVisibleIndex === index ? null : index);
    setSelectedSubmenu(null); // Reset submenu selection when selecting a new radio button
  };

  const handleSubmenuSelect = (option: string) => {
    setSelectedSubmenu(option);
  };
  const handleOkPress = () => {
    hideMenu();
    if (selectedIndex !== null && selectedSubmenu) {
      const selectedLabel = menuItems[selectedIndex].label;
      dispatch(setSort({label: selectedLabel, type: selectedSubmenu}));
      getLstData({label: selectedLabel, type: selectedSubmenu});
    }
  };
  const handleReset = () => {
    setSelectedIndex(null); // Reset selected index
    setSubmenuVisibleIndex(null); // Hide any open submenu
    setSelectedSubmenu(null); // Reset submenu selection
    hideMenu();
    dispatch(setSort({label: '', type: ''}));
    getLstData({label: '', type: ''});
  };

  useEffect(() => {
    handleReset();
  }, [selectedTab]);

  const IOS = Platform.OS === 'ios';

  const {width, height} = Dimensions.get('window');
  // Convert fixed values to relative dimensions
  const topPosition = IOS ? height * (260 / 910) : height * (250 / 910); // Assuming 812 is the design height
  const leftPosition =
    languageData === 'en' ? width * (175 / 410) : width * (150 / 410); // Assuming 375 is the design width

    const sortOptionsMap: Record<string, { asc: string; desc: string }> =
    selectedTab === 'Seeker'
      ? {
          Review: { asc: translate('high', ''), desc: translate('low', '') },
          Level: { asc: translate('high', ''), desc: translate('low', '') },
        }
      : {
          startDateTime: {
            asc: translate('Earliest', ''),
            desc: translate('Latest', ''),
          },
          'Date listed': {
            asc: translate('Newest', ''),
            desc: translate('Oldest', ''),
          },
          Level: { asc: translate('Highest', ''), desc: translate('Lowest', '') },
          Pay: { asc: translate('Highest', ''), desc: translate('Lowest', '') },
        };
  

  return (
    <SafeAreaView style={{flexDirection: 'column'}}>
      <View style={[styles.menuContainer, position]}>
        {visible && (
          <Modal
            transparent={true}
            visible={visible}
            onRequestClose={hideMenu}
            animationType="fade">
            <TouchableWithoutFeedback onPress={hideMenu}>
              <View style={{flex: 1}}>
                <View
                  style={[
                    styles?.modalVieWSty,
                    {
                      width:
                        languageData === 'en'
                          ? Dimensions.get('screen').width / 1.8
                          : Dimensions.get('screen').width / 1.7,
                      top: topPosition,
                      left: leftPosition,
                    },
                  ]}>
                  <ScrollView>
                    {menuItems.map((item, index) => (
                      <View key={item?.id || index}>
                        <TouchableOpacity
                          style={styles.menuItemRow}
                          onPress={() => handleRadioSelect(index)}>
                          <View style={styles.radioOuterCircle}>
                            {selectedIndex === index && (
                              <View style={styles.radioInnerCircle} />
                            )}
                          </View>
                          <View style={styles?.nameViewSty}>
                            <Text style={styles.menuItemText}>
                              {item?.name}
                            </Text>
                            <CustomIcon
                              name="ArrowUp" // Change to any icon name
                              size={24}
                              color={BaseColors.primary}
                              style={{
                                paddingRight: 5,
                                transform: [{rotate: '180deg'}], // Rotate the icon
                              }}
                            />
                          </View>
                        </TouchableOpacity>

                        {/* Expandable submenu logic */}
                        {submenuVisibleIndex === index && (
                          <View style={styles.submenuContainer}>
                            {['Asc', 'Desc'].map(option => {
                              const sortText =
                                sortOptionsMap[item.label]?.[
                                  option.toLowerCase()
                                ] || option;
                              return (
                                <TouchableOpacity
                                  key={option}
                                  style={[
                                    styles.submenuOption,
                                    {
                                      backgroundColor:
                                        selectedSubmenu === option
                                          ? BaseColors?.inactive
                                          : BaseColors.white,
                                    },
                                  ]}
                                  onPress={() => handleSubmenuSelect(option)}>
                                  <Text
                                    style={[
                                      styles.submenuText,
                                      selectedSubmenu === option &&
                                        styles.submenuSelectedText,
                                    ]}>
                                    {sortText}
                                  </Text>
                                </TouchableOpacity>
                              );
                            })}
                          </View>
                        )}
                      </View>
                    ))}
                    <View style={styles.okButtonContainer}>
                      <Button
                        style={styles?.btnSty}
                        type="outlined"
                        txtStyles={styles?.textoulineStyle}
                        onPress={handleReset}>
                        {translate('clears', '')}
                      </Button>
                      <Button
                        style={styles?.btnSty}
                        type="text"
                        txtStyles={styles?.textStyle}
                        onPress={handleOkPress}>
                        {translate('Apply', '')}
                      </Button>
                    </View>
                  </ScrollView>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </Modal>
        )}
      </View>
    </SafeAreaView>
  );
};

export default ModalComponant;
