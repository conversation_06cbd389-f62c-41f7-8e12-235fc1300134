/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Platform,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import Header from '@components/Header';
import { BaseColors } from '@config/theme';
import FastImage from 'react-native-fast-image';
import SwipeableFlatList from 'react-native-swipeable-list';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
 import Toast from 'react-native-simple-toast';
import { translate } from '@language/Translate';
import { useFocusEffect } from '@react-navigation/native';
import { getTimeAgo } from '@app/utils/CommonFunction';
import { Menu, MenuItem } from 'react-native-material-menu';
import { FontFamily } from '@config/typography';
import NoRecord from '@components/NoRecord';
import { isEmpty } from '@app/utils/lodashFactions';
import NotificationAction from '@redux/reducers/notification/actions';
import AnimatedView from '@components/AnimatedView';

export default function Notification({ navigation, route }: {navigation: any}) {
  const IOS = Platform.OS === 'ios';
  const [visible, setVisible] = useState<boolean>(false);
  const [unreadCount, setunreadCount] = useState<any>(0);

  const [state, setState] = useState({
    bottomLoading: false,
    refreshing: false,
    loader: false,
  });
  const { loader, refreshing, bottomLoading } = state;
  const [list, setList] = useState({
    data: [],
    pagination: { currentPage: 1, isMore: null },
  }); // Function to get list of seekers or employers

  const getList = async (page = 1, bottomLoader: boolean = false) => {
    if (loader) {return;} // Prevent duplicate calls
    if (bottomLoader) {
      setState((p: any) => ({ ...p, bottomLoading: true }));
    } else {
      setState((p: any) => ({ ...p, loader: true }));
    }
    const data: any = { page, limit: 10 };
    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.getNotification,
        method: 'GET',
        data: data,
      });
      if (resp?.data && resp?.status) {
        setList(p => ({
          ...p,
          data:
            page > 1
              ? [...list?.data, ...resp?.data?.notifications]
              : resp.data?.notifications || [],
          pagination: resp?.data?.pagination,
        }));
        setunreadCount(resp?.data?.unreadCount);
        NotificationAction.setBadgeCount(resp?.data?.unreadCount);
      } else {
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
      setState((p: any) => ({
        ...p,
        loader: false,
        refreshing: false,
        bottomLoading: false,
      }));
    } catch (e) {
      console.error('Error fetching notifications:', e);
    } finally {
      setState((p: any) => ({
        ...p,
        loader: false,
        refreshing: false,
        bottomLoading: false,
      }));
    }
  };

  const onRefresh = React.useCallback(() => {
    if (!loader) {
      setState((p: any) => ({ ...p, refreshing: true }));
      getList(1, bottomLoading);
      setState((p: any) => ({ ...p, refreshing: false }));
    }
  }, [loader]);

  const ListEndLoader = () => {
    if (!loader && bottomLoading) {
      return <ActivityIndicator color={BaseColors.primary} size={'small'} />;
    }
  };

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (
      isCloseToBottom &&
      list?.pagination?.isMore &&
      !loader &&
      !bottomLoading
    ) {
      getList(Number(list?.pagination?.currentPage) + 1, true);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      console.log('useFocuss effect called');
      getList(1, false);
      return () => {};
    }, []),
  );

  const readNotification = async (item: any) => {
    if (item?.isRead) {return;} // Prevent marking already read notifications
    try {
      const url = BaseSetting.endpoints.readNotification + `/${item?.id}`;
      const resp = await getApiData({
        endpoint: url,
        method: 'PUT',
      });
      if (resp?.status) {
        // Toast.show(resp?.message);
        const updatedData = list?.data.map(notification =>
          notification.id === item.id
            ? { ...notification, isRead: true } // Mark as read permanently
            : notification,
        );
        setList(prev => ({ ...prev, data: updatedData }));
      } else {
        // Toast.show(resp?.message);
      }
    } catch (err) {
      // Toast.show(err?.message || 'Something went wrong.', Toast.LONG);
    }
  };
  const readAllNotification = async () => {
    const data = {
      isRead: true, // Always mark all as read
    };
    try {
      const url = BaseSetting.endpoints.readAllNotification;
      const resp = await getApiData({
        endpoint: url,
        method: 'PUT',
        data: data,
      });
      if (resp?.status) {
        // Toast.show(resp?.message, Toast.BOTTOM);

        // Update the `isRead` status for all notifications
        const updatedData = list?.data.map(notification => ({
          ...notification,
          isRead: true, // Mark all as read
        }));

        setList(prev => ({ ...prev, data: updatedData }));
        SetReadAll(true); // Ensure the state reflects all notifications as read
      } else {
        // Toast.show(resp?.message);
      }
    } catch (err) {
      // Toast.show(err?.message || 'Something went wrong.', Toast.LONG);
    }
  };
  const deleteNotification = async (item: any, type: any) => {
    const data = {
      deleteAll: true,
    };
    const singleDelete = {
      id: item?.id,
    };
    try {
      const url = BaseSetting.endpoints.removeNotification;
      const resp = await getApiData({
        endpoint: url,
        method: 'DELETE',
        data: type === 'deleteAll' ? data : singleDelete,
      });
      if (resp?.status) {
        // Toast.show(resp?.message, Toast.BOTTOM);
        getList(1, false);
      } else {
        // Toast.show(resp?.message, Toast.BOTTOM);
      }
    } catch (err) {
      // Toast.show(err?.message || 'Something went wrong.', Toast.LONG);
    }
  };
  const hideMenu = () => setVisible(false);
  const showMenu = () => setVisible(true);

  const [expandedId, setExpandedId] = useState<any>(null); // State to track expanded item

  // Function to toggle "see more" / "see less"

  const toggleExpanded = (id: any) => {
    setExpandedId(expandedId === id ? null : id); // Toggle between expand and collapse
  };

  const [readAll, SetReadAll] = useState<Boolean>(false);

  return (
    <View style={[styles.container]}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        leftIcon="back-arrow"
        title={translate('notification', '')}
        onLeftPress={() => {
          navigation.goBack();
        }}
        rightIcons={
          true
            ? [
              {
                icon: 'ButtonMd',
                onPress: showMenu,
                wrapStyle: styles.msgIconStyle,
              },
            ]
            : null
        }
      />
      {isEmpty(list?.data) ? null : (
        <View style={{ position: 'absolute', right: 20, top: IOS ? 90 : 40 }}>
          <Menu visible={visible} onRequestClose={hideMenu}>
            <>
              {unreadCount > 0 && (
                <MenuItem
                  textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                  onPress={() => {
                    if (!readAll) {
                      setVisible(false);
                      readAllNotification();
                    }
                  }}>
                  {readAll
                    ? translate('readAllNoti', '')
                    : translate('readAllNoti', '')}
                </MenuItem>
              )}

              <MenuItem
                textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                onPress={() => {
                  setVisible(false);
                  deleteNotification('', 'deleteAll');
                }}>
                {translate('RemoveAllNoti', '')}
              </MenuItem>
            </>
          </Menu>
        </View>
      )}
      <ScrollView
        onScroll={handleScroll}
        // scrollEventThrottle={0.5}
        style={{
          ...styles.mainView,
          // marginBottom: Dimensions.get('screen').height / 150,
        }}
        nestedScrollEnabled={true}
        contentContainerStyle={{ flexGrow: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[BaseColors.primary]} // Customize refresh indicator color
            tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
          />
        }
        showsVerticalScrollIndicator={false}>
        <AnimatedView>
          {loader ? (
            <View style={styles.centerMain}>
              <ActivityIndicator color={BaseColors.primary} />
            </View>
          ) : (
            <SwipeableFlatList
              data={list?.data}
              keyExtractor={item => `${item.id}+1`}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                paddingHorizontal: 15,
                ...styles.mainViewSty,
              }}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  tintColor={BaseColors.primary}
                  colors={[BaseColors.primary]}
                  onRefresh={onRefresh}
                />
              }
              renderItem={({ item, index }) => {
                const isExpanded = expandedId === item.id; // Check if the current item is expanded

                return (
                  <>
                    <View style={styles.mainViewSty}>
                      <TouchableOpacity
                        activeOpacity={1}
                        onPress={() => {
                          readNotification(item);
                          if (
                            item?.action === 'job_detail' ||
                            'employer_job_detail'
                          ) {
                            navigation.navigate('JobApplicant', {
                              notificationId: item?.parsedData?.jobId,
                              type: 'notification',
                            });
                          } else {
                            null;
                          }
                        }}
                        style={{
                          backgroundColor: BaseColors.white,
                          paddingHorizontal: 0,
                          flex: 1,
                          alignSelf: 'center',
                          justifyContent: 'center',
                          paddingBottom: 10,
                          paddingTop: 20,
                          flexDirection: 'row',
                        }}>
                        {item?.isRead === true ? null : (
                          <View style={styles?.dotView} />
                        )}
                        <View style={styles.imgView}>
                          <FastImage
                            source={{ uri: item?.image }}
                            resizeMode={FastImage.resizeMode.cover}
                            style={{ width: '100%', height: '100%' }}
                          />
                        </View>
                        <View>
                          <View style={styles?.descriptionViewSty}>
                            <Text style={styles?.titleSty} numberOfLines={2}>
                              {item?.title}
                            </Text>
                            <Text
                              style={styles?.desSty}
                              numberOfLines={isExpanded ? 0 : 2} // Expand/collapse based on state
                            >
                              {item?.message}
                            </Text>
                            {/* Toggle button */}
                            {item?.message?.length > 75 && (
                              <TouchableOpacity
                                onPress={() => toggleExpanded(item.id)}>
                                <Text style={styles?.seeMoreLessSty}>
                                  {isExpanded
                                    ? translate('seeLess', '')
                                    : translate('seeMore', '')}
                                </Text>
                              </TouchableOpacity>
                            )}
                          </View>
                          <View style={styles.timeView}>
                            <Text style={styles?.timetxtSty}>
                              {getTimeAgo(item?.createdAt)}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>
                    {index !== list.data.length - 1 && (
                      <View style={styles.underLine} />
                    )}
                  </>
                );
              }}
              maxSwipeDistance={70}
              shouldBounceOnMount={true}
              renderQuickActions={({ item }) => (
                <View style={styles?.dltView}>
                  <TouchableOpacity
                    activeOpacity={1}
                    style={styles.dltStyView}
                    onPress={() => {
                      deleteNotification(item, '');
                    }}>
                    <View style={{ flexDirection: 'row' }}>
                      <Text style={styles?.dltTxtSty}>
                        {translate('delete', '')}
                      </Text>
                      {/* <MIcon name="delete" size={27} color={BaseColors.primary} /> */}
                    </View>
                  </TouchableOpacity>
                </View>
              )}
              ListFooterComponent={ListEndLoader}
              onEndReachedThreshold={0.01}
              ListEmptyComponent={
                <View style={styles.noDataSty}>
                  <NoRecord
                    title={'noNotification'}
                    type="employer"
                    description="exploreNoNotification"
                    iconName="employer"
                  />
                </View>
              }
            />
          )}
        </AnimatedView>
      </ScrollView>
    </View>
  );
}
