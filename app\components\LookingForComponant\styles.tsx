import { BaseColors } from '@config/theme';
import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  container: {
    marginHorizontal: 10,
  },
  mainView: {
    padding: 0,
    // borderWidth: 1,
    marginTop: 5,
    borderColor: BaseColors.primary,
    flexDirection: 'row',
    paddingVertical: 15,
    borderRadius: 10,
  },
  boxView: {
    flexDirection: 'column',
    alignItems: 'center',
    height: 'auto',
    width: 65,
  },
  mainBoxView: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: "space-between",
  },
  lineView: {
    borderWidth: 0.2,
    marginHorizontal: 4.5,
    borderColor: BaseColors.textColor,
  },
  titleSty: {
    color: BaseColors.primary,
    fontSize: 12,
    justifyContent: 'center',
    fontWeight: '600',
  },
});
