import React, {useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
} from 'react-native';
import EIcon from 'react-native-vector-icons/Entypo'; // Replace with your specific icon library if different
import styles from './styles';
import {CustomIcon} from '@config/LoadIcons';
import {translate} from '@language/Translate';
import {BaseColors} from '@config/theme';
import ImagePicker from 'react-native-image-crop-picker';
import {chatFilesVal, handleFilePick} from '@app/utils/CommonFunction';
import Icon from 'react-native-vector-icons/Fontisto';
import FIcon from 'react-native-vector-icons/FontAwesome';
import ActionSheet from 'react-native-actionsheet';
import RBSheet from 'react-native-raw-bottom-sheet';

const CvComponent = ({
  cvFile,
  cvError,
  loader,
  setLoader = () =>{},
  Review,
  handleCvFileSelect,
  deleteFile,
  setCvFile,
  ActionSheetRefIOS,
  ActionSheetRef,
  handlePhotoSelect,
}: any) => {
  const IOS = Platform.OS === 'ios';
  const CANCEL_INDEX = 3;
  const DESTRUCTIVE_INDEX = 0;

  function showActionSheet() {
    if (IOS) {
      ActionSheetRefIOS.current.open();
    } else {
      ActionSheetRef.current.show();
    }
  }
  const openCamera = async () => {
    try {
      const image: any = await ImagePicker.openCamera({
        cropping: true,
      });

      const fType = image?.mime || '';
      const isValidFile = chatFilesVal(fType, image.size);

      if (isValidFile) {
        setLoader(true);
        // Pass the selected file and type to handleFilePick
        const fileRes = await handleFilePick(image, 'image');
        setCvFile(fileRes?.data?.filePath);
        if (IOS) {
          ActionSheetRefIOS.current.close();
        } else {
          ActionSheetRef.current.hide();
        }
        setLoader(false);
      } else {
        setTimeout(() => {
          // Toast.show(translate('appliedValidSizeFile', ''), Toast.BOTTOM);
        }, 50);
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };

  const options = [
    <TouchableOpacity
      onPress={() => handleCvFileSelect()}
      key={`gallery-option-${1}`}
      style={[styles.optionsContainer, {marginTop: IOS ? 15 : 0}]}>
      <Icon
        name="file-1"
        size={22}
        color={BaseColors.primary}
        style={{paddingRight: 5}}
      />
      <Text
        style={{
          marginLeft: 15,
          color: BaseColors.primary,
      
        }}>
        {translate('Files', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => openCamera()}
      key={`camera-option-${2}`}
      style={[styles.optionsContainer, {paddingVertical: 10, marginLeft: 6}]}>
      <FIcon
        name="camera"
        size={16}
        color={BaseColors.primary}
        style={{paddingLeft: 4}}
      />
      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Camera', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => handlePhotoSelect()}
      key={`camera-option-${3}`}
      style={[
        styles.optionsContainer,
        {paddingVertical: IOS ? 0 : 10, marginLeft: 6 ,},
      ]}>
      <FIcon name="photo" size={18} color={BaseColors.primary} />
      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Photos', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => {
        if (IOS) {
          ActionSheetRefIOS.current.close();
        } else {
          ActionSheetRef.current.hide();
        }
      }}
      key={`cancel-option-${4}`}
      style={[
        styles.optionsContainer,
        {
          paddingVertical: 10,
          marginHorizontal: IOS ? 0 : 20,
          borderTopWidth: IOS ? 3 : 0,
          borderTopColor: BaseColors.textInput,
        },
      ]}>
      <EIcon name="cross" size={18} color={BaseColors.primary} />

      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Cancel', '')}
      </Text>
    </TouchableOpacity>,
  ];

  function doAction(index: any) {
    if (index === 0) {
      handleCvFileSelect(index);
    } else if (index === 1) {
      openCamera();
    }
  }

  return (
    <View style={styles.fileContainer}>
      <Text style={styles.resumeText}>{translate('CV/Resume', '')}</Text>

      {!cvFile && (
        <Text style={styles.uploadCvText}>{translate('uploadCv', '')}</Text>
      )}

      {cvFile ? (
        <View style={styles.cvFileContainer}>
          <View style={styles.fileInfoContainer}>
            <View style={styles.iconSty}>
              <CustomIcon
                name="document"
                size={25}
                color={styles.fileIconColor.color}
              />
            </View>

            <View style={styles.fileDetailsContainer}>
              <Text numberOfLines={1} style={styles.fileNameText}>
                {typeof cvFile === 'string' && cvFile.includes('http')
                  ? cvFile.split('/').pop()
                  : cvFile?.fileName || '-'}
              </Text>

              {cvFile?.fileSize && (
                <Text style={styles.fileSizeText}>Size: {cvFile.fileSize}</Text>
              )}
            </View>
          </View>

          <TouchableOpacity
            onPress={() => {
              deleteFile('cv');
            }}
            style={styles.removeFileButton}>
            {loader === 'cv' ? (
              <ActivityIndicator color={BaseColors.primary} />
            ) : (
              <EIcon name="cross" size={20} color={BaseColors.textGrey} />
            )}
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.chooseFileContainer}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={Review === 'reviewType' ? null : showActionSheet}>
            {loader ? (
              <ActivityIndicator color={BaseColors.primary} />
            ) : (
              <View>
                <View style={styles.iconViewSty}>
                  <CustomIcon
                    name="Upload"
                    size={20}
                    color={BaseColors.primary}
                  />
                </View>
                <Text style={styles.chooseFileText}>
                  {translate('ChooseFile', '')}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      )}

      {!cvFile && (
        <View style={styles.fileFormatContainer}>
          <Text style={styles.acceptedFileText}>
            {translate('acceptedFile', '')}
          </Text>
          <Text style={styles.maxSizeText}>{translate('maxSize', '')}</Text>
        </View>
      )}

      {cvError && <Text style={styles.errorText}>{cvError}</Text>}

      <ActionSheet
        ref={ActionSheetRef}
        options={options}
        cancelButtonIndex={CANCEL_INDEX}
        destructiveButtonIndex={DESTRUCTIVE_INDEX}
        onPress={(index: any) => doAction(index)}
      />
      <RBSheet
        ref={ActionSheetRefIOS}
        closeOnDragDown={true}
        closeOnPressMask={true}
        dragFromTopOnly={true}
        height={180}
        customStyles={{
          draggableIcon: {
            width: 50,
            marginTop: 30,
          },
          container: {
            backgroundColor: '#FFF',
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
          },
        }}>
        <View>
          {options?.map(item => {
            return item;
          })}
        </View>
      </RBSheet>
    </View>
  );
};
export default CvComponent;
