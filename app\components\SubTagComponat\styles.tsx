import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { Dimensions, Platform, StyleSheet } from 'react-native';

const { width, height } = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

const dimensions = {
  imgHeight: height / 2.3,
  imgWidth: width / 1.35,
  dotWidth: width * 0.09,
  dotHeight: height * 0.005,
  titleFontSize: 30,
  bodyFontSize: 16,
};

export default StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  skipButton: {
    marginTop: Platform.OS === 'ios' ? 20 : 30,
    marginRight: 16,
    alignSelf: 'flex-end',
    backgroundColor: BaseColors.activeColor,
    padding: 7,
    borderRadius: 6,
  },
  swiperContainer: {
    backgroundColor: BaseColors.white,
  },
  container: {
    flex: 1,
  },
  imgContainer: {
    justifyContent: 'center',
    alignSelf: 'center',
  },
  imgView: {
    height: dimensions.imgHeight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  introImage: {
    width: dimensions.imgWidth,
    height: dimensions.imgHeight,
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    position: 'absolute',
    top: height / 1.9,
    alignSelf: 'center',
  },
  dot: {
    width: dimensions.dotWidth,
    height: dimensions.dotHeight,
    borderRadius: 7,
    marginHorizontal: 3,
  },
  textContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
    alignSelf: 'center',
    position: 'absolute',
    top: height / 1.8,
  },
  titleText: {
    color: BaseColors.textColor,
    fontSize: dimensions.titleFontSize,
    fontWeight: '600',
    textAlign: 'center',
  },
  bodyText: {
    color: BaseColors.discriptionTextColor,
    fontSize: dimensions.bodyFontSize,
    fontWeight: '400',
    textAlign: 'center',
    paddingTop: 20,
  },
  buttonContainer: {
    marginBottom: Dimensions.get('screen').height / 40,
    marginTop: 20,
    marginHorizontal: 30,
  },
  nextButton: {
    backgroundColor: BaseColors.primary,
  },
  rbSheetCustomStyles: {
    wrapper: { backgroundColor: 'hsla(360, 20%,2%, 0.6)' },
    draggableIcon: { backgroundColor: '#000' },
    container: {
      backgroundColor: 'white',
      borderTopRightRadius: 20,
      borderTopLeftRadius: 20,
      height: IOS ? height / 1.3 : height / 1.1,
    },
  },
  rbSheetCustomDltStyles: {
    wrapper: { backgroundColor: 'hsla(360, 20%,2%, 0.6)' },
    draggableIcon: { backgroundColor: '#000' },
    container: {
      backgroundColor: 'white',
      borderTopRightRadius: 20,
      borderTopLeftRadius: 20,
      height: IOS ? height / 5 : height / 3.5,
    },
  },
  languageContainer: {
    marginTop: Dimensions.get('screen').height / 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 28,
    marginHorizontal: 20,
  },
  languageTitle: {
    fontSize: 30,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
    textTransform: "capitalize"

  },
  languageOption: {
    height: height / 16,
    // marginBottom: 15,
    borderWidth: 1.7,
    justifyContent: 'center',
    // alignItems: 'center',
    width: width / 1.2,
    borderRadius: 6,
    marginTop: 20,
  },
  languageText: {
    fontSize: 17,
    color: BaseColors.textColor,
    // paddingHorizontal: 20,
    fontFamily: FontFamily.OpenSansRegular,
  },
  iconPadding: {
    position: 'absolute',
    right: 15,
  },
  languageButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: Dimensions.get('screen').height / 30,
    width: '100%',
    paddingHorizontal: 15,
  },
  languageModalView: {
    flexDirection: 'row',
    width: '40%',
    alignItems: 'center',
  },
  imageView: {
    width: 70,
    height: 27,
  },
  descriptionView: {
    marginTop: 15,
  },
  descriptionTxtSty: {
    fontSize: 14,
    color: BaseColors.inputColor,
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // backgroundColor: BaseColors?.lightBlack,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    // elevation: 2,
    borderBottomWidth: 0.8,
    borderBottomColor: BaseColors.borderColor,
  },
  sectionTitle: {
    fontSize: 16,
    color: BaseColors?.primary,
    fontFamily: FontFamily?.OpenSansMedium,
    paddingBottom: IOS ? 0 : 5,
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    marginLeft: 16,
    backgroundColor: BaseColors.white,
    borderBottomWidth: 0.5,
    borderBottomColor: BaseColors.primary,
  },
  optionText: {
    marginLeft: 10,
    fontSize: 14,
    color: BaseColors?.lightGrey,
    fontFamily: FontFamily?.OpenSansSemiBold,
    paddingBottom: IOS ? 0 : 3,
  },

  selectedOptionContainer: {
    backgroundColor: '#e8eef5', //BaseColors?.blueLightTxt, // Light blue background for selected state
    flex: 1,
    // justifyContent: 'space-between',
  },
  checkboxContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  uncheckedBox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: BaseColors.inputColor,
    borderRadius: 5,
  },
  checkedBox: {
    width: 20,
    height: 20,
    backgroundColor: BaseColors.white,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColors.primary,
    borderWidth: 1.5,
  },

  selectedOptionText: {
    color: '#1D559F', // Blue text color for selected state
    fontWeight: '600',
  },
  fixedButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10,
    backgroundColor: BaseColors.white,
    borderTopWidth: 1,
    borderTopColor: '#EEE',
    position: 'absolute',
    bottom: 20,
    width: '100%',
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '85%',
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BaseColors.whiteColor,
    borderRadius: 10,
    // paddingHorizontal: IOS ? 10 : 6,
    // paddingVertical: IOS ? 6 : 4,
    borderWidth: 1,
    borderColor: BaseColors.primary,
    margin: 5,
    justifyContent: 'center',
    textAlign: 'center',
    alignSelf: 'center',
    paddingHorizontal: 5,
    paddingVertical: 8,

  },
  tagText: {
    color: BaseColors.primary,
    marginRight: 5,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 13,
    paddingBottom: IOS ? 0 : 3,
  },
  iconStyle: { paddingTop: 2 },
  placeholderText: {
    color: BaseColors?.dividerColor,
    fontSize: 14,
    margin: 5,
    fontFamily: FontFamily.OpenSansRegular,
    textTransform: "capitalize"

  },
  suggestionList: {
    // marginTop: IOS ? 7 : 1,
    // marginBottom: IOS ? 0 : 5,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: BaseColors.primary,
    borderRadius: 10,
    // marginHorizontal: 3,
    alignItems: 'center',
    // paddingHorizontal: 10,
    // paddingVertical: IOS ? 5 : 0,
    marginLeft: 10,
    marginVertical: 5,
    paddingRight: 5,
    paddingVertical: 3
  },
  suggestionsText: {
    fontSize: 14,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    paddingHorizontal: 5,
    paddingVertical: 5,
    textTransform: 'capitalize',
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 5,
    marginLeft: 5,
  },
  seeMoreButton: {
    marginTop: 10,
    alignItems: 'center',
  },
  seeMoreText: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansBold,
    fontSize: 14,
  },
});
