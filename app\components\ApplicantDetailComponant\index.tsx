/* eslint-disable react-native/no-inline-styles */
import React, { useState } from 'react';
import Button from '@components/UI/Button';
import { CustomIcon } from '@config/LoadIcons';
import { BaseColors } from '@config/theme';
import socketAction from '@redux/reducers/socket/actions';
import IonIcons from 'react-native-vector-icons/Ionicons';
import {
  Dimensions,
  LayoutChangeEvent,
  Linking,
  Platform,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import SwitchComponent from '@components/SwitchComponant';
import styles from './styles';
import {
  capitalizeFirstLetter,
  getBadgeImage,
  getTimeAgo,
  handleAvailabilityUpdate,
} from '@app/utils/CommonFunction';
import MyHarbor from '@screens/MyHarbor';
import { translate } from '@language/Translate';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import authActions from '@redux/reducers/auth/actions';
import { Images } from '@config/images';
import LicenseandCertificate from '@components/LicenseandCertificte';
import WorkExperinceAndCv from '@components/WorkExperinceAndCv';
import ReviewRatingComponant from '@components/ReviewRatingComponant';
import { isEmpty } from 'lodash-es';
import { useAppDispatch, useRedux } from '@components/UseRedux';

const { emit, setSelectedRoom } = socketAction;

export default function ApplicantDetailComponant({
  navigation,
  data,
  applicantData,
  job,
  type,
  jobList,
  handleClick,
  name,
  lastname,
  profileImage,
  location,
  about,
  selectedTags,
  reviewType,
  id,
  isAvailable,
  setIsAvailable,
  experienceList,
  setExperienceList,
  certificationList,
  setCertificationList,
  cvFile,
  deleteFile,
  loader,
  Review,
  licenseFiles,
  setPreCertificate,
  preCertificate,
  setPreExperince,
  preExperince,
  userDetails,
  onRefresh,
  refreshing,
  seekerProfile,
}: {
  navigation?: any;
  data?: any;
  applicantData?: any;
  job?: any;
  userId?: any;
  type?: any;
  jobList?: any;
  handleClick?: any;
  name?: any;
  lastname?: any;
  profileImage?: any;
  location?: any;
  about?: any;
  selectedTags?: any;
  reviewType?: any;
  id?: any;
  isAvailable?: any;
  setIsAvailable?: any;
  experienceList: any;
  setExperienceList: any;
  certificationList: any;
  setCertificationList: any;
  cvFile: any;
  deleteFile: any;
  loader: any;
  Review: any;
  licenseFiles: any;
  setPreCertificate: any;
  preCertificate: any;
  setPreExperince: any;
  preExperince: any;
  userDetails: any;
  onRefresh: any;
  refreshing: any;
  seekerProfile: any;
}) {
  const [imageError, setImageError] = useState(false);
  const { width } = Dimensions.get('window');
  const { useAppSelector } = useRedux();
  const { userData } = useAppSelector((state: any) => state.auth);
  const [showAllSkills, setShowAllSkills] = useState<boolean>(false);
  const IOS = Platform.OS === 'ios';
  const [isTruncated, setIsTruncated] = useState(true); // state to manage truncation
  const [roomId, setRoomId] = useState('');
  const dispatch = useAppDispatch();
  const USER_VERIFIED =
    userData?.personaStatus === 'approved' || applicantData?.personaStatus === 'approved'
      ? true
      : false;

  const skills =
    data?.approvedApplicant?.skills ||
    jobList?.approvedApplicant?.skills ||
    data?.skills ||
    selectedTags;
  const aboutText =
    jobList?.approvedApplicant?.about ||
    data?.approvedApplicant?.about ||
    data?.about ||
    about;

  const firstName =
    data?.approvedApplicant?.firstName ||
    jobList?.approvedApplicant?.firstName ||
    data?.firstName ||
    name;
  const lastName =
    data?.approvedApplicant?.lastName ||
    jobList?.approvedApplicant?.lastName ||
    data?.lastName ||
    lastname;
  const locationText =
    data?.approvedApplicant?.location ||
    jobList?.approvedApplicant?.location ||
    jobList?.location ||
    (data?.shortAddress === null
      ? data?.location
      : data?.shortAddress || userData?.shortAddress || location?.description);
  const avgRating =
    data?.approvedApplicant?.averageRate ||
    jobList?.approvedApplicant?.averageRate ||
    data?.averageRate;
  const avgRatingCount =
    data?.approvedApplicant?.ratingCount ||
    jobList?.approvedApplicant?.ratingCount ||
    data?.ratingCount;

  const profilePhoto =
    data?.approvedApplicant?.profilePhoto ||
    jobList?.approvedApplicant?.profilePhoto ||
    data?.profilePhoto ||
    profileImage;

  const ratings = reviewType === 'review' ? userData?.averageRate : avgRating;
  const [harborHistoryList, setHarborHistoryList] = useState([]);
  const [expandedReviews, setExpandedReviews] = useState<{
    [key: string]: boolean;
  }>({});
  const [isReviewTruncated, setIsReviewTruncated] = useState<{
    [key: string]: boolean;
  }>({});
  const lineHeight = 20; // Replace this with your actual line height from styles

  const toggleExpansion = (reviewId: string | number) => {
    setExpandedReviews(prevState => ({
      ...prevState,
      [reviewId]: !prevState[reviewId],
    }));
  };
  const handleLayout = (
    event: LayoutChangeEvent,
    reviewId: string | number,
  ) => {
    const { height } = event.nativeEvent.layout;
    const maxHeight = 3 * lineHeight; // Height for 3 lines

    setIsReviewTruncated(prevState => ({
      ...prevState,
      [reviewId]: height > maxHeight, // Check if height exceeds 3 lines
    }));
  };
  const chatButtonFn = type => {
    const options = {
      userId: job?.id && job?.userId ? job?.userId || '' : userData?.id,
      applicantId:
        applicantData?.approvedApplicant?.userId || applicantData?.id,
      jobId: job?.id && job?.userId ? job?.id : null,
    };
    dispatch(
      emit('create_room', options, (res: any) => {
        if (type === 'hireAgain') {
          setRoomId(res?.roomId);
          return;
        }
        console.log('Read=====>', res, options);
        const op = {
          userId: userData?.id || '',
          jobId: job?.id && job?.userId ? job?.id : null,
          roomId: res?.roomId,
        };
        dispatch(
          emit('get_single_chat', op, (singleRes: any) => {
            console.log('singleRes=====>', singleRes);
            if (singleRes?.status) {
              dispatch(
                setSelectedRoom({
                  ...res,
                  ...singleRes?.data,
                }),
              );
              navigation.navigate('ChatDetails', {
                userInfo: {
                  ...res,
                  ...singleRes?.data,
                },
              });
            }
          }) as any,
        );
      }) as any,
    );
  };
  const handleAvailable = (val: any) => {
    handleAvailabilityUpdate(
      'isAvailable', // key
      val, // value
      res => {
        dispatch(
          authActions.setUserData({
            ...userData,
            isAvailable: val,
          }) as any,
        );
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      },
      err => {
        // Toast.show(err?.message || translate('err', ''), Toast.BOTTOM);
      },
    );
  };

  const [isTextVisible, setIsTextVisible] = useState(false);

  const toggleTextVisibility = () => {
    setIsTextVisible(!isTextVisible);
  };

  const isHistoryAvailable =
    type === 'OWN' || reviewType === 'review'
      ? userData?.isProfileSet
      : data?.isProfileSet || job?.isProfileSet;

  // Function to toggle between 'See More' and 'See Less'
  const toggleText = () => {
    setIsTruncated(!isTruncated);
  };

  // Determine the displayed text based on the truncation state
  const displayedText = isTruncated
    ? `${aboutText?.slice(0, 80)}...`
    : aboutText;

  const openUserReview = reviewType === 'review' ? userData : userDetails;
  const openMap = () => {
    const locationdata = locationText; // Change this to any location you want
    const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(locationdata)}`;

    Linking.openURL(url).catch((err) => console.error('Failed to open map', err));
  };

  return (
    <>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[BaseColors.primary]} // Customize refresh indicator color
            tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
          />
        }
        style={styles.container}>
        <View style={styles.profileContainer}>
          {/* Profile Image */}
          <TouchableOpacity
            style={styles.profileImageWrapper}
            onPress={() => {
              navigation.navigate('GalleryView', {
                images: [
                  data?.approvedApplicant?.profilePhoto ||
                    jobList?.approvedApplicant?.profilePhoto ||
                    data?.profilePhoto ||
                    profileImage,
                ],
                index: 0,
              });
            }}>
            <FastImage
              source={
                profilePhoto
                  ? { uri: profilePhoto }
                  : (userDetails?.gender || userData?.gender) === 'female'
                  ? Images.female
                  : Images.user
              } // Ensures null is replaced
              style={styles.profileImage}
            />

            {USER_VERIFIED && (
              <View
                style={{
                  height: 20,
                  width: 20,
                  position: 'absolute',
                  bottom: 0,
                  right: 0,
                }}>
                <FastImage
                  source={Images.verified}
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: width * 0.0125, // Assuming 5 is about 1.25% of the width
                  }}
                  resizeMode={FastImage.resizeMode.cover} // Optional: Set resize mode if needed
                />
              </View>
            )}
          </TouchableOpacity>
          <View style={styles.contentCard}>
            <View style={styles?.mainNameView}>
              <View style={styles.nameView}>
                {/* Name and Details */}
                <Text style={styles.name} numberOfLines={2}>
                  {firstName || '-'}{' '}
                  {capitalizeFirstLetter(lastName?.charAt(0))}
                  {/* {capitalizeFirstLetter(lastName?.charAt(0))}. */}
                </Text>
                {userData?.isAvailable || applicantData?.isAvailable ? (
                  <View
                    style={{
                      width: 8,
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: BaseColors.green,
                      marginTop: IOS ? 0 : 5,
                      marginLeft: 5,
                    }}
                  />
                ) : null}

                <View style={styles.imageView}>
                  <FastImage
                    source={getBadgeImage(
                      type === 'OWN'
                        ? userData?.currentBadgeName
                        : data?.currentBadgeName,
                    )}
                    style={styles.medalIcon}
                    resizeMode="contain"
                  />
                </View>
              </View>
            </View>

            {data?.location || location ? (
              <TouchableOpacity  onPress={openMap} style={styles.locationView}>
                <CustomIcon
                  name="jobLocation"
                  size={16}
                  color={BaseColors.primary}
                />
                <Text numberOfLines={2} style={styles.location}>
                  {locationText || '-'}
                </Text>
              </TouchableOpacity>
            ) : null}
            {/* {ratings ? ( */}
            <View style={{ alignSelf: 'center' }}>
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  navigation.navigate('ReviewList', {
                    type: reviewType === 'review' ? 'OWN' : '',
                    id: id,
                  });
                }}
                style={styles.reviewViewSty}>
                <CustomIcon name="fillStar" color={BaseColors.starColor} />
                <Text style={styles.ratingTxtSty}>
                  {Number(
                    reviewType === 'review'
                      ? userData?.averageRate
                        ? userData?.averageRate
                        : '0'
                      : avgRating,
                  ).toFixed(1) || '0'}
                </Text>

                {/* {userData?.ratingCount ? ( */}
                <Text>
                  (
                  {reviewType === 'review'
                    ? userData?.ratingCount || '0'
                    : avgRatingCount || '0'}
                  )
                </Text>
                {/* ) : null} */}
              </TouchableOpacity>
            </View>
            {/* ) : null} */}
          </View>
        </View>
        {skills ? (
          <View
            style={[
              styles.skillsWrapper,
              { justifyContent: skills.length === 1 ? 'center' : 'flex-start' },
            ]}>
            {(showAllSkills ? skills : skills.slice(0, 3))?.map(
              (skill: any) => (
                <View key={`${skill?.id}+1`} style={styles.skillView}>
                  <Text style={styles.skillText}>{skill.name}</Text>
                </View>
              ),
            )}
            {skills?.length > 3 && (
              <TouchableOpacity
                onPress={() => setShowAllSkills(!showAllSkills)}>
                <Text style={styles.seeMoreTxtSty}>
                  {/* {showAllSkills ? 'See Less' : 'See More'} */}
                  {showAllSkills ? '' : '+ '}
                  {skills?.length - 3}{' '}
                  {showAllSkills ? 'less' : translate('more')}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        ) : null}

        <View>
          {aboutText ? (
            <Text style={[styles.descriptionTxt, { marginBottom: 5 }]}>
              {capitalizeFirstLetter(displayedText)}
              {aboutText.length > 80 && (
                <TouchableOpacity style={{}} onPress={toggleText}>
                  <Text style={[styles.toggleText, { top: 3 }]}>
                    {isTruncated ? ' See More' : ' See Less'}
                  </Text>
                </TouchableOpacity>
              )}
            </Text>
          ) : null}
        </View>
        {reviewType === 'review' ? (
          <>
            <View style={styles.switchView}>
              <View style={styles?.switchSty}>
                <SwitchComponent
                  onValueChange={(p: any) => {
                    setIsAvailable(p);
                    handleAvailable(p);
                  }}
                  type="review"
                  value={isAvailable}
                  disabled={userData?.isOccupied === true ? true : false}
                />
                <Text style={[styles.description, styles.available]}>
                  {'I am available'}
                </Text>
                <IonIcons
                  name="information-circle-outline"
                  size={20}
                  color={BaseColors.primary}
                  style={styles.icon}
                  onPress={toggleTextVisibility}
                />
              </View>

              {isTextVisible && (
                <Text style={styles?.availableTxtSty}>
                  {translate('isAvailableDesc', '')}
                </Text>
              )}
              {userData?.isOccupied ? (
                <Text style={styles?.availableTxtSty}>
                  {translate('occupiedTxt', '')}
                </Text>
              ) : null}
            </View>
          </>
        ) : null}

        {reviewType === 'review' ||
        type === 'seekerChat' ||
        type === 'viewEmployer' ? null : (
          <>
            {jobList?.approvedApplicant?.status === 'Approved' ||
            jobList?.approvedApplicant?.status === 'Completed' ||
            jobList?.isApplied?.status === 'Approved' ||
            type === 'seeker' ? null : (
              <>
                {applicantData?.userJob?.status !== 'Declined' &&
                data?.status !== 'pending' ? (
                  <View style={styles.applicationsBtns}>
                    <Button
                      onPress={() => {
                        handleClick('Approved');
                      }}
                      // loading={}
                      style={styles.approveBtn}
                            txtStyles={{ fontSize: 12 }}
                      type="text">
                      {translate("Approve","")}
                    </Button>

                    <Button
                      onPress={() => {
                        handleClick('Declined');
                      }}
                      // loading={}
                      style={styles.declineBtn}
                            txtStyles={{ color: BaseColors.btnRed, fontSize: 12 }}
                      type="outlined">
                      {translate("Decline","")}
                    </Button>
                  </View>
                ) : null}
                {data?.userJob?.status === 'Declined' ? (
                  <View>
                    <Button
                      style={{
                        ...styles.declineBtn,
                        width: '100%',
                        backgroundColor: '#f8bdbd',
                      }}
                      onPress={() => {
                        handleClick('Declined');
                      }}
                      // loading={}
                          txtStyles={{ color: BaseColors.btnRed, fontSize: 12 }}
                      type="outlined">
                      Declined
                    </Button>
                  </View>
                ) : null}
              </>
            )}
          </>
        )}

        {/* {aboutText ? (
          <Section style={{marginTop: 10}} title={translate('about')}>
            <Text style={[styles.description]}>{aboutText}</Text>
          </Section>
        ) : null} */}
        {/* Experience Section */}

        <View style={styles?.marginTop}>
          <WorkExperinceAndCv
            experienceList={experienceList}
            setExperienceList={setExperienceList}
            reviewType={reviewType}
            preExperince={preExperince}
            setPreExperince={setPreExperince}
            userDetails={userDetails}
            navigation={navigation}
            cvFile={cvFile}
          />

          {/* <ExperienceCard
            experienceList={experienceList}
            setExperienceList={setExperienceList}
            reviewType={reviewType}
            preExperince={preExperince}
            setPreExperince={setPreExperince}
            userDetails={userDetails}
          /> */}
        </View>
        <View style={styles?.marginTop}>
          <LicenseandCertificate
            certificationList={certificationList}
            setCertificationList={setCertificationList}
            reviewType={reviewType}
            setPreCertificate={setPreCertificate}
            preCertificate={preCertificate}
            navigation={navigation}
            userDetails={userDetails}
            licenseFiles={licenseFiles}
          />
        </View>
        {/* <CertificateCard
          certificationList={certificationList}
          setCertificationList={setCertificationList}
          reviewType={reviewType}
          setPreCertificate={setPreCertificate}
          preCertificate={preCertificate}
          navigation={navigation}
          userDetails={userDetails}
        /> */}
        {/* <CvFileComponent
          cvFile={cvFile}
          deleteFile={deleteFile}
          loader={loader}
          reviewType={reviewType}
          navigation={navigation}
        /> */}
        {/* <LicenseFileList
          licenseFiles={licenseFiles}
          deleteFile={deleteFile}
          loader={loader}
          reviewType={reviewType}
          navigation={navigation}
          userDetails={userDetails}
        /> */}

        {/* Project Worked Upon Section */}

        <Section title={translate('harborHistory')}>
          <MyHarbor
            navigation={navigation}
            type={'short-history'}
            id={
              type === 'OWN' || reviewType === 'review'
                ? userData?.id
                : data?.id || job?.userId || id
            }
            harborHistoryList={harborHistoryList}
            setHarborHistoryList={setHarborHistoryList}
          />
          <View style={styles.underlineViewSty} />
          {harborHistoryList && harborHistoryList?.length > 0 ? (
            <Text
              style={styles.moreLink}
              onPress={() => {
                navigation.navigate('HarborHistory', {
                  id:
                    type === 'OWN' || reviewType === 'review'
                      ? userData?.id
                      : data?.id || job?.userId || id,
                });
              }}>
              {translate('seeMore')}
            </Text>
          ) : null}
        </Section>

        {!isEmpty(openUserReview?.reviews?.items) ? (
          <View style={styles?.revieeDataSty}>
            <>
              <View
                style={{
                  borderBottomWidth: 1,
                  borderColor: BaseColors?.borderColor,
                  marginBottom: 5,
                }}>
                <Text style={styles.sectionTitle}>
                  {translate('userReview')}
                </Text>
              </View>
              {(openUserReview?.reviews?.items).map(
                (review: any, index: number) => {
                  const reviewId = review.id || index;
                  const isExpanded = expandedReviews[reviewId];
                  const showSeeMore = isReviewTruncated[reviewId];

                  return (
                    <>
                      <ReviewRatingComponant
                        key={reviewId} // Added 'key' prop to prevent React warnings
                        review={review}
                        reviewId={reviewId}
                        isExpanded={isExpanded}
                        showSeeMore={showSeeMore}
                        toggleExpansion={toggleExpansion}
                        handleLayout={handleLayout}
                        getTimeAgo={getTimeAgo}
                        type="userDetail"
                      />
                    </>
                  );
                },
              )}

              {openUserReview?.reviews?.isMore ? (
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate('ReviewList', {
                      type: reviewType === 'review' ? 'OWN' : '',
                      id: id,
                    });
                  }}
                  style={styles?.seeMoreView}>
                  <Text style={styles?.moreSty}>
                    {translate('seeMore', '')}
                  </Text>
                </TouchableOpacity>
              ) : null}
            </>
          </View>
        ) : null}
      </ScrollView>
      {reviewType === 'review' ||
      type === 'viewEmployer' ||
      type === 'seekerChat' ? null : (
        <>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            {userDetails?.hireAgain ? (
              <View
                style={[
                  styles.bottomMsgBtn,
                    { width: userDetails?.hireAgain ? '46%' : '100%' },
                ]}>
                <Button
                  type="outlined"
                  style={'100%'}
                  onPress={() => {
                    chatButtonFn('hireAgain');
                    if (roomId) {
                      navigation.navigate('JobPosting', {
                        type: 'custom',
                        applicantId:
                          applicantData?.approvedApplicant?.userId ||
                          applicantData?.id,
                        roomId: roomId,
                      });
                    }
                  }}>
                  {translate("hireAgain")}
                </Button>
              </View>
            ) : null}
            <View
              style={[
                styles.bottomMsgBtn,
                  { width: userDetails?.hireAgain ? '46%' : '100%' },
              ]}>
              {/* Message Button */}
              {/* {job?.id && job?.userId ? ( */}
              <Button
                type={userDetails?.hireAgain ? 'outlined' : "text"}
                  style={{ width: '100%' }}
                onPress={() => {
                  chatButtonFn('');
                }}>
                {translate("message","")}
              </Button>
              {/* ) : null} */}
            </View>
          </View>
        </>
      )}
    </>
  );
}

const Section = ({ title, children, style }: any) => (
  <View
    style={{
      ...styles.sectionContainer,
      ...style,
    }}>
    <Text style={styles.sectionTitle}>{title}</Text>
    <View
      style={{
        borderBottomWidth: 1,
        borderColor: BaseColors?.borderColor,
        // marginBottom: 15
        paddingTop: 2,
        marginBottom: 5,
      }}
    />
    {children}
  </View>
);

const ProjectCard = () => (
  <View style={styles.card}>
    <View style={styles.cardRow}>
      <FastImage
        source={{ uri: 'https://via.placeholder.com/40' }}
        style={styles.cardIcon}
      />
      <View>
        <Text style={styles.cardTitle}>Project title will be here</Text>
      </View>
    </View>
    <Text style={styles.cardDescription}>
      Lorem ipsum dolor sit amet consectetur. Risus quisque aliquet tellus a
      lacus.
    </Text>
  </View>
);
