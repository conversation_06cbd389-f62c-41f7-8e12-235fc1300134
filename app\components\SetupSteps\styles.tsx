import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {Dimensions, Platform, StyleSheet} from 'react-native';

const {width, height} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseColors?.white,
    // marginHorizontal: 10,
    marginBottom: 5,
    borderRadius: 10,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    color: BaseColors.primary,
    marginBottom: 10,
    fontFamily: FontFamily?.OpenSansBold,
    textTransform: 'capitalize',
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 5,
  },
  numberContainer: {
    width: 36,
    height: 36,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activeNumber: {
    backgroundColor: BaseColors.primary,
  },
  inactiveNumber: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  numberText: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: FontFamily.OpenSansRegular,
  },
  activeNumberText: {
    color: '#fff',
  },
  inactiveNumberText: {
    color: BaseColors.titleTextColor,
  },
  titleText: {
    flex: 1,
    fontSize: 16,
    color: BaseColors?.titleTextColor,
    fontWeight: '500',
    fontFamily: FontFamily?.OpenSansMedium,
    textTransform: 'capitalize',
  },
  arrowText: {
    fontSize: 20,
    color: '#757575',
    marginLeft: 10,
  },
  mainView: {
    backgroundColor: BaseColors?.inputBackground,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 8,
  },
});
