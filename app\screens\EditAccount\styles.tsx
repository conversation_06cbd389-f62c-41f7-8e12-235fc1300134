import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {StyleSheet} from 'react-native';
import {Dimensions} from 'react-native';
import {Platform} from 'react-native';
const IOS = Platform.OS === 'ios';
export default StyleSheet.create({
  container: {
    flex: 1,

    backgroundColor: BaseColors.white,
  },

  policyListContainer: {
    marginTop: 10,
    marginHorizontal: 20,
  },
  policyItem: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  policyTitle: {
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
    fontSize: 16,
    paddingLeft: 10,
  },
});
