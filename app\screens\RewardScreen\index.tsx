import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  ScrollView,
  Share,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import { useSelector } from 'react-redux';
import Header from '@components/Header';
import FastImage from 'react-native-fast-image';
import Button from '@components/UI/Button';
import * as Progress from 'react-native-progress'; // Importing the progress library
import { translate } from '@language/Translate';
import HandleClaimComponant from '@components/HandleClaim';
import { getApiData } from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import { getBadgeImage } from '@app/utils/CommonFunction';
import AnimatedView from '@components/AnimatedView';
import { BaseColors, BaseStyles } from '@config/theme';
import StreakTracker from '@components/StreakTracker';
import dayjs from 'dayjs';
import Entypo from 'react-native-vector-icons/Entypo';
import FloatingButton from '@components/FloatingButton';
import { Images } from '@config/images';

interface RewardCategory {
  id: number;
  keyName: string;
  keyValue: any; // Adjust the type as necessary
  isKeyValue: boolean;
  slug: string;
  points: number;
  message: string;
  rewardCategoryId: number;
  createdAt: string;
  updatedAt: string;
  isDone: boolean;
  isDoneCreatedAt: string | null;
}

export default function RewardScreen({ navigation }: any) {
  const { userData } = useSelector((state: any) => state.auth);
  const [loader, setLoader] = useState(false);
  const [rewardList, setRewardList] = useState<any>([]);
  const [badgeInfo, setBadgeInfo] = useState<any>({});
  const [openStrick, setOpenStrick] = useState<any>(false);
  const [incompleteCategories, setIncompleteCategories] = useState<any[]>([]); // State for incomplete categories
  console.log('badgeInfo ===>', badgeInfo);
  const [showStreakTracker, setShowStreakTracker] = useState(false); // Track visibility of StreakTracker

  const progress = badgeInfo?.totalPercentageCompleted
    ? Number(badgeInfo?.totalPercentageCompleted) / 100
    : 0;

  // Function to filter options based on incomplete categories
  function getIncompleteCategories(
    rewardsData: {category: RewardCategory[]}[],
  ): RewardCategory[] {
    const incompleteCategories: RewardCategory[] = [];

    for (const reward of rewardsData) {
      const firstIncompleteCategory = reward.category.find(
        category => !category.isDone,
      );

      if (firstIncompleteCategory) {
        incompleteCategories.push(firstIncompleteCategory);

        if (incompleteCategories.length === 3) {
          return incompleteCategories; // Return if 3 incomplete categories are found
        }
      }
    }

    return incompleteCategories; // Return whatever is found if less than 3
  }

  const getRewardData = async () => {
    const newObj = {
      userId: userData?.id,
    };
    setLoader(true);
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.rewardList,
        method: 'POST',
        data: newObj,
      });
      console.log('🚀 ~ getRewardData ~ res:', JSON.stringify(res));

      if (res?.status === true) {
        setRewardList(res?.data);
        setIncompleteCategories(getIncompleteCategories(res?.data)); // Update the state with incomplete categories
        setBadgeInfo(res?.badgeInfo || {});
        setLoader(false);
      } else {
        setLoader(false);
        setRewardList([]);
      }
    } catch (err) {
      console.error('Error fetching applicants:', err);
      setLoader(false);
      setRewardList('');
    }
  };

  useEffect(() => {
    getRewardData();
  }, []);

  const streakData = {
    currentStreak: 1,
    totalReadingPoint: 0,
    streakData: [
      {
        day: dayjs().subtract(4, 'day').format('dd').charAt(0),
        date: new Date(),
        earned: false,
      },
      {
        day: dayjs().subtract(3, 'day').format('dd').charAt(0),
        date: new Date(),
        earned: true,
      },
      {
        day: dayjs().subtract(2, 'day').format('dd').charAt(0),
        date: new Date(),
        earned: true,
      },
      {
        day: dayjs().subtract(1, 'day').format('dd').charAt(0),
        date: new Date(),
        earned: true,
      },
      {
        day: dayjs().format('dd').charAt(0),
        date: new Date(),
        earned: true,
      },
      {
        day: dayjs().add(1, 'day').format('dd').charAt(0),
        date: new Date(),
        earned: true,
      },

      {
        day: dayjs().add(2, 'day').format('dd').charAt(0),
        date: new Date(),
        earned: false,
      },
    ],
  };
  const isMaxReached = badgeInfo?.currentBadge === 'Diamond';


  return (
    <View style={styles.container}>
      {/* Header */}
      <Header
        leftIcon="back-arrow"
        title="Rewards"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <ScrollView>
        <AnimatedView>
          {loader ? (
            <View style={styles.centerMain}>
              <ActivityIndicator size={'small'} color={BaseColors.primary} />
            </View>
          ) : (
            <View style={styles.mainView}>
              {/* Profile Card */}
              <View style={styles.card}>
                {/* Profile Section */}
                <View style={styles.profileSection}>
                  <FastImage
                    source={
                      userData?.profilePhoto
                        ? {
                          uri: userData?.profilePhoto,
                        }
                        : Images.user
                    }
                    style={styles.profileImage}
                  />
                  <View style={styles.profileDetails}>
                    <View style={styles.nameRow}>
                      {userData?.isAvailable ? (
                        <View style={BaseStyles.onlineIndicator} />
                      ) : null}
                      <Text style={styles.profileName}>
                        {userData?.firstName} {userData?.lastName}
                      </Text>
                    </View>
                    {/* Progress Bar Section */}
                    <View style={styles.progressContainer}>
                      <View style={styles.progressLabels}>
                        <Text style={styles.progressText}>
                          {badgeInfo?.totalPoints || '0'} pts
                        </Text>

                        {isMaxReached ? <Entypo name="infinity" /> :
                          <Text style={styles.progressText}>
                            {badgeInfo?.endingPoints || '299'} pts
                          </Text>}
                      </View>
                      <Progress.Bar
                        progress={progress}
                        width={null} // Full width
                        height={10}
                        color={BaseColors.primary}
                        unfilledColor={BaseColors.blueLight}
                        borderWidth={0}
                        borderRadius={5}
                        indeterminate={false}
                      />
                      <View style={styles.levelLabels}>
                        <View style={styles.levelLabel}>
                          <View style={styles.imageView}>
                            <FastImage
                              source={getBadgeImage(badgeInfo?.currentBadge)}
                              style={styles.medalIcon}
                              resizeMode="contain"
                            />
                          </View>
                          <Text style={styles.levelText}>
                            {badgeInfo?.currentBadge}
                          </Text>
                        </View>
                        <View style={styles.levelLabel}>
                          <Text style={styles.levelText}>
                            {badgeInfo?.upcomingBadge}
                          </Text>
                          <View style={styles.imageView}>
                            <FastImage
                              source={getBadgeImage(badgeInfo?.upcomingBadge)}
                              style={styles.medalIcon}
                              resizeMode="contain"
                            />
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
                {/* Refer a Friend Button */}
                <View style={styles.mgTp}>
                  <Button
                    onPress={async () => {
                      try {
                        await Share.share({
                          message: `Join Harbor! Sign up using my referral code: ${userData?.myReferralCode} Link to app: https://theharborapp.com `,
                        });
                      } catch (error) {
                        console.error('Error sharing link', error);
                      }
                    }}
                    type="text">
                    {translate('referFrd', '')}
                  </Button>
                </View>

                {/* Stats Section */}
                <View style={styles.statsContainer}>
                  <View style={styles.amountStyle}>
                    <View style={styles.earnPoints}>
                      <Text style={styles.statsText}>
                        {translate('suggestedReward', '')}
                      </Text>
                    </View>
                    {incompleteCategories.length > 0 && (
                      <View style={styles.incompleteCategoriesContainer}>
                        {incompleteCategories.map(
                          (category: any, index: number) => (
                            <View style={styles?.rewardView}>
                              <Text
                                numberOfLines={2}
                                key={index}
                                style={styles.incompleteCategoryText}>
                                {category.keyName}
                                {/* Display the keyName of the incomplete category */}
                              </Text>
                              <View>
                                <TouchableOpacity style={styles?.btnView}>
                                  <Text style={styles?.categoryPointSty}>
                                    {category?.points} pts
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </View>
                          ),
                        )}
                      </View>
                    )}
                  </View>
                </View>
                {/* Rewards Section */}
                <Text style={[styles.textSty, styles.mgBt]}>
                  {translate('Explore', '')}
                </Text>
                <HandleClaimComponant rewardList={rewardList} />
              </View>
            </View>
          )}
        </AnimatedView>
      </ScrollView>

      <View style={styles.floatingContainer}>
        {showStreakTracker && !loader && (
          <View style={styles?.streakTrackerWrapper}>
            <StreakTracker
              currentStreak={badgeInfo?.currentStreak}
              totalReadingPoint={
                badgeInfo?.totalReadingPoint || streakData.totalReadingPoint
              }
              streakData={
                badgeInfo?.streakData?.length
                  ? badgeInfo?.streakData
                  : streakData.streakData
              }
            />
          </View>
        )}
        <FloatingButton
          onPress={() => setShowStreakTracker(!showStreakTracker)}
          iconName={showStreakTracker ? 'cross' : 'fire'}
          iconColor={BaseColors.white}
        />
      </View>
    </View>
  );
}
