import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {StyleSheet} from 'react-native';
import {Dimensions} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: BaseColors.white,
  },
  nosearchView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: Dimensions.get('window').height / 1.6,
  },
  noFound: {
    fontSize: 26,
    fontFamily: FontFamily.OpenSansMedium,
    lineHeight: 51,
    marginVertical: 20,
  },
});
