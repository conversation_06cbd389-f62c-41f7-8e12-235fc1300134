import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {Platform, StyleSheet} from 'react-native';
import {Dimensions} from 'react-native';
const HEIGHT = Dimensions.get('window').height;
const {width} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    backgroundColor: BaseColors.white,
    flex: 1,
    // marginBottom: 10,
    // paddingBottom: 20,
  },
  nosearchView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: Dimensions.get('window').height / 1.6,
  },
  noFound: {
    fontSize: 26,
    fontFamily: FontFamily.OpenSansMedium,
    lineHeight: 51,
    marginVertical: 20,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '80%',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  tabText: {
    fontSize: 16,
    color: '#7a7a7a', // Default text color
  },
  activeText: {
    color: '#1d559f', // Active text color
    fontWeight: 'bold',
  },
  barContainer: {
    position: 'relative',
    width: '80%',
    marginTop: 4,
    height: 4,
    backgroundColor: 'transparent',
  },
  backgroundBar: {
    position: 'absolute',
    height: 4,
    width: '100%',
    backgroundColor: '#e3eaf5', // Light blue background bar color
    borderRadius: 2,
  },
  activeBar: {
    position: 'absolute',
    height: 4,
    width: '50%', // Half width for one tab
    backgroundColor: '#1d559f', // Blue active bar color
    borderRadius: 2,
    transitionDuration: '0.3s', // Smooth transition
  },
  mainView: {
    marginHorizontal: 10,
    marginVertical: 10,
  },
  searchViewSty: {
    flexDirection: 'row',
    alignSelf: 'center',
    // alignItems: 'center',
    textAlign: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 10,
    marginTop: 35,
  },

  searchFieldSty: {
    flex: 0.98, // Take 80% of the parent width
    // height: 45, // Adjust height if needed
  },
  iconView: {
    padding: 5,
    borderWidth: 1,
    borderColor: BaseColors.bordrColor,
    // alignSelf: 'center',
    // alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    width: 40,
    height: 43,
    zIndex: 999,
    // marginLeft: 10,
    // flex: 0.2, // Take 20% of the parent width
  },
  centerMain: {
    flex: 1,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  noDataSty: {
    height: Dimensions.get('screen').height / 1.5,
  },
  msgIconStyle: {
    right: 0,
    width: 40,
    height: 40,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColors.secondry,
  },
  imgView: {
    borderRadius: width * 0.0125, // Assuming 5 is about 1.25% of the width
    borderWidth: width * 0.0025, // Assuming 1 is about 0.25% of the width
    borderColor: BaseColors.secondaryBorder,
    width: width * 0.12, // Assuming 40 is 10% of the width
    height: width * 0.12, // Matching width for a square shape
  },
  descriptiontxtSty: {
    fontSize: 14,
    fontWeight: FontFamily.OpenSansMedium,
    paddingBottom: IOS ? 0 : 5,
  },
  mainViewSty: {
    // flexDirection: 'row',
    // alignSelf: 'center',
    // justifyContent: 'center',
    // paddingBottom: 10,
    // paddingTop: 20,
    backgroundColor: BaseColors.white,
  },
  timeView: {
    marginTop: IOS ? 10 : 0,
    marginHorizontal: 10,
  },
  timetxtSty: {
    fontSize: 14,
    fontFamily: FontFamily?.OpenSansMedium,
    paddingBottom: IOS ? 0 : 4,
    color: BaseColors?.timeColor,
  },
  desSty: {
    fontSize: 14,
    fontFamily: FontFamily?.OpenSansMedium,
    paddingBottom: IOS ? 0 : 4,
    color: BaseColors?.textColor,
  },
  titleSty: {
    fontSize: 16,
    fontFamily: FontFamily?.OpenSansMedium,
    paddingBottom: IOS ? 0 : 2,
    color: BaseColors?.textColor,
  },
  descriptionViewSty: {
    paddingHorizontal: 15,
    width: Dimensions.get('screen').width / 1.3,
  },
  underLine: {
    borderBottomWidth: 0.7,
    borderColor: BaseColors?.timeColor,
  },
  dotView: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: BaseColors?.primary,
    position: 'absolute',
    left: -2,
    top: 7,
  },
  swipeActionContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'red',
    width: 80,
    height: '100%',
  },
  swipeActionText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  dltStyView: {
    alignItems: 'center',
    position: 'absolute',
    right: 0,
    top: '40%',
    paddingHorizontal: 14,
  },
  dltTxtSty: {
    color: BaseColors.white,
    fontFamily: FontFamily?.OpenSansSemiBold,
    paddingBottom: IOS ? 0 : 5,
  },
  dltView: {
    backgroundColor: '#EE484A',
    height: '100%',
    width: '100%',
    alignContent: 'center',
  },
  seeMoreLessSty: {
    color: '#1d559f', // Customize color
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 5,
  },
});
