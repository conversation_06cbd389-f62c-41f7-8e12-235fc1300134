import {StyleSheet} from 'react-native';
import {BaseColors} from '@config/theme';
import {Dimensions} from 'react-native';
import {Platform} from 'react-native';
import {FontFamily} from '@config/typography';
const IOS = Platform.OS === 'ios';
export default StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: BaseColors.white,
  },
  nosearchView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: Dimensions.get('window').height / 1.6,
  },
  noFound: {
    fontSize: 26,
    fontFamily: FontFamily.OpenSansMedium,
    lineHeight: 51,
    marginVertical: 20,
    color: BaseColors.textGrey,
  },
});
