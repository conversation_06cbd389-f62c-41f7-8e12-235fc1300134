import {StyleSheet, Dimensions} from 'react-native';

export default StyleSheet.create({
  animationWrap: {justifyContent: 'center', alignItems: 'center'},
  animation: {height: 150, width: 200},
  mainViewModal: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  containerf: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    width: Dimensions.get('window').width / 1.2,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 1,
    elevation: 15,
  },
  offlineTitle: {fontSize: 26, color: '#666', textAlign: 'center'},
  offlineSubtxt: {
    fontSize: 15,
    color: '#666',
    marginTop: 5,
    textAlign: 'center',
  },
  blurView: {position: 'absolute', top: 0, left: 0, bottom: 0, right: 0},
});
