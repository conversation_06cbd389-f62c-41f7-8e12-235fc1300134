/* eslint-disable no-async-promise-executor */
import BaseSetting from '@config/setting';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import appleAuth from '@invertase/react-native-apple-authentication';
import Toast from 'react-native-simple-toast';
import {translate} from '@language/Translate';

GoogleSignin.configure({
  // scopes: ['https://www.googleapis.com/auth/drive.readonly'],
  webClientId: BaseSetting.googleClientId,
  // forceConsentPrompt: true,
});

export const signInWithGoogle = async () => {
  return new Promise(async (resolve, reject) => {
    try {
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();
      resolve(userInfo);
    } catch (e: any) {
      console.log('🚀 ~ returnnewPromise ~ e:', JSON.stringify(e), e);
      Toast.show(e?.message || translate('err', ''), Toast.SHORT);
      if (e.code === statusCodes.SIGN_IN_CANCELLED) {
        // user cancelled the login flow
      } else if (e.code === statusCodes.IN_PROGRESS) {
        // operation (e.g. sign in) is in progress already
      } else if (e.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        // play services not available or outdated
      } else {
        // some other error happened
      }
      reject(e);
    }
  });
};

export const signOutGoogle = async () => {
  return new Promise(async (resolve, reject) => {
    try {
      await GoogleSignin.revokeAccess();
      await GoogleSignin.signOut();
      return;
      // this.setState({ user: null }); // Remember to remove the user from your app's state as well
    } catch (error) {
      console.error(error);
    }
  });
};

export const googleSignOut = async () => {
  try {
    await GoogleSignin.revokeAccess();
    let response = await GoogleSignin.signOut();
    return response;
  } catch (error) {
    console.error(error);
    return error;
  }
};

export const signinWithApple = async () => {
  // performs login request
  const appleAuthRequestResponse = await appleAuth.performRequest({
    requestedOperation: appleAuth.Operation.LOGIN,
    requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
  });
  console.log(
    '🚀 ~ onAppleButtonPress ~ appleAuthRequestResponse:',
    appleAuthRequestResponse,
  );

  // get current authentication state for user
  // /!\ This method must be tested on a real device. On the iOS simulator it always throws an error.
  const credentialState = await appleAuth.getCredentialStateForUser(
    appleAuthRequestResponse.user,
  );

  // use credentialState response to ensure the user is authenticated
  if (credentialState === appleAuth.State.AUTHORIZED) {
    const data = {
      auth_type: 'apple',
      apple_token: appleAuthRequestResponse.identityToken,
      apple_data: appleAuthRequestResponse,
    };
    console.log('🚀 ~ onAppleButtonPress ~ data:', data);
    return data;
    // const type = 'apple';
    // socialLogin(data, type);
  }
};
