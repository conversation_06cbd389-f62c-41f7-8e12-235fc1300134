import React, {useState} from 'react';
import {ActivityIndicator, Text, TouchableOpacity, View} from 'react-native';
import styles from './styles';
import {translate} from '@language/Translate';
import {isArray, isEmpty} from '@app/utils/lodashFactions';
import moment from 'moment';
import {BaseColors} from '@config/theme';
import {CustomIcon} from '@config/LoadIcons';
import FastImage from 'react-native-fast-image';
import FIcon from 'react-native-vector-icons/FontAwesome'; // Replace with your specific icon library if different
import {FontFamily} from '@config/typography';

const LicenseandCertificate: React.FC<licenseandCertificateProp> = ({
  preCertificate,
  setPreCertificate,
  reviewType,
  navigation,
  licenseFiles,
  userDetails,
}) => {
  const [showAll, setShowAll] = useState(false);

  const combinedArray =
    reviewType === 'reviewbyEmployer'
      ? !isEmpty(userDetails)
        ? (Array.isArray(userDetails?.certifications)
            ? userDetails.certifications
            : []
          ).concat(
            Array.isArray(userDetails?.licenses) ? userDetails.licenses : [],
          )
        : []
      : !isEmpty(preCertificate) && preCertificate.length > 0
      ? preCertificate.concat(Array.isArray(licenseFiles) ? licenseFiles : [])
      : !isEmpty(licenseFiles)
      ? licenseFiles
      : !isEmpty(preCertificate)
      ? preCertificate
      : [];

  if (isEmpty(combinedArray) || !isArray(combinedArray)) {
    return (
      <View style={styles.card}>
        <View
          style={{
            borderBottomWidth: 1,
            borderColor: BaseColors?.borderColor,
          }}>
          <Text style={styles?.titleTxtSty}>
            {translate('licenseandcertificate', '')}
          </Text>
        </View>
        <Text style={styles?.nodataTxt}>-</Text>
      </View>
    );
  }

  const visibleExperiences = showAll
    ? combinedArray
    : combinedArray.slice(0, 2);

  return (
    <View>
      {visibleExperiences?.length > 0 && (
        <View style={styles.card}>
          <View
            style={{
              borderBottomWidth: 1,
              borderColor: BaseColors?.borderColor,
              marginBottom: 15,
            }}>
            <Text style={styles?.titleTxtSty}>
              {translate('licenseandcertificate', '')}
            </Text>
          </View>
          {visibleExperiences.map((certificate, index) => (
            <React.Fragment key={certificate?.id || index}>
              <View style={styles.cardRow}>
                <View style={styles?.certificateMainView}>
                  {certificate?.isVerified ? (
                    <View style={styles?.verificationSty}>
                      <CustomIcon
                        name="Vector"
                        color={BaseColors.primary}
                        size={20}
                      />
                    </View>
                  ) : null}
                  {/* <TouchableOpacity
                    // onPress={() => {
                    //   // Check for image files
                    //   if (
                    //     /\.(jpg|jpeg|png|gif)$/i.test(
                    //       certificate?.docType === 'certifications'
                    //         ? certificate?.fileName
                    //         : certificate?.url,
                    //     )
                    //   ) {
                    //     navigation.navigate('GalleryView', {
                    //       images: [
                    //         certificate?.docType === 'certifications'
                    //           ? certificate?.filePath || certificate?.fileName
                    //           : certificate?.url,
                    //       ],
                    //       index: 0,
                    //     });
                    //   }
                    //   // Check for document files
                    //   else if (
                    //     /\.(pdf|docx)$/i.test(
                    //       certificate?.docType === 'certifications'
                    //         ? certificate?.fileName
                    //         : certificate?.url,
                    //     )
                    //   ) {
                    //     navigation.navigate('WebViewScreen', {
                    //       uri:
                    //         certificate?.docType === 'certifications'
                    //           ? certificate?.filePath || certificate?.fileName
                    //           : certificate?.url,
                    //       type: 'profile',
                    //     });
                    //   }
                    //   // Handle other file types if needed (e.g., show file icon)
                    //   else {
                    //     console.log('File type not supported for preview');
                    //   }
                    // }}
                    style={styles?.imageView}>
                    {certificate?.fileName || certificate?.url ? (
                      // Check for image files
                      /\.(jpg|jpeg|png|gif)$/i.test(
                        certificate?.docType === 'certifications'
                          ? certificate?.fileName
                          : certificate?.url,
                      ) ? (
                          <FastImage
                            source={{
                              uri:
                              certificate?.docType === 'certifications'
                                ? certificate?.filePath || certificate?.fileName
                                : certificate?.url,
                            }}
                            style={{ width: '100%', height: '100%' }}
                            resizeMode="contain"
                          />
                        ) : /\.(pdf|docx)$/i.test(
                          certificate?.docType === 'certifications'
                            ? certificate?.fileName
                            : certificate?.url,
                        ) ? (
                        // Check for document files (pdf, docx, etc.)
                            <FIcon
                              name="file-pdf-o" // You can replace this with the appropriate icon if needed
                              size={30}
                              color={BaseColors.primary}
                            />
                          ) : (
                        // Default for unsupported file types
                            <FIcon
                              name="file"
                              size={30}
                              color={BaseColors.primary}
                            />
                          )
                    ) : (
                      <FIcon name="file" size={30} color={BaseColors.primary} />
                    )}
                  </TouchableOpacity> */}

                  <View style={styles?.txtViewSty}>
                    <Text style={styles?.certificateTitleSty}>
                      {certificate?.docTitle
                        ? certificate?.docTitle
                        : certificate?.docType === 'certifications'
                        ? certificate?.company || certificate?.docTitle
                        : certificate?.docTitle ||
                          (certificate?.url &&
                            typeof certificate?.url === 'string' &&
                            certificate?.url.includes('http'))
                        ? String(certificate?.url)?.split('/').pop()
                        : certificate?.url || '-'}
                    </Text>
                    {certificate?.docType === 'certifications' ? (
                      <Text style={styles?.dateTxtSty}>
                        {certificate?.startDate
                          ? moment(certificate?.startDate).format('M/D/YYYY')
                          : 'N/A'}{' '}
                        -{' '}
                        {certificate?.endDate
                          ? moment(certificate?.endDate).format('M/D/YYYY')
                          : 'N/A'}
                      </Text>
                    ) : null}
                    {/* <Text
                      style={{
                        color: BaseColors.primary,
                        fontSize: 14,
                        fontFamily: FontFamily?.OpenSansMedium,
                      }}>
                      {certificate?.docType === 'certifications'
                        ? 'Certificate'
                        : 'License'}
                    </Text> */}
                  </View>
                </View>
              </View>
            </React.Fragment>
          ))}
          {combinedArray?.length > 2 && (
            <TouchableOpacity
              onPress={() => setShowAll(!showAll)}
              style={styles.seeMoreButton}>
              <Text style={styles.seeMoreText}>
                {showAll ? 'See Less' : 'See More'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

export default LicenseandCertificate;
