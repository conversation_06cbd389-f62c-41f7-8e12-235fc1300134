import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { StyleSheet } from 'react-native';
import { Dimensions } from 'react-native';
const HEIGHT = Dimensions.get('window').height;
const { width } = Dimensions.get('window');

export default StyleSheet.create({
  container: {
    backgroundColor: BaseColors.white,
  },
  msgIconStyle: {
    right: 0,
    width: 35,
    height: 35,
    borderWidth: 1,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColors.primary,
  },
});
