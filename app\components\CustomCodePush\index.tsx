import { View } from 'react-native';
import React from 'react';
import { HotUpdater } from '@hot-updater/react-native';
import Toast from 'react-native-simple-toast';

const Index = () => {
  return (
    <View />
  );
};

// Define types for the update component props
type UpdateNotifierProps = {
  status: 'CHECK_FOR_UPDATE' | 'UPDATING';
  progress: number;
  message: string | null;
};

// Create a proper update component that can use hooks
const UpdateNotifier = ({ progress, status, message: _ }: UpdateNotifierProps) => {
  React.useEffect(() => {
    if (status === 'CHECK_FOR_UPDATE') {
      // Toast.showWithGravity('Checking for updates...', Toast.SHORT, Toast.BOTTOM);
    } else if (status === 'UPDATING') {
      if (progress > 0) {
        Toast.showWithGravity(`Updating: ${Math.round(progress * 100)}%`, Toast.SHORT, Toast.BOTTOM);
      } else {
        Toast.showWithGravity('Starting update...', Toast.SHORT, Toast.BOTTOM);
      }
    }
  }, [status, progress]);

  // Return null to not interrupt the app flow
  return null;
};

export default HotUpdater.wrap({
  source: 'https://eztyxzbdxbeenlaulzxd.supabase.co/functions/v1/update-server',
  requestHeaders: {
    // if you want to use the request headers, you can add them here
  },
  fallbackComponent: UpdateNotifier,
})(Index);
