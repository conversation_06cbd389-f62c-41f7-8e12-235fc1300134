import React, { useCallback, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import BUTTON from '@components/UI/Button';
import Entypo from 'react-native-vector-icons/Entypo';
import FastImage from 'react-native-fast-image';
import { CustomIcon } from '@config/LoadIcons';
import { BaseColors, BaseStyles } from '@config/theme';
import { translate } from '@language/Translate';
import { FontFamily } from '@config/typography';
import { Images } from '@config/images';
import {
  checkImageExists,
  convertToCamelCase,
  formatSalary,
  getBadgeImage,
  getDuration,
} from '@app/utils/CommonFunction';
import { useSelector } from 'react-redux';
import moment from 'moment';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash-es';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import {
  initPaymentSheet,
  presentPaymentSheet,
} from '@stripe/stripe-react-native';
import { getApiData } from '@app/utils/apiHelper';
import Toast from 'react-native-simple-toast';
import BaseSetting from '@config/setting';
import CustomOfferModal from '@components/CustomOfferModal';
import AlertModal from '@components/AlertModal';
import { updateJobStatus } from '@screens/JobApplicant/ApiFuntions';
import { useAppDispatch, useAppSelector } from '@components/UseRedux';
import SocketActions from '@redux/reducers/socket/actions';
import UserConfigActions from '@redux/reducers/userConfig/actions';
const { width } = Dimensions.get('window');

const { emit, setUpdateChat, onReceive } = SocketActions;

interface UserJob {
  status?: string;
}

interface ChatData {
  messageType?: string;
  message?: {
    userId?: string;
    isDeclined?: boolean;
    isAccepted?: boolean;
    isFinal?: boolean;
    isDisable?: boolean;
    jobId?: string;
    title?: string;
    isPayment?: boolean;
    status?: string;
    userJob?: UserJob;
    isFlatRate?: boolean;
    isDirectApplied?: boolean;
    offerBy?: string;
    userData: {
      averageRate: number;
      company: string;
      currentBadgeName: string;
      level: number;
      profilePhoto: string;
    };
  };
  sender?: string;
  receiver?: string;
  counterOfferId?: string;
  id?: number | string;
  processing?: boolean;
}

interface CounterOfferCardProps {
  item: any;
  onActionClick?: any;
  handleUpdateJob?: any;
  applicantsLoader?: any;
  navigation?: any;
  jobDetail?: any;
  applicantType?: any;
  jobtype?: any;
  bodyType?: any;
  buttons?: boolean;
  type?: string;
  loader?: boolean;
  chatData?: ChatData;
}

const stData = {
  customOfferModal: false,
  confirmationModal: false,
  counterOfferModal: false,
  loader: false,
  type: '',
  applicant: {},

  // counterHistory: [],
  customOfferLoader: false,
  counterOfferLoader: false,
};

const CounterOfferCard: React.FC<CounterOfferCardProps> = ({
  item,
  onActionClick = () => {},
  applicantsLoader = false,
  navigation,
  jobDetail,
  handleUpdateJob = () => {},
  applicantType,
  jobtype,
  bodyType,
  buttons = true,
  type = '',
  loader = false,
  chatData = {},
}) => {
  dayjs.extend(customParseFormat); // Extend dayjs with the plugin
  const dispatch = useAppDispatch();
  const isChatView = jobDetail?.isChatView || false;
  const { selectedChatList, selectedRoom } = useSelector((s: any) => s.socket);
  const [state, setState] = useState<any>({
    customOfferModal: false,
    loader: false,
    counterOfferModal: false,
    type: '',
    confirmationModal: false,
    applicant: {},

    counterHistory: [],
    customOfferLoader: false,
    counterOfferLoader: false,
  });
  const [modalOpen, setModalOpen] = useState<any>({
    loader: false,
    confirmationModal: false,
  });

  console.log('processingData jobDetail ===>', jobDetail, chatData);

  const [loading, setLoader] = useState(false);

  const { counterOfferModal, customOfferModal } = state;
  // console.log('chatData?.message ===>', chatData);
  const { userData } = useSelector((s: any) => s.auth);

  const isProfileSetupNotDone = userData?.isProfileSet === false;
  // console.log('🚀 ~ userData buttons:',
  //   chatData?.message?.title === 'Countee' ? item : null,
  //   chatData?.message?.title,
  //   chatData?.message?.title === 'Countee' ? jobDetail : null
  // );

  const [imageError, setImageError] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [numberOfLines, setNumberOfLines] = useState(0);
  const isEmployer = userData?.id === jobDetail?.userId;
  const isOfferByEmployer = item?.offerBy === 'employer';
  const isApproved = String(item?.userJob?.status).toLowerCase() === 'approved';

  const isHistoryView = type === 'History';
  // console.log('jobDetail ===>', isHistoryView, jobDetail);

  const checkEmployerId = userData?.id === item?.employerId;

  const USER_VERIFIED =
    item?.userData?.personaStatus === 'approved' ? true : false;

  const handleOnClose = useCallback(() => {
    setState({ ...state, ...stData });
  }, [state]);

  // Helper function to capitalize the first letter of a string
  const capitalizeFirstLetter = (str: any) => {
    if (!str) {
      return '';
    }
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  };

  const setStateUpdate = useCallback(() => {
    setState((p: any) => ({
      ...p,
      ...stData,
    }));
  }, []);

  interface DATA {
    sender: number | string;
    receiver: number | string;
    conversationRoomId: number | string;
    counterOfferId?: number | string;
  }

  const updateInChat = useCallback(
    (type: string = '') => {
      try {
        let data: DATA = {
          sender: state?.applicant?.receiveBy || chatData?.receiver,
          receiver: state?.applicant?.sendBy || chatData?.sender,
          conversationRoomId: jobDetail?.data?.roomId || selectedRoom?.id,
        };
        if (
          type !== 'regularJob' ||
          state?.applicant?.counterOfferId ||
          chatData?.counterOfferId
        ) {
          data.counterOfferId =
            state?.applicant?.counterOfferId || chatData?.counterOfferId;
        }
        dispatch(
          emit('update_counter_offer', data, (res: any) => {
            console.log('🚀 ~ res:', res);
            setStateUpdate();
            if (res?.counterOfferId) {
              dispatch(setUpdateChat(true));
            }
            handleUpdateJob();
          }),
        );
        return true;
      } catch (e) {
        console.log('errror ===>', e);
      }
    },
    [state, chatData, jobDetail],
  );

  const applyForJob = useCallback(async () => {
    setLoader(chatData?.id);
    const newObj = {
      jobId: jobDetail?.jobId || jobDetail?.id,
      userId: chatData?.message?.userId,
      status: 'Applied',
    };
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.updateuserJob,
        method: 'POST',
        data: newObj,
      });
      if (res?.status === true) {
        dispatch(setUpdateChat(true));
        updateInChat('regularJob');
        // handleUpdateJob();
        // setCAlert({
        //   message: translate('appliedNotification', ''),
        //   showAlert: true,
        //   image: Images.usaPng,
        // });
        handleUpdateJob();
        Toast.show(
          res?.message || translate('appliedNotification', ''),
          Toast.BOTTOM,
        );
      } else {
        Toast.show(res?.message, Toast.BOTTOM);
        console.log('errror in api res ===>', res);
        setLoader(false);
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      }
      setLoader(false);
    } catch (err) {
      setLoader(false);
      // Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  }, [jobDetail]);

  const onSubmit = useCallback(
    async (values: any, type?: string) => {
      const isEdit = state?.type === 'edit';
      console.log('values ===>', values, type, item, chatData);
      if (
        isEmployer &&
        !item?.isDeclined &&
        (item?.isAccepted || item?.isFinal)
      ) {
        // updateJobStatus('Declined', { jobId: jobDetail?.id, userId: item?.seekerId });.
        setState((p: any) => ({ ...p, counterOfferLoader: true }));
        const resp = await updateJobStatus('Declined', {
          jobId: jobDetail?.jobId || jobDetail?.id,
          userId: isChatView ? chatData?.receiver : item?.seekerId,
        });
        console.log('resp resp ==>', resp);
        if (resp?.status) {
          if (isChatView) {
            handleUpdateJob(chatData);
          }
          // resetState();
          setState((p: any) => ({
            ...p,
            customOfferModal: false,
            counterOfferModal: false,
            confirmationModal: false,
            counterOfferLoader: false,
            applicant: {},
            job: {},
          }));
          updateInChat();
          // Goes to back and update data
          // getList();
        } else {
          Toast.show(resp?.message || translate('err'), Toast.SHORT);
          setState((p: any) => ({
            ...p,
            // confirmationModal: false,
            counterOfferLoader: false,
            // applicant: {},
            // job: {},
          }));
        }
        return false;
      }
      if (state?.applicant?.counterOfferId || type === 'custom') {
        if (state?.type === 'Approved' || state?.type === 'Declined') {
          setState((p: any) => ({ ...p, customOfferLoader: true }));
          if (isEmployer && state?.type === 'Approved') {
            const offerData = {
              offerBy: state?.applicant?.offerBy,
              counterOfferId: state?.applicant?.counterOfferId,
              type: state?.type,
              sType: state?.type,
            };
            console.log(
              'caleed ==>',
              state?.type,
              state?.applicant?.offerBy,
              state?.applicant?.counterOfferId,
            );
            setStateUpdate();
            await initializePaymentSheet(item?.seekerId, item, offerData);
            return false;
          }
          const url = BaseSetting.endpoints.updateCounterOfferEdit;
          const apiData = {
            counterOfferId: state?.applicant?.counterOfferId || null,
            userId: userData?.id,
            jobId: jobDetail?.id || null,
            userType:
              state?.applicant?.offerBy === 'seeker' ? 'employer' : 'seeker',
            status: String(state?.type).toLowerCase(),
          };
          console.log('onsubmit called ==>', state?.applicant);
          // return false;
          try {
            const response = await getApiData({
              endpoint: url,
              data: apiData,
              method: 'POST',
            });

            if (response?.status) {
              if (isChatView) {
                handleUpdateJob(chatData);
              }
              console.log('response Approved ===>', response);
              updateInChat();
              // if (isEmployer && state?.type === 'Approved') {
              //   initializePaymentSheet(item?.seekerId, item, {});
              // }
              // Goes to back and update data
              if (response?.message) {
                Toast.show(response?.message || translate('err'), Toast.SHORT);
              }
            } else {
              Toast.show(response?.message || translate('err'), Toast.SHORT);
            }
            setState((p: any) => ({ ...p, customOfferLoader: false }));
          } catch (error) {
            console.error('Error submitting form:', error);
            setState((p: any) => ({ ...p, customOfferLoader: false }));
          }
        } else {
          setState((p: any) => ({ ...p, customOfferLoader: true }));
          console.log(
            '🚀 ~ handleSubmit values:',
            values,
            jobDetail?.isFlatRate,
          );
          // duration must of following :perHour,perDay,perWeek,perMonth"
          const payload: any = {
            jobId: jobDetail?.id,
            finalPrice: values?.salaryAmount,
            duration:
              values?.duration === 'Flat Rate' ||
              (jobDetail?.isFlatRate && values?.flatRate)
                ? undefined
                : convertToCamelCase(values?.duration),
            startTime: values?.startTime
              ? moment(values.startTime).format('hh:mm A')
              : '',
            endTime: values?.endTime
              ? moment(values.endTime).format('hh:mm A')
              : '',
            startDate: values?.startDate
              ? moment(values.startDate).format('MM/DD/YYYY')
              : '',
            endDate: values?.endDate
              ? moment(values.endDate).format('MM/DD/YYYY')
              : '',
            message: values?.msg || '',
            sendBy: userData?.id,
            receiveBy:
              state?.type === 'counter'
                ? state?.applicant?.sendBy || chatData?.sender
                : isEmployer
                  ? state?.applicant?.id
                  : jobDetail?.userId,
            // sendBy: isEmployer ? userData?.id : state?.applicant?.receiveBy,
            // receiveBy: isEmployer ? state?.applicant?.id :  state?.applicant?.sendBy,
            offerBy: userData?.id == jobDetail?.userId ? 'employer' : 'seeker',
          };
          if (isEdit) {
            payload.counterOfferId = state?.applicant?.counterOfferId || null;
            payload.userId = userData?.id;
            payload.userType =
              userData?.id == jobDetail?.userId ? 'employer' : 'seeker';
          }

          const endPoint = isEdit
            ? BaseSetting.endpoints.createCounterOfferEdit
            : BaseSetting.endpoints.createOffer;
          try {
            const response = await getApiData({
              endpoint: endPoint,
              data: payload,
              method: 'POST',
            });

            console.log('response ==>', payload, response);

            if (response?.status) {
              // Update the chat for counter offer for job.
              if (isChatView) {
                handleUpdateJob(chatData);
              }
              try {
                dispatch(
                  emit(
                    'counter_offer',
                    {
                      sender: state?.applicant?.receiveBy || chatData?.receiver,
                      receiver: state?.applicant?.sendBy || chatData?.sender,
                      message: JSON.stringify({
                        jobId: jobDetail?.jobId,
                        counterOfferId: response?.counterOfferId,
                      }),
                      messageType: 'counter_offer',
                      roomId: selectedRoom?.id,
                    },
                    (res: any) => {
                      dispatch(onReceive({ data: res }));
                      console.log('🚀 ~ res:', res);
                    },
                  ),
                );
              } catch (error) {
                console.log('🚀 ~ error:', error);
              }
              // if (isEdit) {
              updateInChat();
              // }
              setStateUpdate();
              // setState({ ...state, customOfferModal: false, counterOfferModal: false, customOfferLoader: false, applicant: false, job: {} });
              Toast.show(response?.message, Toast.SHORT);
            } else {
              setState({ ...state, customOfferLoader: false });
              Toast.show(response?.message || translate('error'), Toast.SHORT);
            }
          } catch (error) {
            setState({ ...state, customOfferLoader: false });
            console.error('Error submitting form:', error);
          }
        }
        // return false;
      } else if (type === 'Approved') {
        setStateUpdate();
        navigation.navigate('JobDetailScreen', {
          applicant: state?.applicant,
          type,
          job: jobDetail,
        });
      } else {
        setState((p: any) => ({
          ...p,
          confirmationModal: true,
          applicant: state?.applicant,
          type,
          job: jobDetail,
        }));
      }
    },
    [customOfferModal, state, counterOfferModal, jobDetail],
  ); // Only depend on jobId to avoid unnecessary re-creations

  const handleClick = useCallback(
    async (item: any, sType: string, isChatCustomHarbor?: boolean = false) => {
      console.log('sType ===>', sType, item);
      if (sType === 'cancelApplication') {
        setState((p: any) => ({
          ...p,
          confirmationModal: true,
          applicant: item,
          type: sType,
          job: jobDetail,
        }));
      } else {
        if (item?.counterOfferId || sType === 'counter') {
          if (sType === 'pay') {
            if (item?.counterOfferId) {
              const offerData = {
                offerBy: item?.offerBy,
                counterOfferId: item?.counterOfferId,
                type: 'approved', //isChatView ? chatData?.message?.type : isChatCustomHarbor ? 'custom_harbor' : 'counter_offer',
              };
              await initializePaymentSheet(
                item?.seekerId || item?.id,
                item,
                offerData,
              );
            } else {
              await initializePaymentSheet(
                item?.seekerId || item?.id,
                item,
                {},
              );
            }
            return false;
          }
          setState((p: any) => ({
            ...p,
            counterOfferModal: true,
            // confirmationModal: sType === 'reject' ? true  : false,
            applicant: item,
            type: sType,
            job: jobDetail,
          }));
        } else {
          if (sType === 'Approved') {
            await initializePaymentSheet(item?.id, {}, {});
            // navigation.navigate('JobDetailScreen', { applicant: state?.applicant, type, job: jobDetail });
            return false;
          }
          setState((p: any) => ({
            ...p,
            confirmationModal: true,
            applicant: item,
            type: sType,
            job: jobDetail,
          }));
          return false;
        }
      }
    },
    [jobDetail],
  );

  const resetState = useCallback(() => {
    setState((p: any) => ({
      ...p,
      confirmationModal: false,
      applicantsLoader: false,
      applicant: {},
      job: {},
      type: '',
    }));
  }, []);

  const cancelApplicationApi = async (loaderType?: any = true) => {
    if (loaderType) {
      setLoader(loaderType);
    } else {
      setState((p: any) => ({ ...p, applicantsLoader: true }));
    }
    const newObj = {
      jobId: jobDetail?.jobId || jobDetail?.id,
      status: 'Declined',
    };

    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.cancelApplication,
        method: 'DELETE',
        data: newObj,
      });
      if (res?.status === true) {
        dispatch(setUpdateChat(true));
        updateInChat('');
        resetState();
        handleUpdateJob();
        Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        // getList();
      } else {
        setLoader(false);
        setState((p: any) => ({
          ...p,
          // confirmationModal: false,
          applicantsLoader: false,
          // applicant: {},
          // job: {},
        }));
        Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        return false;
      }
      setLoader(false);
    } catch (err) {
      setLoader(false);
      Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };

  interface OfferData {
    jobId: string;
    userId: string;
    userType: string;
    counterOfferId?: string | number;
  }
  interface JobData {
    jobId: number | string;
    counterOfferId?: number | string;
  }
  const getJobDetails = async (data: JobData) => {
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.getJobDetailsApproval,
        data,
        method: 'POST',
      });
      console.log('res ===>', res);

      return res;
    } catch {}
  };
  const getSimpleJobDetails = async (jobId: number | string) => {
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.jobDetail + `/${jobId}`,
        method: 'GET',
      });
      return res;
    } catch {}
  };

  interface PaymentData {
    amount?: number;
    jobId?: number;
    currency?: string;
    seekerId?: any;
    timezone?: any;
    counterOfferId?: any;
  }

  interface NewJobInterface {
    data?: {
      jobId?: number | string;
      totalEstimateCharge?: number;
      seekerPayableAmount?: number;
      seekerId?: string | number;
    };
  }

  // Payment api
  const fetchPaymentSheetParams = async (
    amount = 20,
    id = null,
    jobItem: any = null,
    offerData: any = null,
  ) => {
    console.log(
      'amount ===>',
      jobDetail?.isDirectApplied,
      chatData?.message?.isDirectApplied,
      offerData && !jobDetail?.isDirectApplied,
      offerData,
      jobItem,
    );
    let data: PaymentData = {};

    let d: OfferData = {
      jobId: isChatView
        ? chatData?.message?.jobId
        : jobDetail?.jobId || jobDetail?.id,
      userId: userData?.id,
      userType: 'employer',
    };

    let newJobDetails: NewJobInterface = {};
    // const isDirectApplied = chatData
    //   ? chatData?.message?.isDirectApplied
    //   : jobDetail?.isDirectApplied;
    const isNotSimpleJob = offerData && offerData?.counterOfferId;
    if (isNotSimpleJob) {
      d.counterOfferId = offerData?.counterOfferId;
      data.counterOfferId = offerData?.counterOfferId;
      newJobDetails = await getJobDetails(d);
    } else {
      newJobDetails = await getSimpleJobDetails(
        jobDetail?.jobId || jobDetail?.id || chatData?.message?.jobId,
      );
    }

    // if (Number(amount) > 0) {
    data.amount = Number(
      isEmployer
        ? newJobDetails?.data?.totalEstimateCharge || amount || 0
        : newJobDetails?.data?.seekerPayableAmount || amount || 0,
    );
    data.jobId =
      newJobDetails?.data?.jobId ||
      jobDetail?.jobId ||
      jobDetail?.id ||
      chatData?.message?.jobId;
    data.currency = 'USD';
    data.seekerId = isNotSimpleJob
      ? newJobDetails?.data?.seekerId
      : id
        ? id
        : selectedRoom?.userId === userData?.id
          ? selectedRoom?.applicantId
          : selectedRoom?.userId;
    data.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    // }

    console.log(
      '🚀 ~ fetchPaymentSheetParams ~ data:',
      selectedRoom?.userId === userData?.id
        ? selectedRoom?.applicantId
        : selectedRoom?.userId,
      selectedRoom,
      data,
    );
    const resp = await getApiData({
      endpoint: BaseSetting.endpoints.getStripIntentId,
      method: 'POST',
      data: data,
    });
    console.log('🚀 ~ fetchPaymentSheetParams ~ resp:', resp);
    if (!resp?.status) {
      Toast.show(resp?.message || translate('error'), Toast.LONG);
    }

    const { paymentIntent, ephemeralKey, customer, paymentIntentId } = resp?.data || {};

    return {
      paymentIntentId,
      paymentIntent,
      ephemeralKey,
      customer,
    };
  };

  const initializePaymentSheet = async (
    id?: any,
    item?: any,
    offerData?: OfferData,
  ) => {
    console.log('paymentIntent ===>', offerData);
    const { paymentIntentId, paymentIntent, ephemeralKey, customer } =
      await fetchPaymentSheetParams(
        jobDetail?.totalEstimateCharge || item?.totalEstimateCharge,
        id,
        item,
        offerData,
      );

    const { error } = await initPaymentSheet({
      merchantDisplayName: 'The Harbor App',
      customerId: customer,
      customerEphemeralKeySecret: ephemeralKey,
      paymentIntentClientSecret: paymentIntent,
      // Set `allowsDelayedPaymentMethods` to true if your business can handle payment
      //methods that complete payment after a delay, like SEPA Debit and Sofort.
      allowsDelayedPaymentMethods: true,
      // defaultBillingDetails: {
      //   name: 'Jane Doe',
      // },
      googlePay: {
        merchantCountryCode: 'US',
        testEnv: false, // use test environment
      },
      applePay: {
        merchantCountryCode: 'US',
      },
      returnURL: 'com.harbor.newapp://stripe-redirect',
    });
    setLoader(false);
    setState((p: any) => ({ ...p, customOfferLoader: false }));
    if (!error) {
      openPaymentSheet(offerData, paymentIntentId);
      // setLoading(true);
    }
  };

  const updateCounterOffer = async (offerData: any) => {
    const url = BaseSetting.endpoints.updateCounterOfferEdit;
    const apiData = {
      ...offerData,
      userId: userData?.id,
      jobId: jobDetail?.id || null,
      userType: 'employer',
      status: String(offerData?.type).toLowerCase(),
    };
    console.log('onsubmit apiData ==>', apiData);
    // return false;
    try {
      setState((p: any) => ({ ...p, customOfferLoader: true }));
      const response = await getApiData({
        endpoint: url,
        data: apiData,
        method: 'POST',
      });
      if (response?.status) {
        handleUpdateJob();
        console.log('response Approved ===>', response);
        // Goes to back and update data
        if (response?.message) {
          Toast.show(response?.message || translate('err'), Toast.SHORT);
        }
      } else {
        Toast.show(response?.message || translate('err'), Toast.SHORT);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  interface paymentData {
    paymentIntentId: any;
    status?: string;
  }

  const updatePaymentStatus = async (paymentIntent: any, type?: string) => {
    let url = BaseSetting.endpoints.updatePaymentStatus;
    if (type === 'cancel') {
      url = BaseSetting.endpoints.cancelPayment;
    }
    const apiData: paymentData = {
      paymentIntentId: paymentIntent,
    };
    if (type === 'success') {
      apiData.status = 'processing';
    }
    try {
      const response = await getApiData({
        endpoint: url,
        data: apiData,
        method: 'POST',
      });
      if (response?.status) {
        console.log('response Approved ===>', response);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };
  const openPaymentSheet = async (offerData?: any, paymentIntent?: any) => {
    console.log('🚀 ~ openPaymentSheet ~ error:');
    const { error } = await presentPaymentSheet();
    console.log('🚀 ~ openPaymentSheet ~ error:', error);

    if (error) {
      // Hide payment processing modal on error
      dispatch(UserConfigActions.setPaymentProcessing(false));
      Alert.alert(`Error code: ${error.code}`, error.message);
      updatePaymentStatus(paymentIntent, 'cancel');
    } else {
      // Show payment processing modal using global state
      // dispatch(UserConfigActions.setPaymentProcessing(true));
      console.log(
        'jobDetail offerData ===>',
        offerData,
        jobDetail?.isDirectApplied,
      );

      updatePaymentStatus(paymentIntent, 'success');

      handleUpdateJob(isChatView ? chatData : jobDetail, 'payment');
      if (offerData && offerData?.counterOfferId) {
        updateCounterOffer(offerData);
      }
      if (
        chatData?.messageType === 'custom_harbor' &&
        chatData?.message?.isFlatRate
      ) {
        updateInChat('regularJob');
      } else {
        updateInChat('');
      }
      setTimeout(() => {
        dispatch(setUpdateChat(true));
      }, 500);
      // updateJobStatus('Approved');
      // setPaymentInProcess(true);
      // getJobDetails(jobDetail?.jobId || jobDetail?.id);
      // Alert.alert('Success', 'Your order is confirmed!');
    }
  };

  const getDescriptionText = () => {
    if (item?.counterOfferId) {
      return item?.message;
    }
    if (item?.opponentData?.message) {
      return item?.opponentData?.message;
    }
    if (applicantType === 'applicantType') {
      return item?.approvedApplicant?.about || '--------';
    }
    return item?.approvedApplicant?.about || item?.about || '';
  };

  const description = getDescriptionText();

  const jobConfirmView = chatData?.messageType === 'Job_confirmation';

  const renderCustomOfferButton = () => {
    return (
      <View style={styles.applicationsBtns}>

        {jobDetail?.paymentStatus === 'processing' || chatData?.processing || jobDetail?.paymentProcessing ? (
          <BUTTON
            style={styles.paymentProcessingBtn}
            txtStyles={{ fontSize: 14 }}
            type="outlined">
            {translate('processing', '')}
          </BUTTON>
        ) : ( <>
          {((chatData?.messageType === 'custom_harbor' &&
          chatData?.message?.isFlatRate &&
          chatData?.message?.isDirectApplied) ||
          chatData?.messageType === 'counter_offer') && (
            <BUTTON
              disable={chatData?.message?.isDisable}
              onPress={() => {
                if (
                  chatData?.messageType === 'custom_harbor' &&
                chatData?.message?.isFlatRate &&
                chatData?.message?.isDirectApplied
                ) {
                  applyForJob();
                } else {
                  handleClick(
                    item,
                    isEmployer &&
                    chatData?.message?.offerBy === 'employer' &&
                    chatData?.message?.isAccepted
                      ? 'pay'
                      : 'Approved',
                  );
                }
              }}
              loading={loading === chatData?.id}
              style={{
                ...styles.approveBtn,
                width:
                isEmployer &&
                chatData?.message?.offerBy === 'employer' &&
                chatData?.message?.isAccepted
                  ? '85%'
                  : '45%',
              }}
              txtStyles={{ fontSize: 14 }}
              type="text">
              {translate(
                isEmployer &&
                chatData?.message?.offerBy === 'employer' &&
                chatData?.message?.isAccepted
                  ? 'yesApprove'
                  : 'Approve',
                '',
              )}
            </BUTTON>
          )}
          {(chatData?.messageType === 'custom_harbor' ||
          chatData?.messageType === 'counter_offer') && (
            <>
              {chatData?.message?.offerBy !== 'employer' &&
              !chatData?.message?.isAccepted && (
                <BUTTON
                  disable={loading === chatData?.id || chatData?.message?.isDisable}
                  loading={false}
                  onPress={() => {
                    if (isProfileSetupNotDone) {
                      setModalOpen((p: any) => ({
                        ...p,
                        confirmationModal: true,
                      }));
                      return false;
                    }
                    if (!chatData?.message?.isDisable  && loading !== chatData?.id) {
                      handleClick(item, 'counter');
                    }
                  }}
                  style={{
                    ...styles.counterBtnstyl,
                    width:
                      (chatData?.messageType === 'custom_harbor' &&
                        chatData?.message?.isFlatRate &&
                        chatData?.message?.isDirectApplied) ||
                      chatData?.messageType === 'counter_offer'
                        ? '40%'
                        : '80%',
                  }}
                  txtStyles={{ color: BaseColors.primary, fontSize: 14 }}
                  type="outlined">
                  {translate('counter', '')}
                </BUTTON>
              )}
              <TouchableOpacity
                style={{
                  ...styles.crossMainView,
                  borderColor: loading === chatData?.id || chatData?.message?.isDisable
                    ? BaseColors.borderColor
                    : BaseColors.red,
                }}
                onPress={() => {
                  if (!chatData?.message?.isDisable && loading !== chatData?.id) {
                  // if (chatData?.messageType === 'custom_harbor') {
                  //   // cancelApplicationApi(`${chatData?.id}cancel`,);
                  //   handleClick(item, 'reject');
                  // } else {
                    handleClick(item, 'Declined');
                  // }
                  }
                }}>
                {`${chatData?.id}cancel` === loading ? (
                  <ActivityIndicator color={BaseColors.red} />
                ) : (
                  <Entypo
                    name={'cross'}
                    size={24}
                    color={
                      loading === chatData?.id || chatData?.message?.isDisable
                        ? BaseColors.borderColor
                        : BaseColors.red
                    }
                  />
                )}
              </TouchableOpacity>
              {/* ) : null} */}
            </>
          )}</>)}
      </View>
    );
  };

  const renderDirectJobButton = (fromPay = false) => {
    const isCustomOfferPassed = !(
      (chatData?.messageType === 'custom_harbor' &&
        chatData?.message?.isDisable) ||
      chatData?.message?.isPayment
    );
    return (
      <View style={styles.applicationsBtns}>
        {!fromPay && chatData?.message?.isApproved !== 1 ? (
          <BUTTON
            style={{ ...styles.counterBtn }}
            loading={loading === chatData?.id}
            onPress={() => {
              if (isProfileSetupNotDone) {
                setModalOpen((p: any) => ({
                  ...p,
                  confirmationModal: true,
                }));
                return false;
              }
              applyForJob();
            }}
            type="text"
            disable={chatData?.message?.isDisable}
            // block
          >
            {translate(!isEmployer ? 'accept' : 'apply')}
          </BUTTON>
        ) : null}

        {fromPay && isCustomOfferPassed ? (
          <>
            {jobDetail?.paymentStatus === 'processing' || chatData?.processing || jobDetail?.paymentProcessing ? (
              <BUTTON
                style={styles.paymentProcessingBtn}
                txtStyles={{ fontSize: 14 }}
                type="outlined">
                {translate('processing', '')}
              </BUTTON>
            ) : (
              <BUTTON
                style={{ ...styles.counterBtn }}
                type="text"
                disable={
                  ((chatData?.messageType === 'custom_harbor' ||
                chatData?.messageType === 'counter_offer') &&
                chatData?.message?.isDisable) ||
              chatData?.message?.isPayment
                }
                loading={loading === chatData?.id}
                onPress={() => {
                  if (!chatData?.message?.isPayment) {
                    const offerData = {
                      offerBy: chatData?.message?.offerBy,
                      counterOfferId: chatData?.message?.counterOfferId,
                      type: 'approved', //chatData?.messageType,
                    };
                    console.log(
                      'chatData?.messageType ===>',
                      chatData?.messageType,
                      chatData?.message?.type,
                    );
                    // return false;
                    setLoader(chatData?.id);
                    initializePaymentSheet(item?.seekerId, item, offerData);
                  }
                }}>
                {/* pay or paid text */}
                {translate(chatData?.message?.isPayment ? 'Paid' : 'yesApprove')}
              </BUTTON>
            )}
          </>
        ) : null}
        {(!isEmployer &&
          chatData?.messageType === 'custom_harbor' &&
          chatData?.message?.status === 'pending') ||
        (fromPay && isEmployer && isCustomOfferPassed) ? (
          // <Button
          //   size={isSizeSmall(size) ? 'small' : 'middle'}
          //   loading={confirmLoader}
          //   disabled={chatData?.message?.isDisable}
          //   onClick={
          //     fromPay
          //       ? (event) => {
          //         event?.preventDefault();
          //         event?.stopPropagation();
          //         setConfirmModel({
          //           open: true,
          //           type: 'declineApplicant',
          //           data: {
          //             title: trans('common.btn.declineApplicant'),
          //             description: trans('common.subHeading.sureToDecline'),
          //           },
          //         });
          //       }
          //       : (event) => {
          //         event?.preventDefault();
          //         event?.stopPropagation();
          //         cancelApplication();
          //       }
          //   }
          //   type="primary"
          //   color="danger"
          //   variant="outlined"
          //   block
          // >
          //   {trans('common.reject')}
          // </Button>

            <TouchableOpacity
              style={{
                ...styles.crossMainView,
                borderColor: chatData?.message?.isDisable
                  ? BaseColors.borderColor
                  : BaseColors.red,
              }}
              onPress={() => {
                if (!chatData?.message?.isDisable) {
                  if (isProfileSetupNotDone) {
                    setModalOpen((p: any) => ({
                      ...p,
                      confirmationModal: true,
                    }));
                    return false;
                  }
                  // if (chatData?.messageType === 'custom_harbor') {
                  //   // cancelApplicationApi(`${chatData?.id}cancel`,);
                  //   handleClick(item, 'reject');
                  // } else {
                  handleClick(item, 'Declined');
                // }
                }
              }}>
              {`${chatData?.id}cancel` === loading ? (
                <ActivityIndicator color={BaseColors.red} />
              ) : (
                <Entypo
                  name={'cross'}
                  size={24}
                  color={
                    chatData?.message?.isDisable
                      ? BaseColors.borderColor
                      : BaseColors.red
                  }
                />
              )}
            </TouchableOpacity>
          ) : null}
      </View>
    );
  };

  const renderChatButton = () => {
    // chatData?.message?.userJob?.status === 'Approved' ||
    return (chatData?.message?.userJob?.status === 'Pending' && isEmployer) ||
      (chatData?.messageType === 'Job_confirmation' &&
        chatData?.message?.userId === userData?.id) ? null : chatData?.message
        ?.isDeclined || chatData?.message?.userJob?.status === 'Declined' ? (
          <BUTTON
            disable
            style={{ ...styles.counterBtn, ...styles.applied }}
            onPress={() => {}}>
            {translate('Declined')}
          </BUTTON>
        ) : (chatData?.message?.isAccepted ||
        chatData?.message?.isFinal ||
        chatData?.message?.userJob?.status === 'Applied' ||
        chatData?.message?.userJob?.status === 'Approved') &&
      chatData?.message?.userId === userData?.id ? (
        // <BUTTON
        //   style={{ ...styles.counterBtn, ...styles.applied }}
        //   type="text"
        //   disable={chatData?.message?.isPayment}
        //   onPress={() => {
        //     if (!chatData?.message?.isPayment) {
        //       initializePaymentSheet(item?.seekerId, item);
        //     }
        //   }}>
        //   {translate(chatData?.message?.isPayment ? 'Paid' : 'Pay')}
        // </BUTTON>

            chatData?.message?.isDirectApplied &&
      chatData?.message?.type === 'custom' ? (
                renderDirectJobButton(true)
              ) : (
                renderCustomOfferButton()
              )
          ) : (chatData?.message?.isAccepted ||
        chatData?.message?.isFinal ||
        chatData?.message?.userJob?.status === 'Applied' ||
        chatData?.message?.userJob?.status === 'Approved') &&
      chatData?.message?.userId !== userData?.id ? (
              <BUTTON
                disable
                style={{ ...styles.counterBtn, ...styles.applied }}
                onPress={() => {}}>
                {translate(chatData?.message?.isPayment ? 'Approved' : 'Applied')}
              </BUTTON>
            ) : chatData?.sender === userData?.id ? (
              <View style={styles.columnBtns}>
                <BUTTON
                  style={styles.waitBtnChat}
                  txtStyles={{ fontSize: 14, color: BaseColors.textGrey }}
                  type="outlined">
                  {translate('waitForAppr', '')}
                </BUTTON>
                <BUTTON
                  onPress={event => {
                    event?.preventDefault();
                    event?.stopPropagation();
                    handleClick(item, 'edit');
                  }}
                  txtStyles={{ color: BaseColors.primary, fontSize: 14 }}
                  type="outlined"
                  disable={chatData?.message?.isDisable}>
                  {translate('edit', '')}
                </BUTTON>
              </View>
            ) : jobDetail?.isPriceOptional ? (
              <View style={styles.applicationsBtns}>
                {jobDetail?.paymentStatus === 'processing' || chatData?.processing || jobDetail?.paymentProcessing ? (
                  <BUTTON
                    style={styles.paymentProcessingBtn}
                    txtStyles={{ fontSize: 14 }}
                    type="outlined">
                    {translate('processing', '')}
                  </BUTTON>
                ) : (
                  <>
                    {((chatData?.messageType === 'custom_harbor' &&
                        chatData?.message?.isDirectApplied) ||
                        chatData?.messageType === 'counter_offer') && (
                      <BUTTON
                        disable={chatData?.message?.isDisable}
                        // onPress={(event) => {
                        //   event?.preventDefault();
                        //   event?.stopPropagation();
                        // // openActionModal('accept', data);
                        // }}
                        onPress={() => {
                          if (isProfileSetupNotDone) {
                            setModalOpen((p: any) => ({
                              ...p,
                              confirmationModal: true,
                            }));
                          } else if (chatData?.messageType === 'custom_harbor') {
                            applyForJob();
                          } else {
                            handleClick(item, 'Approved');
                          }
                        }}
                        loading={loading === chatData?.id}
                        style={styles.approveBtn}
                        txtStyles={{ fontSize: 14 }}
                        type="text">
                        {translate('Approve', '')}
                      </BUTTON>
                    )}
                    {chatData?.messageType === 'custom_harbor' ||
        chatData?.messageType === 'counter_offer' ? (
                        <>
                          <BUTTON
                            disable={loading === chatData?.id || chatData?.message?.isDisable}
                            onPress={() => {
                              if (isProfileSetupNotDone) {
                                setModalOpen((p: any) => ({
                                  ...p,
                                  confirmationModal: true,
                                }));
                              } else if (!chatData?.message?.isDisable) {
                                handleClick(item, 'counter');
                              }
                            }}
                            style={{
                              ...styles.counterBtnstyl,
                              width:
                  (chatData?.messageType === 'custom_harbor' &&
                    chatData?.message?.isDirectApplied) ||
                  chatData?.messageType === 'counter_offer'
                    ? '40%'
                    : '80%',
                            }}
                            txtStyles={{ color: BaseColors.primary, fontSize: 14 }}
                            type="outlined">
                            {translate('counter', '')}
                          </BUTTON>
                          <TouchableOpacity
                            style={{
                              ...styles.crossMainView,
                              borderColor: loading === chatData?.id || chatData?.message?.isDisable
                                ? BaseColors.borderColor
                                : BaseColors.red,
                            }}
                            onPress={() => {
                              if (!chatData?.message?.isDisable) {
                                if (isProfileSetupNotDone) {
                                  setModalOpen((p: any) => ({
                                    ...p,
                                    confirmationModal: true,
                                  }));
                                  return false;
                                }
                                // if (chatData?.messageType === 'custom_harbor') {
                                //   // cancelApplicationApi(`${chatData?.id}cancel`,);
                                //   handleClick(item, 'reject');
                                // } else {
                                handleClick(item, 'Declined');
                                // }
                              }
                            }}>
                            {`${chatData?.id}cancel` === loading ? (
                              <ActivityIndicator color={BaseColors.red} />
                            ) : (
                              <Entypo
                                name={'cross'}
                                size={24}
                                color={
                                  loading === chatData?.id || chatData?.message?.isDisable
                                    ? BaseColors.borderColor
                                    : BaseColors.red
                                }
                              />
                            )}
                          </TouchableOpacity>
                        </>
                      ) : null}
                  </>) }
              </View>
            ) : jobDetail?.data?.isPriceOptional ? (
              renderCustomOfferButton()
            ) : chatData?.message?.isDirectApplied ? (
              renderDirectJobButton()
            ) : null;
    // item?.isDirectApplied ? (
    //   <View style={styles.applicationsBtns}>
    //     {isApproved && item?.isDirectApplied ? (
    //       <BUTTON disable onPress={() => {}} style={styles.counterBtn}>
    //         {/* Approved */}
    //         {translate('Approved')}
    //       </BUTTON>
    //     ) : String(chatData?.message?.userJob?.status).toLowerCase() === 'declined' ? (
    //       <BUTTON disable style={{ ...styles.counterBtn, ...styles.applied }} onPress={() => {}}>
    //         {translate('Declined')}
    //       </BUTTON>
    //     ) : (
    //       <>
    //         {
    //           jobDetail?.isApproved !== 1 &&
    //             (
    //               <BUTTON
    //                 onPress={() => {
    //                   if (isEmployer) {
    //                     handleClick(item, 'Approved');
    //                   } else {
    //                     applyForJob();
    //                   }
    //                 }}
    //                 style={{ ...styles.approveBtn, width: '86%' }}
    //                 txtStyles={{ fontSize: 14 }}
    //                 loading={loading}
    //                 type="text">
    //                 {translate(isEmployer ? 'yesApprove' : 'accept')}
    //               </BUTTON>
    //             )}
    //         {!isEmployer && chatData?.messageType === 'custom_harbor' && chatData?.message?.status === 'pending' && (
    //           <TouchableOpacity
    //             style={styles.crossMainView}
    //             onPress={() => {
    //               // cancelApplicationApi();
    //               handleClick(item, 'reject', true);
    //             }}>
    //             <Entypo name={'cross'} size={24} color={BaseColors.red} />
    //           </TouchableOpacity>
    //         )}
    //       </>
    //     )}
    //   </View>
    // ) : null;
  };

  // Get initials from the name
  // const getInitials = (name: any) => {
  //   if (!name) {
  //     return '';
  //   }
  //   const nameParts = name.split(' ');
  //   const initials = (nameParts[0]?.[0] || '') + (nameParts[1]?.[0] || '');
  //   return initials.toUpperCase();
  // };

  const cardData = isChatView
    ? chatData?.message
    : isHistoryView
      ? item
      : item?.opponentData;

  // isHistoryView
  //   ? item
  //   : !isEmployer && isOfferByEmployer
  //     ? item?.opponentData
  //     : isEmployer && isOfferByEmployer
  //       ? item?.opponentData
  //       : item;
  // const firstName = item?.approvedApplicant?.firstName || item?.firstName;

  // const profilePhoto =
  //     item?.approvedApplicant?.profilePhoto || item?.profilePhoto;

  // const initials = getInitials(`${firstName}`);

  const avgRate = cardData?.rating || cardData?.averageRate || 0;
  const avgCount = cardData?.reviews || cardData?.ratingCount || 0;

  // const isOfferByMe =
  //     isEmployer &&
  //     isOfferByEmployer &&
  //     !isHistoryView &&
  //     item?.sendBy === userData?.id;

  // const nm =
  //     item?.firstName || item?.lastName
  //       ? `${capitalizeFirstLetter(item?.firstName)} ${capitalizeFirstLetter(
  //         item?.lastName?.charAt(0),
  //       )}.`
  //       : item?.approvedApplicant?.firstName || item?.approvedApplicant?.lastName
  //         ? `${capitalizeFirstLetter(
  //           item?.approvedApplicant?.firstName,
  //         )} ${capitalizeFirstLetter(
  //           item?.approvedApplicant?.lastName?.charAt(0),
  //         )}.`
  //         : '';

  // const ownName = `${capitalizeFirstLetter(
  //   userData?.firstName,
  // )} ${capitalizeFirstLetter(userData?.lastName)}`;

  const name = `${capitalizeFirstLetter(
    cardData?.firstName,
  )} ${capitalizeFirstLetter(cardData?.lastName?.charAt(0))}`;
  // const name = `${
  //   !isEmployer && isOfferByEmployer && !isHistoryView
  //     ? item?.opponentData?.firstName || item?.firstName || '-'
  //     : isOfferByMe
  //       ? ownName
  //       : nm
  // } ${
  //   !isEmployer && isOfferByEmployer && !isHistoryView
  //     ? item?.opponentData?.lastName || item?.lastName || ''
  //     : ''
  // }`;

  // const prPhot = item?.approvedApplicant?.profilePhoto
  //   ? item?.approvedApplicant?.profilePhoto
  //   : item?.profilePhoto;

  const url =
    cardData?.imageUrl ||
    cardData?.profilePhoto ||
    chatData?.message?.userData?.profilePhoto;
  // !isEmployer && isOfferByEmployer && !isHistoryView
  //   ? item?.opponentData?.imageUrl || item?.profilePhoto
  //   : isOfferByMe
  //     ? userData?.profilePhoto
  //     : prPhot;
  // const loc = item?.approvedApplicant?.location
  //   ? item?.approvedApplicant?.location
  //   : item?.location || '';
  const [isvalidImage, setIsValidImage] = useState(false);
  checkImageExists(`${url}`).then((isValid: any) => {
    setIsValidImage(isValid);
  });

  const location =
    cardData?.shortAddress && cardData?.shortAddress !== null
      ? cardData?.shortAddress
      : cardData?.location || ''; // cardData?.location;
  // !isEmployer && isOfferByEmployer && !isHistoryView
  //   ? item?.opponentData?.location || item?.location
  //   : loc;
  // const outgoing = isEmployer && item?.offerBy === 'employer';
  // const isOwnOffer = !isEmployer && item?.offerBy === 'seeker';

  const salary =
    item?.salaryAmount || item?.finalPrice || jobDetail?.salaryAmount;

  const badge = cardData?.badgeInfo?.currentBadge;
  // !isEmployer && isOfferByEmployer && !isHistoryView
  //   ? item?.opponentData?.badgeInfo?.currentBadge ||
  //     item?.badgeInfo?.currentBadge
  //   : item?.approvedApplicant?.badgeInfo?.currentBadge ||
  //     item?.badgeInfo?.currentBadge || item?.opponentData?.badgeInfo?.currentBadge;

  const redirectScreen = () => {
    if (item?.counterOfferId && !isHistoryView) {
      navigation.navigate('CounterHistory', {
        item,
        job: jobDetail,
      });
    } else if (type === 'applicants') {
      navigation.navigate('ApplicantDetails', {
        userId: item?.id,
        type: 'viewEmployer',
      });
    }
  };

  return (
    <View>
      <TouchableOpacity
        activeOpacity={1}
        disabled={jobtype === 'jobDetail' ? true : false}
        onPress={
          chatData
            ? () => {
              navigation.navigate('JobApplicant', {
                jobID: chatData?.message?.jobId,
              });
            }
            : redirectScreen
        }
        style={[
          styles.cardBorder,
          !isChatView && {
            width:
              bodyType === 'home'
                ? Dimensions.get('screen').width * 0.95
                : '100%',
            marginHorizontal: 0,
          },
          { marginBottom: 0 },
        ]}>
        <View style={{ ...styles.innerPadding }}>
          {jobConfirmView ? (
            <Text style={styles.confirmText}>
              {translate(isEmployer ? 'paymentConfirmed' : 'jobConfirmed')}
            </Text>
          ) : null}
          <View style={styles.rowDirection}>
            <View style={styles.imgView}>
              {!imageError && isvalidImage ? (
                <FastImage
                  source={
                    url
                      ? { uri: url }
                      : cardData?.gender === 'female'
                        ? Images.female
                        : Images.user
                  }
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: width * 0.6,
                  }}
                  resizeMode="cover"
                  onError={() => setImageError(true)} // Set error state on image load failure
                />
              ) : (
                <FastImage
                  source={Images.user}
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: width * 0.6,
                  }}
                  resizeMode="cover"
                />
              )}
              {USER_VERIFIED ? (
                <View
                  style={{
                    height: 20,
                    width: 20,
                    position: 'absolute',
                    bottom: -5,
                    right: -8,
                  }}>
                  <FastImage
                    source={Images.verified}
                    style={{
                      width: '100%',
                      height: '100%',
                      borderRadius: width * 0.0125, // Assuming 5 is about 1.25% of the width
                    }}
                    resizeMode={FastImage.resizeMode.cover} // Optional: Set resize mode if needed
                  />
                </View>
              ) : null}
            </View>
            <View style={styles.txtViewSty}>
              <View style={[styles.center, styles.flexRow]}>
                <TouchableOpacity
                  activeOpacity={0.6}
                  onPress={() => {
                    const id = checkEmployerId
                      ? item?.seekerId
                      : item?.employerId;

                    // if (chatData) {
                    //   navigation.navigate('JobApplicant', { jobID: chatData?.message?.jobId });
                    // }
                    // else
                    if (!chatData && id) {
                      navigation.navigate('ApplicantDetails', {
                        userId: id,
                        type: 'viewEmployer',
                      });
                    }
                  }}
                  style={styles.infoRow}>
                  <Text numberOfLines={1} style={[styles.titleTxtSty]}>
                    {isChatView ? item?.title : name}
                  </Text>
                  {item?.isAvailable ? (
                    <View
                      style={[
                        BaseStyles.onlineIndicator,
                        { marginTop: 4, marginLeft: 5 },
                      ]}
                    />
                  ) : null}
                </TouchableOpacity>

                <View style={styles.bookmarkContainer}>
                  <View style={styles.imageView}>
                    <FastImage
                      source={getBadgeImage(badge)}
                      style={styles.medalIcon}
                      resizeMode="contain"
                    />
                  </View>
                </View>
              </View>
              <View style={[styles.infoRow, styles.center, { flex: 1 }]}>
                <TouchableOpacity style={[styles.infoRow]} activeOpacity={1}>
                  <CustomIcon
                    name="jobLocation"
                    size={14}
                    color={BaseColors.primary}
                  />

                  <Text numberOfLines={1} style={styles.desTxtSty}>
                    {location}
                  </Text>
                </TouchableOpacity>

                <View style={styles.DataCustomSty}>
                  {/* {avgRate ? ( */}
                  <View style={styles.reviewViewSty}>
                    <CustomIcon name="fillStar" color={BaseColors.starColor} />
                    <Text style={styles.ratingTxtSty}>
                      {avgRate ? Number(avgRate).toFixed(1) : '0' || '0'}
                    </Text>
                    {avgCount ? (
                      <Text>({avgCount || '0'})</Text>
                    ) : (
                      <Text>({'0'})</Text>
                    )}
                  </View>
                  {/* ) : null} */}
                </View>
              </View>
            </View>
          </View>

          <View style={styles.underlineViewSty} />
          <View style={styles.bodyDataSty}>
            <View style={[styles.infoRow, styles.center]}>
              <View style={styles.infoRow}>
                <CustomIcon
                  name="Calander"
                  size={16}
                  color={BaseColors.logoColor}
                  // style={{ backgroundColor: 'red' }}
                />
                {isEmpty(item?.startDateTime || jobDetail?.startDateTime) &&
                isEmpty(item?.endDateTime || jobDetail?.endDateTime) ? (
                    <Text style={styles.jobdestxtSty}>
                      {translate('datenotAvailable', '')}
                    </Text>
                  ) : (
                    <Text style={styles.jobdestxtSty}>
                      {item?.startDateTime || jobDetail?.startDateTime
                        ? dayjs(
                          item?.startDateTime || jobDetail?.startDateTime,
                        ).format('M/D/YYYY')
                        : ''}
                      {' - '}
                      {item?.endDateTime || jobDetail?.endDateTime
                        ? dayjs(
                          item?.endDateTime || jobDetail?.endDateTime,
                        ).format('M/D/YYYY') // Update moment to dayjs
                        : '-'}
                    </Text>
                  )}
              </View>

              {/* {!item?.isDirectApplied ? ( */}
              <View style={[styles.saleryCustomViewSty, styles.infoRow]}>
                {/* {item?.isDirectApplied ? null : (
                  <Feather
                    name={
                      outgoing || isOwnOffer
                        ? 'arrow-up-right'
                        : 'arrow-down-left'
                    }
                    color={BaseColors.primary}
                    size={16}
                  />
                )} */}
                <Text style={{ ...styles.saleryTXtSty, marginLeft: 2 }}>
                  {salary ? '$' : 'Offer'}
                  {formatSalary(salary)}
                  {item?.duration && salary ? '/' : ''}
                  {item?.duration && salary ? getDuration(item?.duration) : ''}
                </Text>
              </View>
              {/* ) : null} */}
            </View>
            <View style={styles.infoRow}>
              <CustomIcon name="clock" size={16} color={BaseColors.logoColor} />
              {isEmpty(item?.startTime || jobDetail?.startTime) &&
              isEmpty(item?.endTime || jobDetail?.endTime) ? (
                  <Text style={styles.jobdestxtSty}>
                    {translate('timenotAvailable', '')}
                  </Text>
                ) : (
                  <Text style={styles.jobdestxtSty}>
                    {`${
                      item?.startDateTime || jobDetail?.startDateTime
                        ? dayjs(
                          item?.startDateTime || jobDetail?.startDateTime,
                        ).format('h:mm A') // Update moment to dayjs
                        : '-'
                    } - ${
                      item?.endDateTime || jobDetail?.endDateTime
                        ? dayjs(
                          item?.endDateTime || jobDetail?.endDateTime,
                        ).format('h:mm A') // Update moment to dayjs
                        : '-'
                    }`}
                  </Text>
                )}
            </View>
          </View>
          {description ? (
            <View style={styles.bodyDataSty}>
              <Text
                numberOfLines={showFullDescription ? undefined : 2}
                style={[
                  styles.decriptionTXtSty,
                  showFullDescription && { marginBottom: 8 },
                ]}
                onTextLayout={e => {
                  setNumberOfLines(e.nativeEvent.lines.length);
                }}>
                {`${description ? `${translate('note')}: ` : ''}`}
                {description}
              </Text>
              {numberOfLines >= 2 && (
                <TouchableOpacity
                  onPress={() => setShowFullDescription(!showFullDescription)}
                  style={{ marginTop: 4 }}>
                  <Text
                    style={{
                      color: BaseColors.primary,
                      fontSize: 14,
                      fontFamily: FontFamily.OpenSansRegular,
                    }}>
                    {showFullDescription ? 'Show Less' : 'Show More'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ) : null}

          <View style={styles.underlineViewSty} />
          {buttons === false || item?.isDisabled ? null : isChatView ? (
            renderChatButton()
          ) : (
            <View style={styles.applicationsBtns}>
              {isEmployer &&
              !item?.isDeclined &&
              (item?.isAccepted || item?.isFinal) &&
              !isApproved ? (
                  <>
                    {jobDetail?.paymentStatus === 'processing' || chatData?.processing || jobDetail?.paymentProcessing ? (
                      <BUTTON
                        style={styles.paymentProcessingBtn}
                        txtStyles={{ fontSize: 14 }}
                        type="outlined">
                        {translate('processing', '')}
                      </BUTTON>
                    ) : (
                      <>
                        {/* chatData?.message?.isDirectApplied && chatData?.message?.type === 'custom' */}
                        <BUTTON
                          onPress={() => {
                            handleClick(
                              item,
                              isEmployer &&
                          item?.offerBy === 'employer' &&
                          item?.isAccepted
                                ? 'pay'
                                : 'Approved',
                            );
                            // onActionClick(item, isEmployer && item?.offerBy === 'employer' && item?.isAccepted ? 'pay' : 'Approved');
                          }}
                          loading={
                            applicantsLoader?.type === 'Approved' &&
                      item?.id === applicantsLoader?.id
                          }
                          style={{
                            ...styles.approveBtn,
                            width:
                        isEmployer &&
                        item?.offerBy === 'employer' &&
                        item?.isAccepted
                          ? '85%'
                          : '45%',
                          }}
                          txtStyles={{ fontSize: 14 }}
                          type="text">
                          {translate(
                            item?.offerBy === 'employer' && item?.isAccepted
                              ? 'yesApprove'
                              : 'Approve',
                            '',
                          )}
                        </BUTTON>
                        {item?.offerBy !== 'employer' && !item?.isAccepted && (
                          <BUTTON
                            loading={false}
                            onPress={() => {
                              if (isProfileSetupNotDone) {
                                setModalOpen((p: any) => ({
                                  ...p,
                                  confirmationModal: true,
                                }));
                                return false;
                              }
                              handleClick(item, 'counter');
                              // onActionClick(item, 'counter');
                            }}
                            style={styles.counterBtnstyl}
                            txtStyles={{ color: BaseColors.primary, fontSize: 14 }}
                            type="outlined">
                            {translate('counter', '')}
                          </BUTTON>
                        )}

                        <TouchableOpacity
                          style={styles.crossMainView}
                          onPress={() => {
                            handleClick(item, 'Declined');
                            // onActionClick(item, 'Declined');
                          }}>
                          <Entypo name={'cross'} size={24} color={BaseColors.red} />
                        </TouchableOpacity>
                      </>
                    )}
                  </>
                ) : !isEmployer && item?.isAccepted ? (
                  <BUTTON disable style={styles.counterBtn} onPress={() => {}}>
                    {translate('Applied')}
                  </BUTTON>
                ) : !item?.isDeclined && item?.isFinal ? (
                  <BUTTON disable style={styles.counterBtn} onPress={() => {}}>
                    {translate('Approved')}
                  </BUTTON>
                ) : item?.isDeclined ? (
                  <BUTTON disable style={styles.counterBtn} onPress={() => {}}>
                    {translate('Declined')}
                  </BUTTON>
                ) : !item?.isAccepted &&
                !item?.isDeclined &&
                !item?.isFinal &&
                ((isEmployer && item?.offerBy === 'employer') ||
                  (!isEmployer && item?.offerBy === 'seeker')) ? (
                    <>
                      <BUTTON
                        style={styles.waitBtn}
                        txtStyles={{ fontSize: 14, color: BaseColors.textGrey }}
                        type="outlined">
                        {translate('waitForAppr', '')}
                      </BUTTON>
                      <BUTTON
                        onPress={() => {
                          handleClick(item, 'edit');
                          // onActionClick(item, 'edit');
                        }}
                        txtStyles={{ color: BaseColors.primary, fontSize: 14 }}
                        type="outlined">
                        {translate('edit', '')}
                      </BUTTON>
                    </>
                  ) : item?.isDirectApplied ? (
                    <>
                      {isApproved && item?.isDirectApplied ? (
                        <BUTTON
                          disable
                          onPress={() => {}}
                          style={styles.counterBtn}>
                          {/* Approved */}
                          {translate('Approved')}
                        </BUTTON>
                      ) : String(item?.userJob?.status).toLowerCase() ===
                      'pending' && jobDetail?.type === 'custom' ? null : (
                          <>
                            {jobDetail?.paymentStatus === 'processing' || chatData?.processing || jobDetail?.paymentProcessing ? (
                              <BUTTON
                                style={styles.paymentProcessingBtn}
                                txtStyles={{ fontSize: 14 }}
                                type="outlined">
                                {translate('processing', '')}
                              </BUTTON>
                            ) : (
                              <>
                                <BUTTON
                                  onPress={() => {
                                    handleClick(item, 'Approved');
                                    // onActionClick(item, 'Approved');
                                  }}
                                  style={{ ...styles.approveBtn, width: '86%' }}
                                  txtStyles={{ fontSize: 14 }}
                                  loading={loader}
                                  type="text">
                                  {translate('yesApprove')}
                                </BUTTON>
                                <TouchableOpacity
                                  style={styles.crossMainView}
                                  onPress={() => {
                                    onActionClick(item, 'reject');
                                  }}>
                                  <Entypo
                                    name={'cross'}
                                    size={24}
                                    color={BaseColors.red}
                                  />
                                </TouchableOpacity>
                              </>)}
                          </>
                        )}
                    </>
                  ) : jobDetail?.isPriceOptional ? (
                    <>
                      {jobDetail?.paymentStatus === 'processing' || chatData?.processing || jobDetail?.paymentProcessing ? (
                        <BUTTON
                          style={styles.paymentProcessingBtn}
                          txtStyles={{ fontSize: 14 }}
                          type="outlined">
                          {translate('processing', '')}
                        </BUTTON>
                      ) : (
                        <>
                          <BUTTON
                            onPress={() => {
                              if (isProfileSetupNotDone) {
                                setModalOpen((p: any) => ({
                                  ...p,
                                  confirmationModal: true,
                                }));
                                return false;
                              }
                              handleClick(item, 'Approved');
                              // onActionClick(item, 'Approved');
                            }}
                            loading={
                              applicantsLoader?.type === 'Approved' &&
                      item?.id === applicantsLoader?.id
                            }
                            style={styles.approveBtn}
                            txtStyles={{ fontSize: 14 }}
                            type="text">
                            {translate('Approve', '')}
                          </BUTTON>
                          <BUTTON
                            onPress={() => {
                              if (isProfileSetupNotDone) {
                                setModalOpen((p: any) => ({
                                  ...p,
                                  confirmationModal: true,
                                }));
                                return false;
                              }
                              handleClick(item, 'counter');
                              // onActionClick(item, 'counter');
                            }}
                            style={styles.counterBtnstyl}
                            txtStyles={{ color: BaseColors.primary, fontSize: 14 }}
                            type="outlined">
                            {translate('counter', '')}
                          </BUTTON>

                          <TouchableOpacity
                            style={styles.crossMainView}
                            onPress={() => {
                              handleClick(item, 'Declined');
                              // onActionClick(item, 'Declined');
                            }}>
                            <Entypo name={'cross'} size={24} color={BaseColors.red} />
                          </TouchableOpacity>
                        </>)}
                    </>
                  ) : null}
            </View>
          )}

          {buttons && (
            <Text
              style={[
                {
                  textAlign: 'center',
                  fontSize: 14,
                  color: BaseColors.logoColor,
                  fontFamily: FontFamily.OpenSansRegular,
                  lineHeight: 17,
                  paddingBottom: 10,
                },
              ]}>
              {translate('requestedOn')}:{' '}
              {moment(item?.createdAt).format('MMM D, YYYY - h:mma')}
            </Text>
          )}
        </View>
      </TouchableOpacity>
      <CustomOfferModal
        setState={setState}
        state={state}
        title={translate('customOffer')}
        jobDetail={jobDetail}
        onSubmit={(values: any) => {
          // getList();
          // Goes to back and update data
          onSubmit(values, 'custom');
        }}
        navigation={navigation}
        onCancel={handleOnClose}
      />
      {state.confirmationModal && (
        <AlertModal
          image
          title={translate('declineJob', '')}
          visible={state.confirmationModal}
          setVisible={(val: any) =>
            setState((p: any) => ({ ...p, confirmationModal: false }))
          }
          btnYPress={async () => {
            if (
              state?.type === 'cancelApplication' ||
              (!isEmployer &&
                chatData?.messageType === 'custom_harbor' &&
                chatData?.message?.status === 'pending') ||
              state?.applicant?.counterOfferId
            ) {
              setState((p: any) => ({ ...p, applicantsLoader: true }));
              cancelApplicationApi();
            } else if (state?.applicant?.counterOfferId) {
              onSubmit({});
            } else {
              setState((p: any) => ({ ...p, applicantsLoader: true }));
              const resp = await updateJobStatus('Declined', {
                jobId: jobDetail?.jobId || jobDetail?.id,
                userId: isChatView ? chatData?.receiver : state?.applicant?.id,
              });
              console.log('resp resp ==>', resp);
              if (resp?.status) {
                await updateInChat();
                resetState();
                // Goes to back and update data
                // getList();
              } else {
                Toast.show(resp?.message || translate('err'), Toast.SHORT);
                setState((p: any) => ({
                  ...p,
                  // confirmationModal: false,
                  applicantsLoader: false,
                  applicant: {},
                  job: {},
                }));
              }
            }
          }}
          loader={state?.applicantsLoader}
          btnYTitle={translate('DeclineOffer')}
          btnNTitle={translate('CANCEL')}
          btnNPress={() => {
            resetState();
          }}
          confirmation
        />
      )}
      {modalOpen.confirmationModal && (
        <AlertModal
          image
          title={translate('complete', '')}
          visible={modalOpen.confirmationModal}
          setVisible={(val: any) =>
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }))
          }
          lottieViewVisible
          btnYPress={() => {
            navigation.navigate('ProfileSetUp');
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
          }}
          loader={modalOpen?.loader}
          btnYTitle={translate('letsdo')}
          confirmation
          // completeProfile
          titlesty={{ textAlign: 'center' }}
          description={translate('postDescription', '')}
          btnNTitle={translate('maybe')}
          btnNPress={() => {
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
          }}
        />
      )}
    </View>
  );
};

export default CounterOfferCard;
