import {getRandomDarkColor} from '@app/utils/CommonFunction';
import {FontFamily} from '@config/typography';
import React, {useEffect, useState} from 'react';
import {Dimensions, StyleSheet, Text, View} from 'react-native';

const {width} = Dimensions.get('window');

// Function to generate a random dark color

const InitialsAvatar = ({initials, type = 'square'}: any) => {
  const [bgColor, setBgColor] = useState('');

  // Generate the background color only once
  useEffect(() => {
    setBgColor(getRandomDarkColor());
  }, []);

  return (
    <View
      style={[
        styles.initialsContainer,
        {
          backgroundColor: bgColor,
          borderRadius: width * 0.6,

          borderRadius: 50,
        },
      ]}>
      <Text style={styles.initialsText}>{initials}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  initialsContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    // borderRadius: 50, // Circular shape
    overflow: 'hidden',
    borderRadius: width * 0.0125,
  },
  initialsText: {
    fontSize: 20,
    color: '#FFF',
    fontFamily: FontFamily.OpenSansBold,
  },
});

export default InitialsAvatar;
