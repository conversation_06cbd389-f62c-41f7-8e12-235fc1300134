import i18n from 'i18n-js';

import actions from '../redux/reducers/language/actions';

import en from './en.json';
import es from './es.json';

const translationGetters = {
  en: () => en,
  es: () => es,
};

export const translate = (key: any, config?: any) => {
  if (!config) {
    config = {};
  }
  config.defaultValue = key;
  return i18n.t(key, config);
};
/**
 * set Language
 *@function  setI18nConfig
 */
const setI18nConfig = (language: any, store: any, bool: any, lang: any) => {
  const isRTL = false;
  let appLanguage = language;
  console.log('🚀 ~ setI18nConfig ~ appLanguage:', appLanguage);
  if (language === null) {
    appLanguage = 'en';
    store.dispatch({
      type: actions.SET_LANGUAGE,
      languageData: appLanguage,
    });
  }

  const ReactNative = require('react-native');
  try {
    ReactNative.I18nManager.allowRTL(isRTL);
    ReactNative.I18nManager.forceRTL(isRTL);
  } catch (e) {
    // console.log('Error in RTL', e);
  }
  i18n.defaultLocale = 'en';
  // i18n.translations = {
  //   [appLanguage || 'en']: translationGetters[appLanguage || 'en'](),
  // };
  i18n.translations = {
    [appLanguage]: translationGetters[appLanguage](),
  };
  i18n.locale = appLanguage;
  store.dispatch({
    type: actions.UPDATE_LANGUAGE,
    updateLanguage: !lang,
  });
};

export const initTranslate = (store: any, bool = false) => {
  const {
    language: {languageData, updateLanguage},
  } = store.getState();
  setI18nConfig(languageData, store, bool, updateLanguage);
};
