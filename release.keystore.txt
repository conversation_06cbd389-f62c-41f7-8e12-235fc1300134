keytool -list -v -keystore C:\Users\<USER>\Desktop\harborApp\android\app\androidrelease.keystore -alias my-key-alias -storepass android -keypass android 

Alias name: my-key-alias
Creation date: Mar 18, 2025
Entry type: PrivateKeyEntry
Certificate chain length: 1
Certificate[1]:
Owner: CN=groovy, OU=groovy, O=groovy, L=nadiad, ST=gujarat, C=IN
Issuer: CN=groovy, OU=groovy, O=groovy, L=nadiad, ST=gujarat, C=IN
Serial number: 7a1a781bd01eea73
Valid from: Tue Mar 18 16:58:22 IST 2025 until: Sat Aug 03 16:58:22 IST 2052
Certificate fingerprints:
         SHA1: 96:38:46:EA:1B:8A:42:4B:5D:4D:B5:1B:D7:1F:1D:61:FC:04:00:06
         SHA256: AE:8B:85:32:5B:AC:6B:1F:D7:AC:F9:35:B5:EB:BD:72:61:8B:F4:17:88:31:C0:C8:46:2D:63:C1:91:91:9C1:9C:8C1:9C:1:91:91:91:91:1:91:91:91:1:91:91:11:9C:8C:C1:F0
Signature algorithm name: SHA256withRSA
Subject Public Key Algorithm: 2048-bit RSA key
Version: 3

Extensions:

#1: ObjectId: 2.5.29.14 Criticality=false
SubjectKeyIdentifier [
KeyIdentifier [
0000: 58 A9 3A 06 60 AE 89 B7   9E 59 D8 C3 C2 46 76 E2  X.:.`....Y...Fv.
0010: EA DC 04 48                                        ...H
]
]