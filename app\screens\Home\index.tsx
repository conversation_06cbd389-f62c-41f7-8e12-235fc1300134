import React, { useEffect, useState } from 'react';
import {
  View,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  Text,
  Dimensions,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import styles from './styles';
import { translate } from '@language/Translate';
import Header from '@components/Header';
import TextInput from '@components/UI/TextInput';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import { isEmpty } from '@app/utils/lodashFactions';
// import Toast from 'react-native-simple-toast';
import SocketActions from '@redux/reducers/socket/actions';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import FeatureEmployerComponant from '@components/FeatureEmployerComponant';
import Button from '@components/UI/Button';
import LookingForComponant from '@components/LookingForComponant';
import AnimatedView from '@components/AnimatedView';
import { BaseStyles } from '@config/theme';
import { getBatchCount } from '@app/utils/CommonFunction';

const { emit, setTotalMsgCount } = SocketActions;

export default function Home({ navigation }: { navigation: any }) {
  const dispatch = useDispatch();
  const { userData } = useSelector((state: any) => state.auth);
  const { totalMsgCount } = useSelector((state: any) => state.socket); // Use your RootState type
  const [featureloader, setFeatureLoader] = useState(false);
  // console.log('list ==>', list);
  const [unReadCount, setUnReadCount] = useState<any>({});
  const [name, setName] = useState<string>('');
  const isFocused = useIsFocused();

  useEffect(() => {
    if (!isEmpty(totalMsgCount)) {
      setUnReadCount(totalMsgCount?.data);
    } else {
      dispatch(
        emit('unread_message_count', { userId: userData?.id }, (res: any) => {
          console.log('unread_message_count resData ===>', res);
          if (res) {
            dispatch(setTotalMsgCount(res) as any);
          }
        }) as any,
      );
    }
  }, [isFocused, !isEmpty(totalMsgCount)]);

  // Function to get list of seekers or employers

  // Handle user input
  const handleInputChange = (value: string) => {
    setName(value);
  };

  useFocusEffect(
    React.useCallback(() => {
      console.log('useFocuss effect called');

      // getJobList(1, false);
      getBatchCount();
      return () => { };
    }, []),
  );

  return (
    <SafeAreaView style={[styles.container]}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        leftIcon="logo"
        title=""
        rightIcons={[
          {
            icon: 'notification1',
            onPress: () => {
              navigation.navigate('Notification');
            },
            notiBadge: true,
            wrapStyle: BaseStyles.notificationIconStyle,
          },
          {
            icon: 'reward-outlined',
            onPress: () => {
              navigation.navigate('RewardScreen');
            },
            badge: totalMsgCount?.totalCount || false,
            // wrapStyle: styles.msgIconStyle,
          },
        ]}
      />

      <ScrollView
        // onScroll={handleScroll}
        // scrollEventThrottle={0.5}
        style={{
          ...styles.mainView,
          marginBottom: 25,
          // marginBottom: Dimensions.get('screen').height / 150,
        }}
        nestedScrollEnabled={true}
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}>
        <AnimatedView>
          {/* {userData?.isSkipped === true ? ( */}
          {/* ) : null} */}
          <View style={styles.searchViewSty}>
            <View style={styles.searchFieldSty}>
              <TextInput
                onChange={handleInputChange}
                value={name}
                placeholderText={translate('searchHarbor', '')}
                searchButton={true}
                placeholderStyle={{
                  width: '82%',
                  fontSize: Dimensions.get('screen').width * 0.038,
                }}
              />
            </View>
            <Button
              type="outlined"
              containerStyle={styles.searchStyle}
              onPress={() => {
                navigation.navigate('SearchScreen', { searchItem: name });
              }}>
              Search
            </Button>
          </View>
          <LookingForComponant />
          <View style={styles.centerMains}>
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={styles?.titleTxtSty}>Featured Employers</Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate('FeaturedList', {
                    searchType: 'Employer',
                  });
                }}>
                <Text style={styles?.seeAllTxtSty}>See all</Text>
              </TouchableOpacity>
            </View>

            <FeatureEmployerComponant
              navigation={navigation}
              featueType={'forEmployer'}
              setFeatureLoader={setFeatureLoader}
              featureloader={featureloader}
            />

            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={styles?.titleTxtSty}>Featured Seekers</Text>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => {
                  navigation.navigate('FeaturedList', {
                    searchType: 'Seeker',
                  });
                }}>
                <Text style={styles?.seeAllTxtSty}>See all</Text>
              </TouchableOpacity>
            </View>

            <FeatureEmployerComponant
              navigation={navigation}
              featueType={'forSeeker'}
              setFeatureLoader={setFeatureLoader}
              featureloader={featureloader}
            />

            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={styles?.titleTxtSty}>Upcoming Jobs</Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate('SearchScreen', {
                    searchType: 'Employer',
                  });
                }}>
                {/* <Text style={styles?.seeAllTxtSty}>See all</Text> */}
              </TouchableOpacity>
            </View>

            <FeatureEmployerComponant
              navigation={navigation}
              featueType={'upcomingJobs'}
              setFeatureLoader={setFeatureLoader}
              featureloader={featureloader}
            />
          </View>
        </AnimatedView>
      </ScrollView>
    </SafeAreaView>
  );
}
