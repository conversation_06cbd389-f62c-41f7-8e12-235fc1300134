/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import FastImage from 'react-native-fast-image';
import {CustomIcon} from '@config/LoadIcons';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import {isArray, isEmpty} from '@app/utils/lodashFactions';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {
  capitalizeFirstLetterOfEachWord,
  formatSalary,
  getBadgeImage,
  getDuration,
  imagePattern,
} from '@app/utils/CommonFunction';
import InitialsAvatar from '@components/InitialsAvatar';
import {Images} from '@config/images';
import {translate} from '@language/Translate';
import {useRedux} from '@components/UseRedux';
import {BaseColors} from '@config/theme';

dayjs.extend(customParseFormat); // Extend dayjs with the plugin

interface EmployarCardProps {
  item: any;
  onSave: any;
  saveLoader: any;
  isSavedHistory?: any;
  navigation: any;
  navigateName: any;
  bodyType?: any;
  isSeeker?: any;
  type?: any;
}

const EmployarCard: React.FC<EmployarCardProps> = ({
  item,
  onSave,
  isSavedHistory,
  saveLoader = false,
  navigation,
  navigateName,
  bodyType,
  isSeeker,
  type,
}) => {
  const [imageError, setImageError] = useState(false);
  const {useAppSelector} = useRedux();

  const isCustom = item?.type === 'custom';
  const {width} = Dimensions.get('window');
  const [showAllSkills, setShowAllSkills] = useState<boolean>(false);
  const {userData} = useAppSelector((state: any) => state.auth); // Use your RootState type
  const IOS = Platform.OS === 'ios';
  const USER_VERIFIED =
    item?.userData?.personaStatus === 'approved' ? true : false;

  const firstLetter = item?.title?.[0]?.toUpperCase() || '';
  const initials = firstLetter; // Use the first letter as the initials
  const jobConfirmView =
    item?.messageType === 'Job_confirmation' && item?.isChatView;

  const isEmployer = userData?.id === item?.userId;
  const isUploadedImage = !isEmpty(item?.images) && isArray(item?.images);
  const isCompanyView = item?.userData?.company || item?.company;

  const profilePhoto = isUploadedImage
    ? item?.images[0]
    : item?.userData?.profilePhoto;

  const isValidImage = (url: string | undefined) => {
    return typeof url === 'string' && imagePattern.test(url);
  };

  const shouldShowDefaultImage = imageError || !isValidImage(profilePhoto);

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={() => {
        const data = isCustom
          ? {
              applicant: {},
              type: 'Approved',
              job: item,
              jobID: item,
            }
          : {jobID: item, job: item};

        if (type === 'viewEmployer') {
          navigation.navigate(navigateName, {
            userId: item?.userId,
            type: 'viewEmployer',
            seekerProfile: 'seekerProfile',
          });
        } else {
          if (!isSavedHistory) {
            navigation.push(navigateName, data);
          }
        }
      }}
      style={[
        styles.cardBorder,
        {
          width:
            bodyType === 'home'
              ? Dimensions.get('screen').width * 0.95
              : '100%',
          marginHorizontal: bodyType === 'home' ? 7 : 0,
          borderWidth: isSavedHistory ? 0 : 1,
        },
      ]}>
      {!isSavedHistory ? (
        <View style={styles.innerPadding}>
          {jobConfirmView ? (
            <Text style={styles.confirmText}>
              {translate(isSeeker ? 'jobConfirmed' : 'paymentConfirmed')}
            </Text>
          ) : null}
          <View style={styles.rowDirection}>
            <View style={styles.imgView}>
              <FastImage
                source={
                  shouldShowDefaultImage
                    ? Images.user
                    : {uri: profilePhoto, priority: FastImage.priority.high}
                }
                style={{
                  width: '100%',
                  height: '100%',
                  borderRadius: width * 0.6,
                }}
                resizeMode={FastImage.resizeMode.cover}
                onError={() => setImageError(true)}
              />
              {USER_VERIFIED ? (
                <View style={styles?.imageViewSty}>
                  <FastImage
                    source={Images.verified}
                    style={{
                      width: '100%',
                      height: '100%',
                      borderRadius: width * 0.0125, // Assuming 5 is about 1.25% of the width
                    }}
                    resizeMode={FastImage.resizeMode.cover} // Optional: Set resize mode if needed
                    onError={() => setImageError(true)} // Set error state on image load failure
                  />
                </View>
              ) : null}
            </View>
            <View style={styles.rowStyle}>
              <View style={styles.txtViewSty}>
                <View style={styles.draftView}>
                  <View>
                    <Text numberOfLines={1} style={styles.titleTxtSty}>
                      {item?.title
                        ? capitalizeFirstLetterOfEachWord(item.title)
                        : ''}
                    </Text>
                  </View>
                  {item?.status == 'draft' ? (
                    <View style={styles.draftSty}>
                      <Text style={styles.txtSty}>Draft</Text>
                    </View>
                  ) : null}
                </View>
                <View style={styles?.locationViewSty}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingTop: 0,
                      paddingBottom: IOS ? 0 : 0,
                      width: '100%',
                    }}>
                    <CustomIcon
                      name="jobLocation"
                      size={16}
                      color={BaseColors.primary}
                    />
                    <Text numberOfLines={1} style={styles.desTxtSty}>
                      {isEmployer ? (
                        <>
                          {(String(item?.status).toLowerCase() === 'pending' ||
                            String(item?.status).toLowerCase() === 'applied') &&
                          item?.shortAddress !== null &&
                          item?.isApproved === 0
                            ? item?.shortAddress
                            : item?.location || item?.location}
                        </>
                      ) : (
                        <>
                          {(String(item?.status).toLowerCase() === 'pending' ||
                            String(item?.status).toLowerCase() === 'applied') &&
                          item?.shortAddress !== null &&
                          item?.isApproved === 0
                            ? item?.shortAddress
                            : item?.location || ''}
                        </>
                      )}
                    </Text>
                  </View>
                  {item?.type === 'custom' ? (
                    <View style={styles?.customViewSty}>
                      <Text style={styles.customTxtSty}>
                        {translate('Custom', '')}
                      </Text>
                    </View>
                  ) : null}
                </View>
              </View>

              <View style={{flexDirection: 'row'}}>
                <View style={styles.imageView}>
                  <FastImage
                    source={getBadgeImage(item?.badgeInfo?.currentBadge)}
                    style={styles.medalIcon}
                    resizeMode="contain"
                  />
                </View>
                <TouchableOpacity
                  style={styles.bookmarkContainer}
                  onPress={() => {
                    onSave(item);
                  }}>
                  {saveLoader === item?.id ? (
                    <ActivityIndicator color={BaseColors.primary} />
                  ) : (
                    <Ionicons
                      name={item?.isSaved ? 'bookmark' : 'bookmark-outline'}
                      size={25}
                      color={
                        item?.isSaved ? BaseColors.primary : BaseColors.black
                      }
                    />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View style={styles.underlineViewSty} />
          <View style={styles.bodyDataSty}>
            <View style={styles.companyViewSty}>
              <View style={styles.infoRow}>
                <CustomIcon
                  name="briefcase"
                  size={16}
                  color={BaseColors.logoColor}
                />
                <Text style={styles.jobdestxtSty}>
                  {isCompanyView
                    ? item?.userData?.company || item?.company || ''
                    : translate('companyNotAvail')}
                </Text>
              </View>
              {/* {item?.averageRate ? ( */}
              <View style={styles.reviewViewSty}>
                <CustomIcon name="fillStar" color={BaseColors.starColor} />
                <Text style={styles.ratingTxtSty}>
                  {item?.averageRate
                    ? Number(item?.averageRate).toFixed(1)
                    : '0' || '0'}
                </Text>

                {/* {item?.ratingCount ? ( */}
                <Text>
                  ({item?.ratingCount ? item?.ratingCount : '0' || '0'})
                </Text>
                {/* ) : null} */}
              </View>
              {/* ) : null} */}
            </View>
            <View style={styles.infoRow}>
              <CustomIcon
                name="Calander"
                size={16}
                color={BaseColors.logoColor}
              />
              {isEmpty(item?.startDateTime) && isEmpty(item?.endDateTime) ? (
                <Text style={styles.jobdestxtSty}>
                  {translate('datenotAvailable', '')}
                </Text>
              ) : (
                <Text style={styles.jobdestxtSty}>
                  {item?.startDateTime
                    ? dayjs(item?.startDateTime).format('M/D/YYYY') // Update moment to dayjs
                    : '-'}
                  {' - '}
                  {item?.endDateTime
                    ? dayjs(item?.endDateTime).format('M/D/YYYY') // Update moment to dayjs
                    : '-'}
                </Text>
              )}
            </View>
            <View>
              <View style={styles.infoRow}>
                <CustomIcon
                  name="clock"
                  size={16}
                  color={BaseColors.logoColor}
                />
                {isEmpty(item?.startTime) && isEmpty(item?.endTime) ? (
                  <Text style={styles.jobdestxtSty}>
                    {translate('timenotAvailable', '')}
                  </Text>
                ) : (
                  <Text style={styles.timeTxtView}>
                    {`${
                      item?.startDateTime
                        ? dayjs(item?.startDateTime)
                            .format('h:mm A')
                            .toUpperCase()
                        : '-'
                    } - ${
                      item?.endDateTime
                        ? dayjs(item?.endDateTime)
                            .format('h:mm A')
                            .toUpperCase()
                        : '-'
                    }`}
                  </Text>
                )}
              </View>
              {/* {jobConfirmView ? (
                <View>
                  <Text>Ref Id : {item?.id}</Text>
                </View>
              ) : null} */}
            </View>
          </View>
          <View style={styles.underlineViewSty} />

          <View style={styles.DataSty}>
            <View style={styles.bottomViewSty}>
              <View style={styles.skillsViewSty}>
                {isArray(item?.skills) && !isEmpty(item?.skills) ? (
                  <>
                    {(showAllSkills
                      ? item?.skills
                      : item?.skills.slice(0, 3)
                    ).map((skill: any, index: number) => (
                      <View
                        key={`${index}+1`}
                        style={[
                          styles.skillSty,
                          {marginRight: index === 0 ? 8 : 8},
                        ]}>
                        <Text style={styles.skillTxtSty}>{skill?.name}</Text>
                      </View>
                    ))}
                    {item?.skills.length > 3 && (
                      <TouchableOpacity
                        onPress={() => setShowAllSkills(!showAllSkills)}>
                        <Text style={styles.seeMoreTxtSty}>
                          {showAllSkills ? 'See Less' : 'See More'}
                        </Text>
                      </TouchableOpacity>
                    )}
                  </>
                ) : null}
              </View>
            </View>
            <View style={styles.saleryViewSty}>
              <Text style={styles.saleryTXtSty}>
                {item?.salaryAmount ? '$' : translate('offer')}
                {formatSalary(item?.salaryAmount) || ''}
                {item?.duration && item?.salaryAmount ? '/' : ''}
                {item?.salaryAmount ? getDuration(item?.duration) : ''}
              </Text>
            </View>
          </View>
        </View>
      ) : (
        <View style={styles.rowDirectionHistory}>
          <View style={styles.imgViewHistory}>
            <FastImage
              source={
                shouldShowDefaultImage
                  ? Images.user
                  : {uri: profilePhoto, priority: FastImage.priority.high}
              }
              style={{
                width: '100%',
                height: '100%',
                borderRadius: width * 0.6,
              }}
              resizeMode={FastImage.resizeMode.cover} // Optional: Set resize mode if needed
              onError={() => setImageError(true)} // Set error state on image load failure
            />

            {USER_VERIFIED ? (
              <View
                style={{
                  height: 20,
                  width: 20,
                  position: 'absolute',
                  bottom: -2,
                  right: -4,
                }}>
                <FastImage
                  source={Images.verified}
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: width * 0.0125, // Assuming 5 is about 1.25% of the width
                  }}
                  resizeMode={FastImage.resizeMode.cover} // Optional: Set resize mode if needed
                />
              </View>
            ) : null}
          </View>
          <View style={styles.rowStyle}>
            <View style={styles.txtViewSty}>
              <View style={styles.draftView}>
                <Text numberOfLines={1} style={styles.titleTxtHistorySty}>
                  {item?.title
                    ? capitalizeFirstLetterOfEachWord(item.title)
                    : ''}
                </Text>
              </View>
              {item?.description ? (
                <View style={styles.bodyDataHistorySty}>
                  <Text numberOfLines={2} style={[styles.decriptionTXtSty]}>
                    {item?.description}
                  </Text>
                </View>
              ) : null}
            </View>
          </View>
        </View>
      )}
    </TouchableOpacity>
  );
};

export default EmployarCard;
