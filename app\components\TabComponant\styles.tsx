import {StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '../../config/typography';

export default StyleSheet.create({
  container: {
    // marginTop: 20,
    alignItems: 'center',
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '80%',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  tabText: {
    fontSize: 18,
    color: '#7a7a7a', // Default text color
    fontFamily: FontFamily.OpenSansBold,
  },
  activeText: {
    color: BaseColors.primary, // Active text color
    fontFamily: FontFamily.OpenSansBold,
    fontSize: 18,
  },
  barContainer: {
    position: 'relative',
    width: '80%',
    marginTop: 4,
    height: 4,
    backgroundColor: 'transparent',
  },
  backgroundBar: {
    position: 'absolute',
    height: 8,
    width: '100%',
    backgroundColor: '#e3eaf5', // Light blue background bar color
    borderRadius: 4,
  },
  activeBar: {
    position: 'absolute',
    height: 8,
    width: '50%', // Half width for one tab
    backgroundColor: BaseColors.primary, // Blue active bar color
    borderRadius: 4,
    transitionDuration: '0.3s', // Smooth transition
  },
  badgeCount: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 18,
    height: 18,
    position: 'absolute',
    right: 17,
    top: 10,
    // zIndex: 999,
    backgroundColor: BaseColors.primary,
    borderRadius: 20,
  },
  badgeStyle: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 13,
    color: BaseColors.white,
    lineHeight: 15,
  },
});
