import React, {useState} from 'react';
import {Text, TouchableOpacity, View} from 'react-native';
import styles from './styles';
import {translate} from '@language/Translate';
import {BaseColors} from '@config/theme';
import {CustomIcon} from '@config/LoadIcons';

type StatusOption = 'Saved' | 'Pending' | 'Completed' | 'Upcoming';

interface StatusFilterProps {
  onStatusChange?: (status: StatusOption) => void; // Callback when a status is selected
  selectedTab: String;
  isFiltered?: boolean;
  navigation?: any;
  selectedStatus?: any;
  setSelectedStatus?: any;
}

const StatusFilter: React.FC<StatusFilterProps> = ({
  onStatusChange,
  selectedTab = 'Employer',
  isFiltered = false,
  navigation = {},
  selectedStatus,
  setSelectedStatus,
}) => {

  const handleStatusChange = (status: StatusOption) => {
    setSelectedStatus(status);
    if (onStatusChange) {
      onStatusChange(status);
    }
  };

  const isSavedFilter = selectedStatus === 'Saved';

  return (
    <View style={styles.mainCContain}>
      {/* <TouchableOpacity
        style={{
          ...styles.iconView,
        }}
        activeOpacity={0.5}
        onPress={() => {
          handleStatusChange('Saved');
        }}>
        <CustomIcon
          name="bookmark"
          size={25}
          color={isSavedFilter ? BaseColors.primary : BaseColors.inputColor}
          style={{textAlign: 'center'}}
        />
      </TouchableOpacity> */}
      <View style={[styles.container]}>
        {(selectedTab === 'Employer'
          ? [
              // translate('Saved', ''),
              translate('Pending', ''),
              translate('Upcoming', ''),
              translate('Completed', ''),
            ]
          : [
              // translate('Saved', ''),
              translate('Pending', ''),
              translate('Upcoming', ''),
              translate('Completed', ''),
            ]
        ).map(status => (
          <TouchableOpacity
            key={status}
            style={[
              styles.tab,
              selectedStatus === status && status === translate('Upcoming', '')
                ? styles.savedActiveSty
                : status === translate('Upcoming', '')
                ? styles.savedTab
                : selectedStatus === status &&
                  status === translate('Completed', '')
                ? styles.completedActiveView
                : status === translate('Completed', '')
                ? styles.completedTab
                : selectedStatus === status &&
                  status === translate('Pending', '')
                ? styles.pendingActiveSTy
                : status === translate('Pending', '')
                ? styles.pendingViewSty
                : null,
            ]}
            onPress={() => handleStatusChange(status as StatusOption)}>
            <Text
              style={[
                styles.tabText,
                selectedStatus === translate('Pending', '') &&
                status === translate('Pending', '')
                  ? styles?.pendingTxtSTy
                  : status === translate('Pending', '')
                  ? styles.activePendingTXtsty
                  : null,
              ]}>
              {status}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      {/* <TouchableOpacity
        style={{
          ...styles.iconView,
          borderColor: isFiltered ? BaseColors.primary : BaseColors.inputColor,
        }}
        activeOpacity={0.5}
        onPress={() => {
          navigation.navigate('FilterScreen', {
            type: 'myHarbor',
            selectedTab: selectedTab,
          });
        }}>
        <CustomIcon
          name="setting"
          size={25}
          color={isFiltered ? BaseColors.primary : BaseColors.inputColor}
          style={{textAlign: 'center'}}
        />
      </TouchableOpacity> */}
    </View>
  );
};

export default StatusFilter;
