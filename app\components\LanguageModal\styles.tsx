import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {Dimensions, Platform, StyleSheet} from 'react-native';

const {width, height} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

const dimensions = {
  imgHeight: height / 2.3,
  imgWidth: width / 1.35,
  dotWidth: width * 0.09,
  dotHeight: height * 0.005,
  titleFontSize: 30,
  bodyFontSize: 16,
};

export default StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  skipButton: {
    marginTop: Platform.OS === 'ios' ? 20 : 30,
    marginRight: 16,
    alignSelf: 'flex-end',
    backgroundColor: BaseColors.activeColor,
    padding: 7,
    borderRadius: 6,
  },
  swiperContainer: {
    backgroundColor: BaseColors.white,
  },
  container: {
    flex: 1,
  },
  imgContainer: {
    justifyContent: 'center',
    alignSelf: 'center',
  },
  imgView: {
    height: dimensions.imgHeight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  introImage: {
    width: dimensions.imgWidth,
    height: dimensions.imgHeight,
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    position: 'absolute',
    top: height / 1.9,
    alignSelf: 'center',
  },
  dot: {
    width: dimensions.dotWidth,
    height: dimensions.dotHeight,
    borderRadius: 7,
    marginHorizontal: 3,
  },
  textContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
    alignSelf: 'center',
    position: 'absolute',
    top: height / 1.8,
  },
  titleText: {
    color: BaseColors.textColor,
    fontSize: dimensions.titleFontSize,
    fontWeight: '600',
    textAlign: 'center',
  },
  bodyText: {
    color: BaseColors.discriptionTextColor,
    fontSize: dimensions.bodyFontSize,
    fontWeight: '400',
    textAlign: 'center',
    paddingTop: 20,
  },
  buttonContainer: {
    marginBottom: Dimensions.get('screen').height / 40,
    marginTop: 20,
    marginHorizontal: 30,
  },
  nextButton: {
    backgroundColor: BaseColors.primary,
  },
  rbSheetCustomStyles: {
    wrapper: {backgroundColor: 'hsla(360, 20%,2%, 0.6)'},
    draggableIcon: {backgroundColor: '#000'},
    container: {
      backgroundColor: 'white',
      borderTopRightRadius: 20,
      borderTopLeftRadius: 20,
      height: IOS ? height / 2.7 : height / 2.5,
    },
  },
  rbSheetCustomDltStyles: {
    wrapper: {backgroundColor: 'hsla(360, 20%,2%, 0.6)'},
    draggableIcon: {backgroundColor: '#000'},
    container: {
      backgroundColor: 'white',
      borderTopRightRadius: 20,
      borderTopLeftRadius: 20,
      height:  height / 3.5,
    },
  },
  rbSheetCustomDltModalStyles: {
    wrapper: {backgroundColor: 'hsla(360, 20%,2%, 0.6)'},
    draggableIcon: {backgroundColor: '#000'},
    container: {
      backgroundColor: 'white',
      borderTopRightRadius: 20,
      borderTopLeftRadius: 20,
      height:  height / 3,
    },
  },
  languageContainer: {
    marginTop: Dimensions.get('screen').height / 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 28,
    marginHorizontal: 20,
  },
  languageTitle: {
    fontSize: 30,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
  },
  languageOption: {
    height: height / 16,
    // marginBottom: 15,
    borderWidth: 1.7,
    justifyContent: 'center',
    // alignItems: 'center',
    width: width / 1.2,
    borderRadius: 6,
    marginTop: 20,
  },
  languageText: {
    fontSize: 17,
    color: BaseColors.textColor,
    // paddingHorizontal: 20,
    fontFamily: FontFamily.OpenSansRegular,
  },
  iconPadding: {
    position: 'absolute',
    right: 15,
  },
  languageButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: Dimensions.get('screen').height / 30,
    width: '100%',
    paddingHorizontal: 15,
  },
  languageModalView: {
    flexDirection: 'row',
    width: '40%',
    alignItems: 'center',
  },
  imageView: {
    width: 70,
    height: 27,
  },
  descriptionView: {
    marginTop: 20,
  },
  descriptionTxtSty: {
    fontSize: 18,
    color: BaseColors.inputColor,
    FontFamily: FontFamily.OpenSansRegular,
  },
});
