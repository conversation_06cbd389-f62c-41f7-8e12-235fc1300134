import { getBadgeImage } from '@app/utils/CommonFunction';
import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import React, { useEffect, useState } from 'react';
import {
  Animated,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import Entypo from 'react-native-vector-icons/Entypo';
import FastImage from 'react-native-fast-image';
import { useSelector } from 'react-redux';

const
  RewardNotification = ({
    visible,
    onClose,
    currentXP,
    xpGained,
    currentLevel,
    xpRequiredForNextLevel,
    badge,
  }) => {
  // Animation values
    const { userData } = useSelector((state: any) => state.auth); // Use your RootState type
    console.log('🚀 ~ userData:', userData);

    const [animation] = useState(new Animated.Value(0));
    const [progressAnimation] = useState(new Animated.Value(0));
    const isMaxReached = badge?.currentBadge === 'Diamond';


    // Calculate previous and new progress percentages
    const previousXP = currentXP - xpGained;
    const previousProgressPercentage =
    (previousXP / xpRequiredForNextLevel) * 100;
    const newProgressPercentage = (currentXP / xpRequiredForNextLevel) * 100;

    useEffect(() => {
      if (visible) {
      // Reset animations when modal becomes visible
        animation.setValue(0);
        progressAnimation.setValue(previousProgressPercentage);

        // Start animations
        Animated.sequence([
        // First fade in the modal
          Animated.timing(animation, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          // Then animate the progress bar
          Animated.timing(progressAnimation, {
            toValue: newProgressPercentage,
            duration: 1500,
            useNativeDriver: false,
          }),
        ]).start();
      }
    }, [visible, previousProgressPercentage, newProgressPercentage]);

    const progressWidth = progressAnimation.interpolate({
      inputRange: [0, 100],
      outputRange: ['0%', '100%'],
      extrapolate: 'clamp',
    });

    const badgeInfo = userData?.badgeInfo;

    return (
      <Animated.View
        style={[
          styles.modalContent,
          {
            opacity: animation,
            transform: [
              {
                scale: animation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                }),
              },
            ],
          },
        ]}>
        {/* <View style={styles?.centerView}> */}
        <Text style={styles?.progressText}>
          {'+'}
          {xpGained} XP
        </Text>
        {/* </View> */}
        <View style={styles.progressContainer}>
          <View style={styles.progressLabels}>
            <Text style={styles.progressText}>
              {badge?.totalPoints || '0'} pts
            </Text>
            {isMaxReached ? <Entypo name="infinity" /> : <Text style={styles.progressText}>
              {badge?.endingPoints || '299'} pts
            </Text>}
          </View>
          <View style={styles.progressBackground}>
            <Animated.View
              style={[styles.progressFill, { width: progressWidth }]}
            />
          </View>
          <View style={styles.levelLabels}>
            <View style={styles.levelLabel}>
              <View style={styles.imageView}>
                <FastImage
                  source={getBadgeImage(badge?.currentBadge)}
                  style={styles.medalIcon}
                  resizeMode="contain"
                />
              </View>
              <Text style={styles.levelText}>{badge?.currentBadge}</Text>
            </View>
            <View style={styles.levelLabel}>
              <Text style={styles.levelText}>{badge?.upcomingBadge}</Text>
              <View style={styles.imageView}>
                <FastImage
                  source={getBadgeImage(badge?.upcomingBadge)}
                  style={styles.medalIcon}
                  resizeMode="contain"
                />
              </View>
            </View>
          </View>
        </View>
      </Animated.View>
    );
  };

const styles = StyleSheet.create({
  modalContent: {
    width: '100%',
    // backgroundColor: '#192339',
    borderRadius: 16,
    overflow: 'hidden',
    padding: 16,
    // elevation: 5,
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.3,
    // shadowRadius: 4,
  },
  header: {
    // alignItems: 'center',
    // marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFD700', // Gold color
  },
  rewardSection: {
    alignItems: 'center',
    marginBottom: 20,
  },
  xpGained: {
    fontSize: 13,
    fontWeight: 'bold',
    color: BaseColors.textColor, // Green color
    // marginBottom: 8,
  },
  levelText: {
    fontSize: 14,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
    paddingHorizontal: 5,
  },
  progressContainer: {
    // marginBottom: 24,
  },
  progressBackground: {
    height: 10,
    backgroundColor: BaseColors.blueLight,
    borderRadius: 10,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: BaseColors.primary, // Green color
  },
  progressLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  xpText: {
    color: '#BBBBBB',
    fontSize: 14,
  },
  nextLevelText: {
    color: '#FFD700', // Gold color
    fontSize: 14,
    fontWeight: '500',
  },
  closeButton: {
    backgroundColor: '#3F51B5', // Purple-blue color
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  progressText: {
    fontSize: 14,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
    textAlign: 'center',
  },
  levelLabel: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  medalIcon: {
    width: '100%',
    height: '100%',
  },
  centerView: {
    flex: 1,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  levelLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  imageView: { width: 35, height: 35 },
});

export default RewardNotification;
