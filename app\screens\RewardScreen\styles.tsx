import {StyleSheet, Dimensions} from 'react-native';
import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';

const HEIGHT = Dimensions.get('screen').height;

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  centerMain: {
    flex: 1,
    marginTop: HEIGHT / 3,
  },
  mainView: {
    marginHorizontal: 10,
    marginBottom: Dimensions.get('screen').height / 9,
  },
  imageView: {width: 35, height: 35},
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    paddingHorizontal: 10,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: BaseColors.primary,
  },
  profileDetails: {
    marginLeft: 16,
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileName: {
    fontSize: 14,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansBold,
  },
  profileSubtext: {
    fontSize: 14,
    color: '#666',
    fontFamily: FontFamily.OpenSansRegular,
  },
  progressContainer: {
    marginTop: 0,
  },
  progressLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
    marginBottom: 5,
  },
  progressText: {
    fontSize: 14,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
  },
  levelLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  levelLabel: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  medalIcon: {
    width: '100%',
    height: '100%',
  },
  levelText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  mgBt: {marginBottom: 15},
  mgTp: {marginTop: 10},
  statsContainer: {
    flexDirection: 'row',
    width: '100%',

    marginBottom: 16,

    borderColor: '#e0e0e0',
    paddingVertical: 8,
    marginTop: 15,
  },
  statsText: {
    fontSize: 16,
    color: BaseColors?.textColor,
    fontFamily: FontFamily.OpenSansBold,
  },
  rewardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: BaseColors.borderColor,
    borderRadius: 7,
  },
  rewardItem: {
    alignItems: 'center',
    width: '30%',
    marginBottom: 15,
  },
  rewardIcon: {
    width: 40,
    height: 40,
    marginBottom: 8,
  },
  rewardText: {
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
  },
  rewardCount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  seeAllText: {
    textAlign: 'right',
    color: '#1d559f',
    fontSize: 14,
    fontWeight: 'bold',
  },
  amountStyle: {
    backgroundColor: BaseColors.inputBackground,
    padding: 8,
    borderRadius: 10,
    width: '100%',
  },
  buttonStyles: {borderWidth: 1, paddingVertical: 0},
  textSty: {
    fontSize: 16,
    color: BaseColors?.textColor,
    fontFamily: FontFamily.OpenSansBold,
  },
  rewardView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  rewardViewSty: {
    borderRadius: 25,
    width: 50,
    height: 50,
    backgroundColor: BaseColors.primary,
    marginTop: 10,
    marginBottom: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleSty: {
    fontSize: 18,
    fontFamily: FontFamily.OpenSansBold,
  },
  earnPoints: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  incompleteCategoriesContainer: {
    marginTop: 10,
    gap: 10,
  },
  buttonText: {
    fontSize: 10,
    color: BaseColors.primary,
    backgroundColor: BaseColors.white,
  },
  incompleteCategoryText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
    width: Dimensions.get('screen').width / 1.6,
    color: BaseColors?.textGrey,
  },
  rewardView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  btnView: {
    borderWidth: 0.7,
    borderColor: BaseColors?.primary,
    paddingVertical: 5,
    width: Dimensions.get('screen').width / 5.2,
    alignItems: 'center',
    borderRadius: 5,
  },
  categoryPointSty: {
    fontSize: 13,
    fontFamily: FontFamily?.OpenSansSemiBold,
    color: BaseColors.primary,
  },
  crossView: {
    alignItems: 'center',
    marginBottom: 30,
  },
  crossIcon: {
    padding: 5,
    borderWidth: 1,
    borderColor: BaseColors.primary,
    borderRadius: 20,
  },
  floatingContainer: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    // flexDirection: 'column',
    alignItems: 'center',
  },

  streakTrackerWrapper: {
    flex: 1,
  },
});
