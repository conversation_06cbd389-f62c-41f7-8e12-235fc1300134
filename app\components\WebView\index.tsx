import React from 'react';
import WebView from 'react-native-webview';

interface WebViewCmpProps {
  url: any;
  style: any;
  type: any;
}

const WebViewCmp: React.FC<WebViewCmpProps> = ({url, style, type}) => {
  const googleDocsViewerUrl = `https://docs.google.com/gview?url=${encodeURIComponent(
    url,
  )}&embedded=true`;
  return (
    <WebView
      style={{...style, flex: 1}}
      bounces={false}
      originWhitelist={['*']}
      source={{
        uri:
          type === 'profile'
            ? googleDocsViewerUrl
            : type === 'jobView'
            ? googleDocsViewerUrl
            : url,
      }}
      startInLoadingState
      scrollEnabled
      scalesPageToFit={true}
      showsVerticalScrollIndicator={false}
    />
  );
};

export default WebViewCmp;
