import React from 'react';
import {View, BackHandler, ActivityIndicator} from 'react-native';
import BaseSetting from '@config/setting';
import {useState} from 'react';
import {useEffect} from 'react';
import {BaseColors} from '@config/theme';
import WebView from 'react-native-webview';
import {isEmpty} from '@app/utils/lodashFactions';
// import Toast from 'react-native-simple-toast';
import Header from '@components/Header';
import {getApiData} from '@app/utils/apiHelper';
import styles from './styles';
/**
 * terms screen
 * @module  terms
 *
 */

export default function Policy({navigation, route}: any) {
  const [terms, setTerms] = useState('');
  const type = route?.params?.type;
  const title = route?.params?.title;
  const [loader, setLoader] = useState(false);
  const defaultStyle = `
  <style>
  html, body {overflow-x: 'hidden'; word-break: 'break-all';color: ${BaseColors.InputBorder};background-color:${BaseColors.white}; padding:5px;  white-space: 'no-wrap'}img {display: inline; height: auto; max-width: 100%;}
  </style>`;

  const defaultHead = `<meta name="viewport" content="width=device-width, initial-scale=1">${defaultStyle}`;

  const termCondition = async () => {
    setLoader(true);
    const url = `${BaseSetting.endpoints.cmsCondition}?slug=${type}`; // Append type to slug

    try {
      const resp = await getApiData({endpoint: url, method: 'GET'});
      if (resp?.status) {
        let updatedHtmlBody = resp?.data?.htmlBody;
        // Remove the margin: 20px; from the body
        updatedHtmlBody = updatedHtmlBody.replace(/margin:\s*20px;/g, '');
        setTerms(updatedHtmlBody); // Set the modified htmlBody
      } else {
        setTerms('');
        // Toast.show(resp?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setTerms('');
      // Toast.show('somethingWentWrong');
    }
  };

  useEffect(() => {
    termCondition();
  }, []);

  //this function for back button
  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }
  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <>
      <View style={{flex: 1, backgroundColor: BaseColors.white}}>
        <Header
          title={title}
          leftIcon="back-arrow"
          onLeftPress={() => navigation.goBack()}
        />
        <View style={styles.container}>
          {loader ? (
            <ActivityIndicator
              style={{flex: 1}}
              color={BaseColors.primary}
              size={'large'}
            />
          ) : (
            <WebView
              style={{flex: 1}}
              bounces={false}
              originWhitelist={['*']}
              source={{
                html: !isEmpty(terms) ? `${defaultHead}${terms}` : '',
              }}
              startInLoadingState
              scrollEnabled
              scalesPageToFit={false}
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>
      </View>
    </>
  );
}
