# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

  def node_require(script)
    # Resolve script with node to allow for hoisting
    require Pod::Executable.execute_command('node', ['-p',
      "require.resolve(
        '#{script}',
        {paths: [process.argv[1]]},
      )", __dir__]).strip
  end

  node_require('react-native-permissions/scripts/setup.rb')


# pod 'Firebase', :modular_headers => true
# pod 'FirebaseCore', :modular_headers => true
# pod 'GoogleUtilities', :modular_headers => true
# pod 'FirebaseCoreInternal', :modular_headers => true
# pod 'GTMSessionFetcher', :modular_headers => true
# pod 'AppAuth', :modular_headers => true
# pod 'Google-Maps-iOS-Utils', '4.2.2'

platform :ios, '13.4'
prepare_react_native_project!
# use_modular_headers!

setup_permissions([
  'Camera',
  'LocationAccuracy',
  'LocationAlways',
  'LocationWhenInUse',
  # 'Notifications',
  'PhotoLibrary',
])


linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'Harbor' do
  config = use_native_modules!

  use_frameworks! :linkage => :static
  $RNFirebaseAsStaticFramework = true
  pod 'Firebase/Messaging'
  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )
  pod 'RNFS', :path => '../node_modules/react-native-fs'
  pod 'react-native-safe-area-context', :path => '../node_modules/react-native-safe-area-context'
  target 'HarborTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      :ccache_enabled => false
    )
  end
end
