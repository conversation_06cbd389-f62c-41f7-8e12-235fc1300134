import {Platform, StyleSheet} from 'react-native';
import {store} from '../../redux/store/configureStore';
import {FontFamily} from '@config/typography';
const {
  auth: {isTablet},
} = store.getState();
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  mainView: {
    flex: 1,
    justifyContent: 'center',
    alignContent: 'center',
    backgroundColor: 'hsla(360, 20%,2%, 0.4)',
  },
  modalView: {
    marginHorizontal: IOS ? 20 : 30,
    borderRadius: 20,
    padding: 20,
    // alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    borderWidth: 0.8,
  },
  blurBackground: {backgroundColor: 'rgba(0, 0, 0, 0.7)'},
  btnView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  DesView: {paddingVertical: 20},
  textStyle: {fontWeight: 'bold', textAlign: 'center', fontSize: 13},
  modalTitle: {
    marginBottom: 25,
    fontFamily: FontFamily.OpenSansBold,
    fontSize: 25,
    fontWeight: '700',
  },
  imageTitle: {
    marginTop: 15,
    marginBottom: 0,
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 21,
  },
  modallongTitle: {
    marginTop: 10,
    fontFamily: FontFamily.OpenSansBold,
    fontSize: 15,
    fontWeight: '700',
  },
  modalText: {
    marginBottom: 20,
    fontFamily: FontFamily.OpenSansRegular,
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 25,
  },
  deleteAcText: {
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 12,
  },
  closeIcon: {
    marginRight: 15,
    fontSize: 15,
    opacity: 0.7,
    left: 10,
  },
  closeIconView: {position: 'absolute', right: 7, top: 7, padding: 10},
  imgViewStyle: {alignItems: 'center', marginBottom: 10},
  imgStyle: {height: 70, width: 70, borderRadius: 10},
  lottieViewStyle: {
    aspectRatio: 1,
    height: 100,
  },
  linkDescriptionStyle: {
    fontSize: 14,
    textAlign: 'center',
  },
  linkViewStyle: {flexDirection: 'row', marginTop: 10, marginBottom: 17},
  linkStyle: {
    marginRight: 5,
    fontSize: 14,
  },
  rowViewStyle: {flexDirection: 'row'},
  linkConnectorStyle: {
    marginRight: 5,
    fontSize: 14,
  },
  secondLinkStyle: {
    fontSize: 14,
  },
  subtitleTxt: {fontFamily: FontFamily.OpenSansBold, fontWeight: '700'},
  blurView: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
});
