/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  Text,
  View,
} from 'react-native';
import { useRedux } from '@components/UseRedux';
import styles from './styles';

import Header from '@components/Header';

import { BaseColors } from '@config/theme';
import EmployarCard from '@components/EmployerCard';

import SeekerCard from '@components/SeekerCard';
import Toast from 'react-native-simple-toast';
import { translate } from '@language/Translate';
import PaymentBreakdown from '@components/PaymentBreakdown';
import Button from '@components/UI/Button';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import { isEmpty } from '@app/utils/lodashFactions';
import socketAction from '@redux/reducers/socket/actions';
import AlertModal from '@components/AlertModal';
import FastImage from 'react-native-fast-image';
import { useStripe } from '@stripe/stripe-react-native';

const { emit } = socketAction;

export default function JobDetailScreen({
  navigation,
  route,
}: {
  navigation: any;
  route: any;
}) {
  const { params } = route;
  const { job, applicant, type } = params;
  const { useAppSelector, dispatch } = useRedux();
  console.log('job ==>', job, applicant);
  const [refreshing, setRefreshing] = useState(false);
  const [loader, setLoader] = useState(false);
  const [saveLoader, setSaveLoader] = useState('');
  const [jobDetails, setJobDetail] = useState(job);
  const [applicantDetail, setApplicant] = useState({});
  const { userData } = useAppSelector((state: any) => state.auth); // Use your RootState type
  const appDetails = isEmpty(applicant) ? applicantDetail : applicant;

  const isEmployee = userData?.id === jobDetails?.userId;
  const { initPaymentSheet, presentPaymentSheet } = useStripe();

  const fetchPaymentSheetParams = async (amount = 20, id = null) => {
    console.log('amount ===>', amount);
    let data: {
      amount?: number;
      jobId?: number;
      currency?: string;
      seekerId: number;
      timezone: any;
    } = {};
    if (Number(amount) > 0) {
      data.amount = amount;
      data.jobId = jobDetails?.id;
      data.currency = 'USD';
      data.seekerId = jobDetails?.applicantId || appDetails?.id || id;
      data.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    }

    console.log('🚀 ~ fetchPaymentSheetParams ~ data:', data);
    const resp = await getApiData({
      endpoint: BaseSetting.endpoints.getStripIntentId,
      method: 'POST',
      data: data,
    });
    console.log('🚀 ~ fetchPaymentSheetParams ~ resp:', resp);
    if (!resp?.status) {
      // Toast.show(resp?.message || translate('error'), Toast.LONG);
    }

    const { paymentIntent, ephemeralKey, customer } = resp?.data || {};

    return {
      paymentIntent,
      ephemeralKey,
      customer,
    };
  };

  const initializePaymentSheet = async (id?: any) => {
    console.log('paymentIntent ===>');
    const { paymentIntent, ephemeralKey, customer } =
      await fetchPaymentSheetParams(jobDetails?.totalEstimateCharge, id);

    const { error } = await initPaymentSheet({
      merchantDisplayName: 'The Harbor App',
      customerId: customer,
      customerEphemeralKeySecret: ephemeralKey,
      paymentIntentClientSecret: paymentIntent,
      // Set `allowsDelayedPaymentMethods` to true if your business can handle payment
      //methods that complete payment after a delay, like SEPA Debit and Sofort.
      allowsDelayedPaymentMethods: true,
      // defaultBillingDetails: {
      //   name: 'Jane Doe',
      // },
      googlePay: {
        merchantCountryCode: 'US',
        testEnv: false, // use test environment
      },
      applePay: {
        merchantCountryCode: 'US',
      },
      returnURL: 'com.harbor.app://stripe-redirect',
    });
    if (!error) {
      setLoading(true);
    }
  };

  const jobApplicant = async (id: any) => {
    setLoader(true);
    const newObj = {
      jobId: id,
      page: 1,
      limit: 10,
    };
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.listApplicants,
        method: 'GET',
        data: newObj,
      });
      console.log('listApplicants ===', res?.data?.items);
      if (res?.status === true) {
        setApplicant(res?.data?.items[0] || {});
        initializePaymentSheet(res?.data?.items[0]?.id);
      } else {
        setApplicant({}); // Ensure the list is empty
      }
      setLoader(false);
    } catch (err) {
      setApplicant([]); // Clear the list to prevent crashes
    }
  };

  const getJobDetails = useCallback(async () => {
    setLoader(true);
    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.jobDetail + `/${job?.id}`,
        method: 'GET',
      });

      if (resp?.data && resp?.status) {
        setJobDetail(resp?.data); // Update list with new data
        jobApplicant(resp?.data?.id);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      // Toast.show('Failed to fetch data.', Toast.SHORT);
    }
  }, []);

  useEffect(() => {
    if (isEmpty(applicant)) {
      getJobDetails();
    }
  }, []);

  const [loading, setLoading] = useState(false);
  const [state, setState] = useState({
    loader: false,
    confirmationModal: false,
  });
  const [payementapprove, setPaymentApprove] = useState<boolean>(false);

  const updateJobStatus = useCallback(
    async (type: string) => {
      const data = {
        jobId: jobDetails?.id,
        status: type,
        userId: appDetails?.id,
      };
      console.log('Read 111=====>');
      setState((p: any) => ({ ...p, loader: true }));
      try {
        const resp = {
          status: true,
          message:
            type === 'Approved'
              ? translate('successApplied', '')
              : translate('declinedApplied', ''),
        };
        console.log('resp', resp);

        if (resp?.status) {
          setState((p: any) => ({ ...p, confirmationModal: false }));
          // Toast.show(
          //   resp?.message ||
          //   translate(
          //     type === 'Approved' ? 'successApplied' : 'declinedApplied',
          //     '',
          //   ),
          //   Toast.SHORT,
          // );
          if (type === 'Approved') {
            console.log('abss');
            dispatch(
              emit(
                'create_room',
                {
                  userId: jobDetails?.userId || '',
                  applicantId: appDetails?.id,
                  jobId: jobDetails?.id || '',
                },
                (res: any) => {
                  console.log('Read=====>', res);
                  const op = {
                    userId: userData?.id || '',
                    jobId: jobDetails?.id || '',
                    roomId: res?.roomId,
                  };
                  dispatch(
                    emit('get_single_chat', op, (singleRes: any) => {
                      console.log('Read=====>', singleRes);
                      navigation.replace('ChatDetails', {
                        userInfo: {
                          ...res,
                          ...singleRes?.data,
                        },
                      });
                    }) as any,
                  );
                },
              ) as any,
            );
            setPaymentApprove(true);
          }
        } else {
          setState((p: any) => ({ ...p, loader: false }));
          Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
          setPaymentApprove(false);
        }

        setState((p: any) => ({ ...p, loader: false }));
      } catch (e) {
        setState((p: any) => ({ ...p, loader: false }));
        console.log('ERRR', e);
        setPaymentApprove(false);
      }
    },
    [appDetails, jobDetails],
  );

  const openPaymentSheet = async () => {
    setState(p => ({ ...p, loader: true }));
    console.log('🚀 ~ openPaymentSheet ~ error:');
    const { error } = await presentPaymentSheet();
    console.log('🚀 ~ openPaymentSheet ~ error:', error);

    if (error) {
      Alert.alert(`Error code: ${error.code}`, error.message);
      setState(p => ({ ...p, loader: false }));
    } else {
      updateJobStatus('Approved');
      // Alert.alert('Success', 'Your order is confirmed!');
    }
  };

  useEffect(() => {
    // if (!isEmpty(appDetails)) {
    initializePaymentSheet();
    // }
  }, []);

  const onRefresh = React.useCallback(async () => {
    if (!loader) {
      setRefreshing(true);
      await getJobDetails();
      setRefreshing(false);
    }
  }, [loader]);

  const handleSave = async () => {
    setSaveLoader(job?.id);
    try {
      let data: any = { userId: job?.id };

      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.seekerSave,
        method: 'POST',
        data: data,
      });
      if (resp?.status) {
        setJobDetail({
          ...job,
          savedUser: resp?.data?.savedUser || null,
        });
        // setJobDetail()//TODO:update bookmark
        setSaveLoader('');
      } else {
        setSaveLoader('');
        // Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
      setSaveLoader('');
    } catch (e) {
      setSaveLoader('');
      console.log('ERRR', e);
    }
  };

  return (
    <SafeAreaView style={[styles.container]}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        leftIcon="back-arrow"
        title={jobDetails?.title || 'Job Title'}
        onLeftPress={() => {
          if (payementapprove) {
            navigation.replace('BottomTabsNavigator');
          } else {
            navigation.goBack();
          }
        }}
      />
      <ScrollView
        nestedScrollEnabled={true}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[BaseColors.primary]} // Customize refresh indicator color
            tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
          />
        }>
        {loader ? (
          <View
            style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <ActivityIndicator size={'small'} color={BaseColors.primary} />
          </View>
        ) : (
          <View style={{ marginHorizontal: 15 }}>
            <EmployarCard
              item={job || {}}
              onSave={() => {
                handleSave();
              }}
              saveLoader={saveLoader}
              navigation={navigation}
              navigateName={''}
            />
            {!isEmpty(appDetails) && (
              <View>
                <SeekerCard
                  item={appDetails || {}}
                  onSave={() => { }}
                  saveLoader={null}
                  applicantsLoader={null}
                  navigation={navigation}
                  jobtype="jobDetail"
                />
              </View>
            )}
            <View>
              <Text style={styles.sectionTitle}>{translate('Images', '')}</Text>
              <View style={styles.imagesRow}>
                {!isEmpty(jobDetails?.images) &&
                  jobDetails?.images?.map((image: any, index: number) => {
                    return (
                      <View style={styles.imageWrapper} key={index}>
                        <FastImage
                          source={{ uri: image }}
                          style={styles.uploadedImage}
                          resizeMode="cover"
                        />
                      </View>
                    );
                  })}
              </View>
            </View>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>
                {translate('Description', '')}
              </Text>
              <Text style={styles.descriptionText}>
                {jobDetails?.description}
              </Text>
            </View>
            <PaymentBreakdown
              totalSalary={jobDetails?.totalSalaryAmount || '-'}
              serviceChargePer={
                isEmployee
                  ? jobDetails?.employerServiceChargePer
                  : jobDetails?.seekerServiceChargePer
              }
              serviceCharge={
                isEmployee
                  ? `${jobDetails?.serviceCharge || 0}`
                  : `${jobDetails?.seekerServiceCharge}` || 0
              }
              totalWithServiceCharge={
                isEmployee
                  ? jobDetails?.totalEstimateCharge || 0
                  : jobDetails?.seekerPayableAmount || 0
              }
            />
          </View>
        )}
      </ScrollView>
      {String(appDetails?.userJob?.status).toLowerCase() === 'applied' ? (
        <View style={{ marginHorizontal: 15, marginTop: 20 }}>
          <Button
            onPress={() => {
              setState(p => ({ ...p, confirmationModal: true }));
            }}
            type="text">
            {translate('yesApprove')}
          </Button>
        </View>
      ) : (
        <Button
          style={{ marginHorizontal: 15, marginTop: 20 }}
          disable={true}
          type="outlined">
          {String(jobDetails?.status).toUpperCase()}
        </Button>
      )}

      {state.confirmationModal && (
        <AlertModal
          image
          title={translate('areyouApproveJob', '')}
          visible={state.confirmationModal}
          setVisible={(val: any) =>
            setState((p: any) => ({ ...p, confirmationModal: false }))
          }
          btnYPress={() => openPaymentSheet()}
          loader={state?.loader}
          btnYTitle={translate('YES')}
          btnNTitle={translate('CANCEL')}
          btnNPress={() => {
            setState((p: any) => ({ ...p, confirmationModal: false }));
          }}
          confirmation
        />
      )}
    </SafeAreaView>
  );
}
