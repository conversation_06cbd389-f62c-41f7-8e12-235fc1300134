import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {Dimensions, Platform, StyleSheet} from 'react-native';

const {width, height} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

const dimensions = {
  imgHeight: height / 2.3,
  imgWidth: width / 1.35,
  dotWidth: width * 0.09,
  dotHeight: height * 0.005,
  titleFontSize: 30,
  bodyFontSize: 16,
};

export default StyleSheet.create({
  rbSheetContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContainer: {
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textStyle: {
    alignSelf: 'center',
    flex: 1,
  },
  headerText: {
    fontSize: 20,
    fontFamily: FontFamily?.OpenSansBold,
    textAlign: 'center',
    color: BaseColors.textColor,
  },
  dropdownContainer: {
    width: '100%',
    marginBottom: 20,
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderWidth: 1,
    borderColor: BaseColors.lightBlack,
    borderRadius: 8,
    marginBottom: 10,
    backgroundColor: BaseColors.lightBlack,
  },
  dropdownText: {
    fontSize: 16,
    color: BaseColors.titleTextColor,
    fontFamily: FontFamily.OpenSansMedium,
  },
  uploadButton: {
    backgroundColor: '#1d559f',
    paddingVertical: 15,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
  },
  uploadButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  subMenuContainer: {
    padding: 10,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginTop: 10,
    marginBottom: 10,
  },
  iconSty: {
    alignContent: 'flex-end',
    // position: 'absolute',
    // right: -90,
  },
});
