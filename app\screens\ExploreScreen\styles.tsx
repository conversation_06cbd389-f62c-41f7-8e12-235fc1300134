import {BaseColors, FontFamily} from '@config/theme';
import {StyleSheet} from 'react-native';
import {Dimensions} from 'react-native';
import {Platform} from 'react-native';
const IOS = Platform.OS === 'ios';
export default StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
  },
  nosearchView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: Dimensions.get('window').height / 1.6,
  },
  noFound: {
    fontSize: 26,
    fontFamily: FontFamily.medium,
    lineHeight: 51,
    color: BaseColors.textGrey,
    marginVertical: 20,
  },
});
