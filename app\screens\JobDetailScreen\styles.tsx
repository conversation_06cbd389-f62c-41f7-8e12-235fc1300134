import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {StyleSheet} from 'react-native';
import {Dimensions} from 'react-native';
const HEIGHT = Dimensions.get('window').height;
const {width} = Dimensions.get('window');

export default StyleSheet.create({
  container: {
    backgroundColor: BaseColors.white,
    flex: 1,
    // marginBottom: 10,
    paddingBottom: 20,
  },
  imgView: {
    borderRadius: width * 0.0125, // Assuming 5 is about 1.25% of the width
    borderWidth: width * 0.0025, // Assuming 1 is about 0.25% of the width
    borderColor: BaseColors.secondaryBorder,
    width: width * 0.12, // Assuming 40 is 10% of the width
    height: width * 0.12, // Matching width for a square shape
  },
  row: {
    flexDirection: 'row',
    // justifyContent: 'space-around', // Space between items
    alignSelf: 'center', // Align items vertically
    padding: 5,
    marginTop: 10,
  },
  skillText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.primary,
    paddingBottom: 4,
  },
  dateText: {
    fontFamily: FontFamily.OpenSansRegular,
    color: '#000',
  },
  applicantsLink: {
    textDecorationLine: 'underline',
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    marginLeft: 5,
  },
  skillsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 10,
  },
  skillBadge: {
    borderWidth: 1,
    borderColor: BaseColors.primary,
    borderRadius: 5,
    paddingHorizontal: 10,
    marginHorizontal: 5,
  },
  skillText: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: 5,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    marginTop: 20,
  },
  sectionTitle: {
    fontFamily: FontFamily.OpenSansBold,
    fontSize: 14,
    color: '#202020',
  },
  seeAll: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
  },
  centerMain: {
    // flex: 1,
    alignContent: 'center',
    justifyContent: 'center',
    display: 'flex',
    // marginTop: HEIGHT / 7,
  },
  applicantsList: {
    paddingHorizontal: 15,
    marginTop: 10,
    marginBottom: 12,
  },
  cardContainer: {
    width: Dimensions.get('screen').width * 0.41,
    backgroundColor: '#fff',
    borderRadius: 10,
    marginRight: 15,
    padding: 10,
    borderWidth: 0.5,
    borderColor: BaseColors.black,
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignSelf: 'center',
  },
  cardInfo: {
    marginTop: 10,
    alignItems: 'center',
  },
  cardName: {
    fontFamily: FontFamily.OpenSansBold,
    fontSize: 14,
    color: '#000',
  },
  cardSubtitle: {
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 12,
    color: '#666',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  rating: {
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 12,
    color: '#000',
  },
  proBadge: {
    backgroundColor: BaseColors.activeColor,
    color: BaseColors.primary,
    fontSize: 10,
    borderRadius: 5,
    paddingHorizontal: 10,
    marginLeft: 5,
    padding: 3,
  },
  levelBadge: {
    backgroundColor: BaseColors.activeColor,
    color: BaseColors.primary,
    fontSize: 10,
    borderRadius: 5,
    paddingHorizontal: 5,
    marginLeft: 5,
    paddingTop: 3,
  },
  section: {
    marginTop: 20,

  },
  imageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  imagePlaceholder: {
    width: 60,
    height: 60,
    backgroundColor: '#f5f5f5',
    borderRadius: 5,
  },
  descriptionText: {
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 12,
    color: '#666',
    marginTop: 10,
  },
  imageWrapper: {
    position: 'relative',
    marginRight: 10,
    marginBottom: 10,
    width: Dimensions.get('screen').width * 0.25,
    height: Dimensions.get('screen').height / 8,
    borderWidth: 0.6,
    borderRadius: 5,
    borderColor: BaseColors.black,
  },
  uploadedImage: {
    width: '100%',
    height: '100%',
    borderRadius: 5,
  },
  imagesRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  msgIconStyle: {
    right: 0,
    width: 35,
    height: 35,
    // borderWidth: 1,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColors.primary,
  },
});
