import {StyleSheet} from 'react-native';
import {Platform} from 'react-native';
import {BaseColors} from '../../config/theme';
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingVertical: 20,
  },
  stepItem: {
    width: 70,
    alignItems: 'center',
    position: 'relative',
  },
  stepCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#CACACA',
    textAlign: 'center',
    lineHeight: 30,
    color: '#CACACA',
    backgroundColor: 'transparent', // Default background
  },
  activeCircle: {
    borderColor: BaseColors.primary,
    color: BaseColors.primary,
  },
  completedCircle: {
    backgroundColor: BaseColors.primary, // Blue background for completed steps
    color: '#FFFFFF', // White text color for completed steps
    borderColor: BaseColors.primary,
  },
  stepLabel: {
    fontSize: 14,
    color: '#CACACA',
    marginTop: 5,
    fontWeight: '400',
  },
  activeLabel: {
    color: BaseColors.primary,
    fontWeight: '400',
    fontSize: 14,
  },
  completedLabel: {
    color: BaseColors.primary, // Blue label for completed steps
    fontSize: 14,
    fontWeight: '600',
  },
  stepLine: {
    width: 35,
    height: 2,
    backgroundColor: '#CACACA',
    position: 'absolute',
    left: 61,
    top: 27,
  },
  componentContainer: {
    flex: 1,
    marginTop: 20,
    marginHorizontal: 20,
  },
});
