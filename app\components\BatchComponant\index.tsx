import React, {useState} from 'react';
import {
  Modal,
  StyleProp,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
} from 'react-native';
import ConfettiCannon from 'react-native-confetti-cannon';
import FastImage from 'react-native-fast-image';
import styles from './styles';
import {translate} from '@language/Translate';
import {BaseColors} from '@config/theme';
import {Images} from '@config/images';
import LottieView from 'lottie-react-native';
import Button from '@components/UI/Button';
import Micon from 'react-native-vector-icons/MaterialCommunityIcons';
import RewardNotification from '@components/RewardNotification';

const Animated = require('react-native-reanimated').default;
const FadeInDown = require('react-native-reanimated').FadeInDown;

interface BatchComponentProps {
  visible: boolean;
  modalTitle: string;
  modalDescription?: string;
  buttonTxt?: string;
  highLightText?: string;
  onClickSaveBtn?: () => void;
  showDropDown?: boolean;
  cancelText?: string;
  showCancelBtn?: boolean;
  style?: StyleProp<ViewStyle>;
  dropDown?: boolean;
  loading?: boolean;
  logoutModal?: boolean;
  setModalVisible?: (visible: boolean) => void;
  modalHeder?: string;
  type?: string;
  state?: any;
}

const BatchComponent: React.FC<BatchComponentProps> = ({
  visible,
  modalTitle,
  modalDescription,
  highLightText,
  style,
  setModalVisible = () => {},
  modalHeder = '',
  type = '',
  state = {},
}) => {
  const [confeti, setConfeti] = useState(true);
  console.log('state ===>', state);
  const [userXP, setUserXP] = useState(240); // Starting XP
  const [userLevel, setUserLevel] = useState(3); // Current level
  const xpRequiredForLevel4 = 500; // XP required for next level
  return (
    <View style={styles.container}>
      {/*  <StatusBar barStyle={'dark-content'} /> */}

      {/* Confetti behind the modal */}
      {confeti && (
        <View style={styles.confettiContainer}>
          <ConfettiCannon
            count={50}
            explosionSpeed={200}
            origin={{x: 0, y: 0}}
            onAnimationEnd={() => {
              setConfeti(false);
            }}
          />
        </View>
      )}

      <Modal
        animationType="fade"
        transparent={true}
        visible={visible}
        style={style as ViewStyle}
        onRequestClose={() => setModalVisible(false)}>
        <TouchableWithoutFeedback>
          <View style={styles.modalOverlay}>
            <View style={styles.modalView}>
              <Animated.View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: 10,
                }}
                entering={FadeInDown}
                duration={7000}>
                {state?.action?.action === 'streakNotification' ? (
                  <TouchableOpacity 
                    style={[
                      styles.floatingButton,
                      {backgroundColor: BaseColors.primary},
                      style,
                    ]}
                    activeOpacity={1}>
                    <Micon name={'fire'} color={BaseColors.white} size={26} />
                  </TouchableOpacity>
                ) : type === 'normal' ? (
                  <LottieView
                    autoPlay={true}
                    style={styles.loader}
                    source={Images.batch}
                  />
                ) : (
                  <FastImage
                    source={
                      state?.meta?.currentBadge === 'Bronze'
                        ? Images.bronze
                        : state?.meta?.currentBadge === 'Silver'
                        ? Images.silver
                        : state?.meta?.currentBadge === 'Gold'
                        ? Images.silver
                        : state?.meta?.currentBadge === 'Platinum'
                        ? Images.platinum
                        : Images.Diamond
                    }
                    style={styles.medalIcon}
                    resizeMode="contain"
                  />
                )}
              </Animated.View>
              <Animated.View entering={FadeInDown} duration={7000}>
                <Text style={styles.modalHeder}>
                  {state?.action?.action === 'streakNotification'
                    ? modalTitle
                    : 'Congratulations!'}
                </Text>
              </Animated.View>
              <Animated.View entering={FadeInDown} duration={7000}>
                {/* <Text style={styles.modalTitleText}>{modalTitle}</Text> */}
                <Text style={styles.modalText}>
                  {modalDescription}{' '}
                  <Text
                    style={[styles.modalText, {color: BaseColors.textColor}]}>
                    {highLightText}
                  </Text>
                </Text>
              </Animated.View>
              <RewardNotification
                visible={visible} // Ensure it's passed as true when modal is visible
                // onClose={() => setModalVisible(false)}
                currentXP={state.meta?.totalPoints}
                xpGained={state?.meta?.updatedXp}
                xpRequiredForNextLevel={state?.meta?.endingPoints}
                badge={state?.meta}
              />
              <View style={styles.btnView}>
                <Button
                  onPress={() => {
                    setModalVisible(false);
                  }}
                  type="text">
                  {translate('Continue')}
                </Button>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

export default BatchComponent;
