import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {translate} from '@language/Translate';
import {useSelector} from 'react-redux';
import {useAppDispatch} from '@components/UseRedux';
import UserConfigActions from '@redux/reducers/userConfig/actions';

const {width} = Dimensions.get('window');

interface PaymentProcessingProps {
  visible: boolean;
  onClose: () => void;
}

const PaymentProcessing: React.FC<PaymentProcessingProps> = ({
  visible,
  onClose,
}) => {
  const dispatch = useAppDispatch();
  const {updateJobData, isPaymentProcessing} = useSelector((state: any) => state.userConfig);
  const [timeElapsed, setTimeElapsed] = useState(0);

  // Debug logs
  useEffect(() => {
    console.log('PaymentProcessing visible:', visible);
    console.log('PaymentProcessing isPaymentProcessing:', isPaymentProcessing);
  }, [visible, isPaymentProcessing]);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (visible) {
      // Reset timer when modal becomes visible
      setTimeElapsed(0);

      // Start timer to track elapsed time
      timer = setInterval(() => {
        setTimeElapsed(prev => prev + 1);
      }, 1000);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [visible]);

  // Close the modal when updateJobData is true (payment notification received)
  useEffect(() => {
    if (updateJobData && visible) {
      onClose();
      // Reset updateJobData after closing the modal
      dispatch(UserConfigActions.setUpdateJobData(false));
    }
  }, [updateJobData, visible, onClose, dispatch]);

  // Auto-close after 30 seconds if no notification is received
  useEffect(() => {
    if (timeElapsed >= 30 && visible) {
      onClose();
    }
  }, [timeElapsed, visible, onClose]);

  return (
    <Modal
      transparent
      animationType="fade"
      visible={visible}
      onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.modalContent}>
          <ActivityIndicator size="large" color={BaseColors.primary} />
          <Text style={styles.title}>{translate('processPayemnt')}</Text>
          <Text style={styles.message}>
            {translate('paymentStart')}
          </Text>
          {timeElapsed >= 15 && (
            <Text style={styles.note}>
              {translate('paymentTooMuchTime')}
            </Text>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: width * 0.85,
    backgroundColor: BaseColors.white,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontFamily: FontFamily.OpenSansBold,
    fontSize: 18,
    color: BaseColors.textColor,
    marginTop: 15,
    marginBottom: 10,
    textAlign: 'center',
  },
  message: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 14,
    color: BaseColors.textBlack,
    marginBottom: 10,
    textAlign: 'center',
  },
  note: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 12,
    color: BaseColors.textBlack,
    fontStyle: 'italic',
    marginTop: 10,
    textAlign: 'center',
  },
});

export default PaymentProcessing;
