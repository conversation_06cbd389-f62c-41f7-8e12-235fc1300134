import React from 'react';
import {View, Text, TouchableOpacity, LayoutChangeEvent} from 'react-native';
import FastImage from 'react-native-fast-image';
import styles from './styles'; // Replace with your actual styles import
import {Rating} from 'react-native-ratings';
import {BaseColors} from '@config/theme';
import {Images} from '@config/images';

interface ReviewRatingProps {
  review: any;
  reviewId: string | number;
  isExpanded: boolean;
  showSeeMore: boolean;
  toggleExpansion: (id: string | number) => void;
  handleLayout: (e: LayoutChangeEvent, id: string | number) => void;
  getTimeAgo: (date: string) => string;
  type: string;
}

const ReviewRatingComponant: React.FC<ReviewRatingProps> = ({
  review,
  reviewId,
  isExpanded,
  showSeeMore,
  toggleExpansion,
  handleLayout,
  getTimeAgo,
  type,
}) => {
  const profilePhoto =
    review?.employer?.profilePhoto || review?.seeker?.profilePhoto;
  return (
    <React.Fragment key={reviewId}>
      <View style={styles?.reviewView}>
        <View style={styles?.mainReviewSty}>
          <View style={styles?.mainReviewView}>
            <View style={styles?.mainImgView}>
              <View style={styles?.imageViewSty}>
                <FastImage
                  source={
                    profilePhoto
                      ? {
                          uri:
                            review?.employer?.profilePhoto ||
                            review?.seeker?.profilePhoto,
                        }
                      : Images.user
                  }
                  style={styles?.imgSty}
                  resizeMode="contain"
                />
              </View>
              <View>
                <Text style={styles?.txtSty}>
                  {(review?.employer?.firstName || review?.seeker?.firstName) +
                    ' ' +
                    (review?.employer?.lastName || review?.seeker?.lastName)}
                </Text>
              </View>
            </View>
            <View style={styles?.ratingView}>
              <Rating
                type="custom"
                ratingColor="#FFD700" // Replace with your star color
                ratingBackgroundColor="#E0E0E0"
                ratingCount={5}
                imageSize={23}
                tintColor={
                  type === 'userDetail' ? BaseColors.inputBackground : 'FFFFFF'
                }
                readonly
                startingValue={review.rating}
              />
            </View>
          </View>
          <View style={styles?.decscriptionView}>
            <View style={styles?.txtView}>
              <Text
                style={styles?.descriptionReviewSty}
                numberOfLines={isExpanded ? undefined : 3}
                ellipsizeMode={!isExpanded ? 'tail' : undefined}
                onLayout={e => handleLayout(e, reviewId)}>
                {review?.description || 'No description available.'}
              </Text>
              {showSeeMore && (
                <TouchableOpacity onPress={() => toggleExpansion(reviewId)}>
                  <Text style={styles?.seeMoreText}>
                    {isExpanded ? 'See Less' : 'See More'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
            <View style={styles?.timeView}>
              <Text style={[styles?.txtSty, {fontSize: 12}]}>
                {getTimeAgo(review?.createdAt)}
              </Text>
            </View>
          </View>
        </View>
      </View>
      <View style={styles?.borderLine} />
    </React.Fragment>
  );
};

export default ReviewRatingComponant;
