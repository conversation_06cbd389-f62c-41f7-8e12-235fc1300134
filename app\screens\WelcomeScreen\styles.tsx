import {Dimensions, Platform, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const {width, height} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  innerContainer: {
    paddingHorizontal: width * 0.05,
  },
  mainImgView: {
    alignSelf: 'center',
    paddingTop: height * 0.09,
  },
  imgView: {
    alignItems: 'center',
    justifyContent: 'center',
    width: width * 0.4,
    height: height * 0.09,
  },
  titleContainer: {
    paddingTop: height * 0.01,
  },
  mainTitle: {
    textAlign: 'center',
    fontSize: width * 0.08,
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansSemiBold,
  },
  welcomeText: {
    textAlign: 'center',
    fontSize: width * 0.055,
    color: BaseColors.textColor,
    paddingTop: height * 0.012,
    fontFamily: FontFamily.OpenSansRegular,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: width * 0.04,
    color: BaseColors.textColor,
    paddingTop: height * 0.012,
    fontFamily: FontFamily.OpenSansRegular,
  },
  inputContainer: {
    marginTop: height * 0.056,
  },
  noErrorContainer: {
    borderColor: BaseColors.white,
  },
  inputTitle: {
    color: BaseColors.inputColor,
  },
  textInput: {
    color: BaseColors.inputColor,
  },
  codeText: {
    color: BaseColors.inputColor,
  },
  textInputContainer: {
    borderColor: BaseColors.primary,
    borderWidth: width * 0.003,
    borderRadius: width * 0.02,
  },
  verifyCodeText: {
    fontSize: width * 0.035,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  buttonContainer: {
    marginTop: height * 0.08,
    marginHorizontal: width * 0.08,
  },
  buttonStyle: {
    backgroundColor: BaseColors.primary,
  },
  toggleContainer: {
    paddingTop: height * 0.012,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  toggleText: {
    textAlign: 'center',
    fontSize: width * 0.045,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  toggleActionText: {
    textAlign: 'center',
    paddingHorizontal: width * 0.025,
    fontSize: width * 0.045,
    color: BaseColors.primary,
    textDecorationLine: 'underline',
    fontFamily: FontFamily.OpenSansRegular,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: height * 0.04,
  },
  divider: {
    width: width * 0.3,
    borderWidth: height * 0.0005,
    borderColor: '#BDBDBD',
  },
  orText: {
    paddingHorizontal: width * 0.04,
    fontSize: width * 0.04,
    fontFamily: FontFamily.OpenSansRegular,
  },
  appleButton: {
    marginVertical: height * 0.025,
    marginHorizontal: width * 0.08,
  },
  googleButton: {
    marginHorizontal: width * 0.08,
  },
  continueText: {
    textAlign: 'center',
    color: BaseColors.primary,
  },
  svgView: {
    position: 'absolute',
    top: -height * 0.02,
  },
  subViewTxt: {
    paddingTop: 0,
    alignSelf: 'center',
  },
  txtStye: {
    fontSize: width * 0.07,
    fontFamily: FontFamily.OpenSansSemiBold,
    color: BaseColors.textBlack,
  },
  cardViewSty: {
    borderWidth: width * 0.003,
    borderColor: BaseColors.white,
    borderBottomWidth: width * 0.003,
    borderBottomLeftRadius: width * 0.03,
    borderBottomRightRadius: width * 0.03,
    backgroundColor: BaseColors.textGrey,
    marginTop: height * 0.03,
    borderRadius: width * 0.03,
  },
  insideView: {
    flexDirection: 'row',
    padding: width * 0.012,
    alignItems: 'center',
    borderWidth: width * 0.003,
    borderBottomLeftRadius: width * 0.03,
    borderBottomRightRadius: width * 0.03,
    backgroundColor: BaseColors.white,
    borderColor: BaseColors.textGrey,
    borderRadius: width * 0.03,
  },
  sekerTxtSty: {
    fontSize: width * 0.065,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansSemiBold,
    paddingLeft: width * 0.04,
  },
  bottomView: {
    borderColor: BaseColors.textGrey,
    borderBottomWidth: 0,
    backgroundColor: BaseColors.textGrey,
    padding: IOS ? height * 0.015 : height * 0.01,
    borderBottomLeftRadius: width * 0.03,
    borderBottomRightRadius: width * 0.03,
  },
  jobTxtty: {
    textAlign: 'center',
    color: BaseColors?.white,
    fontFamily: FontFamily.OpenSansSemiBold,
    paddingBottom: IOS ? 0 : height * 0.005,
    fontSize: width * 0.035,
  },
  btnViewSty: {
    marginHorizontal: width * 0.05,
  },
  laterTxtSty: {
    color: BaseColors.primary,
    fontSize: width * 0.05,
    fontFamily: FontFamily.OpenSansRegular,
  },
  laterTxtView: {
    alignSelf: 'center',
    marginVertical: height * 0.04,
  },
  imageView: {
    width: width * 0.4,
    height: height * 0.1,
  },
  mainImagecontainer: {
    padding: height * 0.02,
    paddingHorizontal: 0,
  },
});
