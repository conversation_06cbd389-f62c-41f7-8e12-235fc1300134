import React, {useState} from 'react';
import {View, Text, TouchableOpacity, ActivityIndicator} from 'react-native';
import styles from './styles';
import {translate} from '@language/Translate';
import {isArray, isEmpty} from '@app/utils/lodashFactions';
import moment from 'moment';
import EIcon from 'react-native-vector-icons/Entypo';
import {BaseColors} from '@config/theme';
import BaseSetting from '@config/setting';
import {getApiData} from '@app/utils/apiHelper';
// import Toast from 'react-native-simple-toast';
import {CustomIcon} from '@config/LoadIcons';
import {useDispatch} from 'react-redux';
import AIcon from 'react-native-vector-icons/Feather';
import FastImage from 'react-native-fast-image';
import FIcon from 'react-native-vector-icons/FontAwesome'; // Replace with your specific icon library if different

const CertificateCard: React.FC<CertificateCardProps> = ({
  certificationList,
  setCertificationList,
  preCertificate,
  setPreCertificate,
  reviewType,
  setEditCertificate,
  editCertificate,
  refRBSheet,
  setEditCertificateList,
  setEdit,
  navigation,
  userDetails,
  setCheckSaveCerti,
  setCheckSaveExperince,
  setAddData,
}) => {
  const [deletingIndex, setDeletingIndex] = useState<number | null>(null); // For individual deletion
  const [deletingAll, setDeletingAll] = useState<boolean>(false); // For "delete all"
  const [showAll, setShowAll] = useState(false);
  const dispatch = useDispatch();

  if (
    reviewType === 'reviewbyEmployer'
      ? isEmpty(userDetails?.certifications) ||
        !isArray(userDetails?.certifications)
      : isEmpty(preCertificate) || !isArray(preCertificate)
  ) {
    return null;
  }

  const deleteFile = async (type: string, index?: number) => {
    if (type === 'all') {
      setDeletingAll(true); // Set deletingAll to true when deleting all files
    } else {
      setDeletingIndex(index); // Set the index of the certificate being deleted
    }

    let array = {};

    // If deleting a single file, send the specific file id
    if (index !== undefined) {
      const certificate = preCertificate[index];
      array = [certificate.id]; // Send only the id of the certificate to be deleted
    }
    // If deleting all files, collect all ids in an array
    else if (type === 'all') {
      const ids = preCertificate.map(cert => cert.id); // Collect all ids from the preCertificate
      array = ids; // Send all ids as an array
    }

    try {
      const url = BaseSetting.endpoints.deleteQualification;
      const resp = await getApiData({
        endpoint: url,
        method: 'POST',
        data: {array},
      });

      if (resp?.status) {
        // Toast.show(resp?.message, Toast.BOTTOM);

        if (type === 'certificate' && index !== undefined) {
          // Remove the specific file from the list after successful deletion
          const updatedFiles = preCertificate.filter((_, i) => i !== index);
          setPreCertificate(updatedFiles);
        }
        // If deleting all files
        else if (type === 'all') {
          setPreCertificate([]); // Clear all files from the list
        }
      } else {
        console.log('🚀 ~ deleteFile ~ Failed to delete file');
        // Toast.show(resp?.message, Toast.BOTTOM);
      }
    } catch (err) {
      // Toast.show(err?.message || 'Something went wrong.', Toast.LONG);
    }

    // Reset loader states
    setDeletingIndex(null); // Reset deletingIndex after individual file deletion
    setDeletingAll(false); // Reset deletingAll after deleting all files
  };

  const visibleExperiences = showAll
    ? reviewType === 'reviewbyEmployer'
      ? userDetails?.certifications
      : preCertificate
    : reviewType === 'reviewbyEmployer'
    ? userDetails?.certifications.slice(0, 1)
    : preCertificate.slice(0, 2);

  return (
    <View>
      {visibleExperiences?.length > 0 && (
        <View style={styles.card}>
          <View>
            <Text style={styles?.titleTxtSty}>
              {translate('certificate', '')}
            </Text>
            {reviewType === 'reviewbyEmployer' ||
            reviewType === 'review' ? null : (
              <TouchableOpacity
                onPress={() => deleteFile('all')} // Pass 'all' to delete all certificates
                style={styles.removeFileButton}>
                {deletingAll ? (
                  <ActivityIndicator size="small" color={BaseColors.textGrey} />
                ) : (
                  <EIcon name="cross" size={20} color={BaseColors.textGrey} />
                )}
              </TouchableOpacity>
            )}
          </View>
          {visibleExperiences.map((certificate, index) => (
            <React.Fragment key={certificate?.id || index}>
              <View style={styles.cardRow}>
                <View style={styles?.certificateMainView}>
                  <View style={{position: 'absolute', top: -5, right: 0}}>
                    <View style={styles.crossView}>
                      {reviewType === 'reviewbyEmployer' ||
                      reviewType === 'review' ? null : (
                        <View style={styles?.iconeditViewSty}>
                          <TouchableOpacity
                            onPress={() => {
                              setEdit(false);
                              setEditCertificate(true);
                              setCheckSaveExperince(false);
                              setCheckSaveCerti(false);
                              setAddData('Upload Certifications');
                              setEditCertificateList(certificate);
                              refRBSheet.current?.open();
                            }} // Pass 'experince' and index for single file deletion
                            style={styles.editIconsty}>
                            <AIcon
                              name="edit-2"
                              size={14}
                              color={BaseColors.textGrey}
                            />
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => deleteFile('certificate', index)} // Pass 'certificate' and index for single file deletion
                            style={styles.removeFileButton}>
                            {deletingIndex === index ? (
                              <ActivityIndicator
                                size="small"
                                color={BaseColors.textGrey}
                              />
                            ) : (
                              <EIcon
                                name="cross"
                                size={20}
                                color={BaseColors.textGrey}
                              />
                            )}
                          </TouchableOpacity>
                        </View>
                      )}
                    </View>
                  </View>
                  <TouchableOpacity
                    onPress={() => {
                      // Check for image files
                      if (
                        /\.(jpg|jpeg|png|gif)$/i.test(certificate?.fileName)
                      ) {
                        navigation.navigate('GalleryView', {
                          images: [certificate?.filePath],
                          index: 0,
                        });
                      }
                      // Check for document files
                      else if (/\.(pdf|docx)$/i.test(certificate?.fileName)) {
                        navigation.navigate('WebViewScreen', {
                          uri: certificate?.filePath || certificate?.fileName,
                          type: 'profile',
                        });
                      }
                      // Handle other file types if needed (e.g., show file icon)
                      else {
                        console.log('File type not supported for preview');
                      }
                    }}
                    style={styles?.imageView}>
                    {certificate?.fileName ? (
                      // Check for image files
                      /\.(jpg|jpeg|png|gif)$/i.test(certificate?.fileName) ? (
                        <FastImage
                          source={{
                            uri: certificate?.filePath || certificate?.fileName,
                          }}
                          style={{width: '100%', height: '100%'}}
                          resizeMode="contain"
                        />
                      ) : /\.(pdf|docx)$/i.test(certificate?.fileName) ? (
                        // Check for document files (pdf, docx, etc.)
                        <FIcon
                          name="file-pdf-o" // You can replace this with the appropriate icon if needed
                          size={30}
                          color={BaseColors.primary}
                        />
                      ) : (
                        // Default for unsupported file types
                        <FIcon
                          name="file"
                          size={30}
                          color={BaseColors.primary}
                        />
                      )
                    ) : (
                      <FIcon name="file" size={30} color={BaseColors.primary} />
                    )}
                  </TouchableOpacity>

                  <View style={styles?.txtViewSty}>
                    <Text style={styles?.certificateTitleSty}>
                      {certificate?.company || certificate?.docTitle}
                    </Text>
                    <Text style={styles?.dateTxtSty}>
                      {certificate?.startDate
                        ? moment(certificate?.startDate).format('M/D/YYYY')
                        : 'N/A'}{' '}
                      -{' '}
                      {certificate?.endDate
                        ? moment(certificate?.endDate).format('M/D/YYYY')
                        : 'N/A'}
                    </Text>
                  </View>
                  {certificate?.isVerified ? (
                    <View style={styles?.verificationSty}>
                      <CustomIcon
                        name="Vector"
                        color={BaseColors.primary}
                        size={20}
                      />
                    </View>
                  ) : (
                    <>
                      <View />
                    </>
                  )}
                </View>
              </View>
            </React.Fragment>
          ))}
          {reviewType === 'reviewbyEmployer'
            ? userDetails?.certifications.length > 2
            : preCertificate.length > 2 && (
                <TouchableOpacity
                  onPress={() => setShowAll(!showAll)}
                  style={styles.seeMoreButton}>
                  <Text style={styles.seeMoreText}>
                    {showAll ? translate('Less', '') : translate('More', '')}
                  </Text>
                </TouchableOpacity>
              )}
        </View>
      )}
    </View>
  );
};

export default CertificateCard;
