import React, {useState, useEffect} from 'react';
import {
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import styles from './styles';
import Header from '@components/Header';
import {translate} from '@language/Translate';
import FastImage from 'react-native-fast-image';
import AIcon from 'react-native-vector-icons/AntDesign';
import {BaseColors} from '@config/theme';
import WebView from 'react-native-webview';
import BaseSetting from '@config/setting';
import {getApiData} from '@app/utils/apiHelper';

export default function VideoTutorial({navigation}: any) {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [selectedTitle, setSelectedTitle] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState<Boolean>(false);
  const [videoTitle, setVideoTitle] = useState('');
  const [videoId, setVideoId] = useState('');
  const [videoListurl, setVideoListUrl] = useState('');
  const [videoList, setVideoList] = useState([]);

  const openVideo = async (videoId: string) => {
    setLoading(true);
    setSelectedVideo(videoId);
    try {
      const response = await fetch(
        `https://noembed.com/embed?url=https://www.youtube.com/watch?v=${videoId}`,
      );
      const data = await response.json();
      if (data.title) {
        setSelectedTitle(data.title);
      } else {
        setSelectedTitle('Unknown Title');
      }
    } catch (error) {
      console.error('Error fetching video title:', error);
      setSelectedTitle('Error Loading Title');
    }
    setModalVisible(true);
    setLoading(false);
  };

  const extractVideoId = (url: string) => {
    if (!url) return null;
    const regExp =
      /^.*(?:youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[1].length === 11 ? match[1] : null;
  };

  const getVideoList = async () => {
    setLoader(true);

    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.videoTutorialList,
        method: 'GET',
      });
      console.log('🚀 ~ getVideoList ~ resp:', resp);

      if (resp?.data && resp?.status) {
        const processedData = resp.data.map((item: any) => ({
          id: item.id.toString(),
          videoId: extractVideoId(item.url),
          title: item.title, // Use the provided title
        }));
        setVideoList(processedData);
      } else {
        console.log('Error fetching video list');
      }

      setLoader(false);
    } catch (e) {
      setLoader(false);
      console.log('Error:', e);
    }
  };

  useEffect(() => {
    getVideoList();
  }, []);

  const renderItem = ({
    item,
  }: {
    item: {id: string; videoId: string; title: string};
  }) => (
    <View style={styles.mainViewSty}>
      <TouchableOpacity
        activeOpacity={0.9}
        style={styles.videoBoxSty}
        onPress={() => openVideo(item.videoId)}>
        {/* Thumbnail Image */}
        <FastImage
          source={{
            uri: `https://img.youtube.com/vi/${item.videoId}/hqdefault.jpg`,
          }}
          style={{width: '100%', height: '100%', borderRadius: 10}}
          resizeMode={FastImage.resizeMode.cover}
        />
        <View style={styles.overlay}>
          <AIcon name="playcircleo" size={35} color={BaseColors.white} />
        </View>
        {/* Video Title */}
        <View style={styles.Textoverlay}>
          <Text style={styles.textSty}>{item.title}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
  return (
    <View style={styles.mainContainer}>
      <Header
        leftIcon="back-arrow"
        title={translate('Video Tutorial', '')}
        onLeftPress={() => navigation.goBack()}
      />
      <View style={styles?.videoSty}>
        {loader ? (
          <View
            style={{
              flex: 1,
              backgroundColor: BaseColors?.white,
              alignContent: 'center',
              justifyContent: 'center',
            }}>
            <ActivityIndicator size="large" color={BaseColors.primary} />
          </View>
        ) : (
          <FlatList
            data={videoList} // Set the processed video list here
            renderItem={renderItem}
            keyExtractor={item => item.id.toString()} // Ensure id is a string
          />
        )}
      </View>
      {/* Modal for Video Player */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {/* Show Loading Indicator While Fetching */}
            {loading ? (
              <View
                style={{
                  display: 'flex',
                  backgroundColor: BaseColors?.black,
                  alignContent: 'center',
                }}>
                <ActivityIndicator size="large" color={BaseColors.red} />
              </View>
            ) : (
              <>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setModalVisible(false)}>
                  <AIcon
                    name="closecircle"
                    size={30}
                    color={BaseColors.white}
                  />
                </TouchableOpacity>
                {selectedVideo && (
                  <WebView
                    source={{
                      uri: `https://www.youtube.com/embed/${selectedVideo}?autoplay=1&controls=1`,
                    }}
                    style={styles.webView}
                    allowsFullscreenVideo
                  />
                )}
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}
