@echo off
echo Cleaning Android build...

REM Remove build directories
rmdir /s /q android\.gradle
rmdir /s /q android\app\build
rmdir /s /q android\build

REM Clean Gradle caches
cd android
call gradlew.bat clean
call gradlew.bat --stop

REM Remove node_modules/.cache
cd ..
rmdir /s /q node_modules\.cache

REM Clean watchman state
watchman watch-del-all 2>nul

REM Clean Metro cache
del /q /s %TEMP%\metro-*

REM Clean React Native caches
rmdir /s /q %APPDATA%\Temp\react-native-*

echo Rebuilding Android...

REM Force reinstall node_modules
echo Reinstalling node modules is recommended if this clean build doesn't work
REM uncomment the next line to force reinstall:
REM npm install

REM Rebuild the project with increased memory
cd android
set GRADLE_OPTS=-Xmx4g -XX:MaxMetaspaceSize=1g
call gradlew.bat assembleDebug --no-daemon

echo Build process completed. 