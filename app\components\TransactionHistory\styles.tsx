import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    backgroundColor: BaseColors.white,
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 20,
    marginBottom: 20,
  },
  date: {
    fontSize: 12,
    color: BaseColors.textGrey,
    alignSelf: 'flex-end',
    fontFamily: FontFamily.OpenSansMedium,
  },
  renderView: {
    paddingVertical: 7,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 1,
    marginVertical: 1,
  },
  id: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansMedium,
  },
  name: {
    fontSize: 16,
    flex: 1,
    color: BaseColors.blackColor,
    fontFamily: FontFamily.OpenSansMedium,
    textTransform: 'capitalize',
  },
  title: {
    fontSize: 14,
    flex: 1,
    color: BaseColors.lightGrey,
    fontFamily: FontFamily.OpenSansMedium,
    textTransform: 'capitalize',
  },
  amount: {
    fontSize: 14,
    color: BaseColors.blackColor,
    fontFamily: FontFamily.OpenSansMedium,
  },
  separator: {
    borderWidth: 0.5,
    borderColor: BaseColors.borderColor,
  },
  titleText: {
    fontSize: 20,
    color: BaseColors.blackColor,
  },
  clipBoard: {
    // padding: 10,
    alignItems: 'center',
    // justifyContent: 'center',
    alignSelf: 'center',
    // marginLeft: 5,
    // paddingLeft: 10,
    paddingVertical: 2,
    // backgroundColor: 'red',
  },
  descriptionText: {
    fontSize: 20,
    color: BaseColors.blackColor,
  },
  thirdRow: {
    borderWidth: 1,
    padding: 8,
    marginVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: BaseColors.primary + 20,
    borderColor: BaseColors.primary + 20,
  },
  warningText: {
    fontSize: 14,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansBold,
    marginTop: 4,
  },
  underline: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    height: 1, // Adjust this value to change the thickness of the underline
    backgroundColor: BaseColors.primary, // Adjust the color as needed
  },
  completeNowBtnStyle: {
    alignItems: 'center',
    position: 'relative',
  },
});
