/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import {Text, View, TouchableOpacity, Dimensions} from 'react-native';
import Button from '../../components/UI/Button';
import {initTranslate, translate} from '../../lang/Translate';
import IIcon from 'react-native-vector-icons/Ionicons';
import RBSheet from 'react-native-raw-bottom-sheet';
import langActions from '../../redux/reducers/language/actions';
import {useDispatch, useSelector} from 'react-redux';
import {store} from '../../redux/store/configureStore';
import styles from './styles';
import {BaseColors} from '@config/theme';
import FastImage from 'react-native-fast-image';
import {Images} from '@config/images';
const {width, height} = Dimensions.get('window');

interface LanguageModal {
  refRBSheet: any;
  setOpenBottomSheet: any;
  title: string;
  openLanguage: boolean;
  Description: string;
  cancelProp: any;
  onClick?: any;
  setSelectedLanguage?: any;
  selectedLanguage?: any;
  doneProp?: any;
  showDescription?: boolean;
  deleteSty: boolean;
  showCancelbutton?: boolean;
  loader?: boolean;
  type: any;
}

export default function LanguageModal({
  refRBSheet,
  setOpenBottomSheet,
  title,
  openLanguage,
  Description,
  cancelProp,
  onClick,
  setSelectedLanguage,
  selectedLanguage,
  doneProp,
  showDescription,
  deleteSty,
  showCancelbutton,
  loader,
  type,
}: LanguageModal) {
  return (
    <RBSheet
      ref={refRBSheet}
      height={260}
      useNativeDriver={false}
      closeOnDragDown
      onClose={() => {
        setOpenBottomSheet(false);
      }}
      closeOnPressMask
      customStyles={
        deleteSty && type === 'delete'
          ? styles.rbSheetCustomDltModalStyles
          : deleteSty
          ? styles.rbSheetCustomDltStyles
          : styles.rbSheetCustomStyles
      }>
      <View style={[styles.languageContainer]}>
        <Text style={styles.languageTitle}>{title}</Text>
        {openLanguage ? (
          <>
            <TouchableOpacity
              style={[
                styles.languageOption,
                {
                  borderColor:
                    selectedLanguage === 'en'
                      ? BaseColors.primary
                      : BaseColors.lightborderColor,
                },
              ]}
              onPress={() => setSelectedLanguage('en')}>
              <View style={styles.languageModalView}>
                <View style={styles.imageView}>
                  <FastImage
                    source={Images.usaPng}
                    style={{width: '100%', height: '100%'}}
                    resizeMode="contain"
                  />
                </View>
                <Text style={styles.languageText}>
                  {translate('English', '')}
                </Text>
              </View>
              {selectedLanguage === 'en' && (
                <IIcon
                  name="checkmark-sharp"
                  size={30}
                  color={BaseColors.primary}
                  style={styles.iconPadding}
                />
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.languageOption,
                {
                  borderColor:
                    selectedLanguage === 'es'
                      ? BaseColors.primary
                      : BaseColors.lightborderColor,
                },
              ]}
              onPress={() => setSelectedLanguage('es')}>
              <View style={styles.languageModalView}>
                <View style={styles.imageView}>
                  <FastImage
                    source={Images.SpanishPng}
                    style={{width: '100%', height: '100%'}}
                    resizeMode="contain"
                  />
                </View>
                <Text style={styles.languageText}>
                  {translate('Spanish', '')}
                </Text>
              </View>
              {selectedLanguage === 'es' && (
                <IIcon
                  name="checkmark-sharp"
                  size={30}
                  color={BaseColors.primary}
                  style={styles.iconPadding}
                />
              )}
            </TouchableOpacity>
          </>
        ) : (
          <>
            {showDescription ? (
              <View style={styles.descriptionView}>
                <Text style={styles.descriptionTxtSty}>{Description}</Text>
              </View>
            ) : null}
          </>
        )}
      </View>
      <View style={styles.languageButtonContainer}>
        {showCancelbutton ? (
          <Button onPress={onClick}   loading={loader} type="text" style={{width: '100%'}}>
            {doneProp}
          </Button>
        ) : (
          <>
            <Button
              onPress={() => {
                refRBSheet?.current?.close();
              }}
              type="outlined"
              style={{width: '40%'}}>
              {cancelProp}
            </Button>
            <Button
              loading={loader}
              onPress={onClick}
              type="text"
              style={{width: '40%'}}>
              {doneProp}
            </Button>
          </>
        )}
      </View>
    </RBSheet>
  );
}
