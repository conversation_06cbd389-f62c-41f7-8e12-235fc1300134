{"originHash": "a1569f9895aa2be8e24832f98525d5da4eb90b5d158a82691c15b47eb72a13d7", "pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "194a6706acbd25e4ef639bcaddea16e8758a3e27", "version": "1.2024011602.0"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk.git", "state": {"revision": "dbdfdc44bee8b8e4eaa5ec27eb12b9338f3f2bc1", "version": "11.5.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "4f234bcbdae841d7015258fbbf8e7743a39b8200", "version": "11.4.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "53156c7ec267db846e6b64c9f4c4e31ba4cf75eb", "version": "8.0.2"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "f56d8fc3162de9a498377c7b6cea43431f4f5083", "version": "1.65.1"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "5cfe5f090c982de9c58605d2a82a4fc77b774fbd", "version": "4.1.0"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "2d12673670417654f08f5f90fdd62926dc3a2648", "version": "100.0.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "ebc7251dd5b37f627c93698e4374084d98409633", "version": "1.28.2"}}], "version": 3}