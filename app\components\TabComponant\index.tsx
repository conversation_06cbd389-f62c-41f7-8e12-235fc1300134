import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import styles from './styles';
import {translate} from '@language/Translate';

const TabComponent = ({
  selectedTab,
  setSelectedTab,
  badge,
  loader,
}: {
  selectedTab?: string;
  setSelectedTab?: any;
  badge?: any;
  loader?: any;
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.tabs}>
        {/* Seeker Tab */}
        <TouchableOpacity
          disabled={loader ? true : false}
          onPress={() => setSelectedTab('Seeker')}
          style={styles.tab}>
          {Number(badge?.seekerTotal) > 0 ? (
            <TouchableOpacity style={styles.badgeCount} activeOpacity={0.8}>
              <Text style={styles.badgeStyle}>{badge?.seekerTotal}</Text>
            </TouchableOpacity>
          ) : null}
          <Text
            style={[
              styles.tabText,
              selectedTab === 'Seeker' && styles.activeText,
            ]}>
            {translate('seekers', '')}
          </Text>
        </TouchableOpacity>

        {/* Employer Tab */}
        <TouchableOpacity
          disabled={loader ? true : false}
          onPress={() => setSelectedTab('Employer')}
          style={styles.tab}>
          {Number(badge?.employerTotal) > 0 ? (
            <TouchableOpacity
              style={{...styles.badgeCount, right: 0}}
              activeOpacity={0.8}>
              <Text style={styles.badgeStyle}>{badge?.employerTotal}</Text>
            </TouchableOpacity>
          ) : null}
          <Text
            style={[
              styles.tabText,
              selectedTab === 'Employer' && styles.activeText,
            ]}>
            {translate('Employers', '')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Background bar and active indicator */}
      <View style={styles.barContainer}>
        <View style={styles.backgroundBar} />
        <View
          style={[
            styles.activeBar,
            {left: selectedTab === 'Seeker' ? '0%' : '50%'},
          ]}
        />
      </View>
    </View>
  );
};

export default TabComponent;
