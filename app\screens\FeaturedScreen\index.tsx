import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  RefreshControl,
  ScrollView,
  View,
} from 'react-native';
import Header from '@components/Header';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { BaseColors } from '@config/theme';
 import Toast from 'react-native-simple-toast';
import styles from './styles';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import NoRecord from '@components/NoRecord';
import isEmpty from 'lodash/isEmpty';
import { isArray } from '@app/utils/lodashFactions';
import { useIsFocused } from '@react-navigation/native';
import { LayoutChangeEvent } from 'react-native';
import AnimatedView from '@components/AnimatedView';
import SeekerCard from '@components/SeekerCard';
import EmployarCard from '@components/EmployerCard';

export default function FeaturedList({ navigation, route }: any) {
  const { params } = route;
  const { type, id, searchType } = params;

  const isFocused = useIsFocused();
  const [saveLoader, setSaveLoader] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'Seeker' | 'Employer'>(
    'Employer',
  );
  const [featuredList, setFeaturedList] = useState({
    data: [],
    pagination: { currentPage: 1, isMore: null },
  });

  const { data, pagination } = featuredList;
  console.log('🚀 ~ FeaturedList ~ data:', data);
  const [expandedReviews, setExpandedReviews] = useState<{
    [key: string]: boolean;
  }>({});
  const [isTruncated, setIsTruncated] = useState<{ [key: string]: boolean }>({});

  const lineHeight = 20; // Replace this with your actual line height from styles

  const toggleExpansion = (reviewId: string | number) => {
    setExpandedReviews(prevState => ({
      ...prevState,
      [reviewId]: !prevState[reviewId],
    }));
  };

  const handleLayout = (
    event: LayoutChangeEvent,
    reviewId: string | number,
  ) => {
    const { height } = event.nativeEvent.layout;
    const maxHeight = 3 * lineHeight; // Height for 3 lines

    setIsTruncated(prevState => ({
      ...prevState,
      [reviewId]: height > maxHeight, // Check if height exceeds 3 lines
    }));
  };

  const [state, setState] = useState({
    bottomLoading: false,
    refreshing: false,
    loader: false,
    saveLoader: false,
    applicantsLoader: false,
    confirmationModal: false,
    applicant: {},
    job: {},
  });
  const { loader, refreshing, bottomLoading } = state;

  const getReviewList = useCallback(
    async (page = 1, bottomLoader = false, refresh = false) => {
      if (bottomLoader) {
        setState((p: any) => ({ ...p, bottomLoading: true }));
      } else if (refresh) {
        setState((p: any) => ({ ...p, refreshing: true }));
      } else {
        setState((p: any) => ({ ...p, loader: true }));
      }
      try {
        const data: any = { page, limit: 5 };
        if (searchType === 'Seeker') {
          data.type = 'seeker';
        } else {
          data.type = 'employer';
        }
        const resp = await getApiData({
          endpoint: BaseSetting.endpoints.getFeatured,
          method: 'GET',
          data: data,
        });

        console.log('🚀 ~ resp:', resp?.data?.items);
        if (resp?.data && resp?.status) {
          const cData = featuredList.data;
          console.log('🚀 ~ cData:', cData);

          const list =
            !isEmpty(resp?.data?.items) && isArray(resp?.data?.items)
              ? resp?.data?.items
              : [];
          console.log('list ===>', list);
          const data = page > 1 ? [...cData, ...list] : list;
          setFeaturedList((p: any) => ({
            ...p,
            data: data,
            pagination: resp,
          })); // Update user details
        } else {
          setFeaturedList((p: any) => ({ ...p, data: [], pagination: {} })); // Update user details
          Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
        }
        setState((p: any) => ({
          ...p,
          loader: false,
          bottomLoading: false,
          refreshing: false,
        }));
      } catch (error) {
        setState((p: any) => ({
          ...p,
          loader: false,
          bottomLoading: false,
          refreshing: false,
        }));
        console.error('Error fetching list:', error);
        // Toast.show('Failed to fetch data.', Toast.SHORT);
      }
    },
    [isFocused, selectedTab],
  );


  useEffect(() => {
    getReviewList();
  }, [isFocused, selectedTab]);

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (
      isCloseToBottom &&
      featuredList?.pagination?.isMore &&
      !loader &&
      !bottomLoading
    ) {
      getReviewList(
        Number(featuredList?.pagination?.currentPage) + 1,
        true,
        false,
      );
    }
  };

  const onRefresh = React.useCallback(() => {
    if (!loader) {
      getReviewList(1, bottomLoading, refreshing);
    }
  }, [loader]);

  const listEndLoader = () => {
    if (!loader && bottomLoading) {
      return <ActivityIndicator color={BaseColors.primary} size={'small'} />;
    }
    return null;
  };
  const handleSave = useCallback(
    async (item: any) => {
      setSaveLoader(item?.id);
      try {
        let data: any = {};
        if (searchType === 'Seeker') {
          data = { userId: item?.id };
        } else {
          data = { jobId: item?.id };
        }
        const resp = await getApiData({
          endpoint:
            searchType === 'Seeker'
              ? BaseSetting.endpoints.seekerSave
              : BaseSetting.endpoints.saveCard,
          method: 'POST',
          data: data,
        });
        if (resp?.status) {
          setFeaturedList((prevList: any) => ({
            ...prevList,
            data: prevList?.data?.map((i: any) =>
              i.id === item?.id
                ? {
                  ...i,
                  isSaved: !i?.isSaved, // Revert the save state if error occurs
                  savedUser: i?.savedUser ? null : item?.savedUser, // Revert savedUser
                }
                : i,
            ),
          }));
        }
        setSaveLoader(false);
      } catch (e) {
        setSaveLoader(false);
        console.log('ERRR', e);
      }
    },
    [searchType],
  );

  const rendItem = (item: any, index: any) => {
    const reviewId = item.id || index;
    const isExpanded = expandedReviews[reviewId];
    const showSeeMore = isTruncated[reviewId];

    if (searchType === 'Seeker') {
      return (
        <SeekerCard
          item={item}
          onSave={handleSave}
          saveLoader={saveLoader}
          navigation={navigation}
          type="seeker"
        // bodyType="home"
        />
      );
    } else {
      return (
        <EmployarCard
          item={item}
          onSave={handleSave}
          saveLoader={saveLoader}
          navigation={navigation}
          navigateName={'JobApplicant'}
        // bodyType="home"
        />
      );
    }
    // </View>
  };

  return (
    <KeyboardAwareScrollView
      bounces={false}
      contentContainerStyle={styles.scrollContainer}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
      enableOnAndroid={false}>
      <View style={{ backgroundColor: 'white', flex: 1 }}>
        <Header
          leftIcon="back-arrow"
          title={`${'Featured ' + searchType}`}
          onLeftPress={() => {
            navigation.goBack();
          }}
        />
        <ScrollView
          onScroll={handleScroll}
          // scrollEventThrottle={0.5}
          style={{
            ...styles.mainView,
            // marginBottom: Dimensions.get('screen').height / 150,
          }}
          nestedScrollEnabled={true}
          contentContainerStyle={{ flexGrow: 1 }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[BaseColors.primary]} // Customize refresh indicator color
              tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
            />
          }
          showsVerticalScrollIndicator={false}>
          <AnimatedView>
            {loader ? (
              <View style={styles.centerMain}>
                <ActivityIndicator color={BaseColors.primary} size={'large'} />
              </View>
            ) : (
              <>
                <FlatList
                  data={data || []}
                  keyExtractor={(item: any) => `${item.id}+1`}
                  renderItem={({ item }) => rendItem(item)}
                  contentContainerStyle={{
                    //   marginTop: 30,
                    marginBottom: Dimensions.get('screen').height / 9,
                  }}
                  ListEmptyComponent={
                    <View style={styles.centerMain}>
                      <NoRecord title={'noReview'} />
                    </View>
                  }
                  style={{
                    ...styles.mainView,
                  }}
                  scrollEnabled={false} // Disable FlatList's scrolling
                  ListFooterComponent={listEndLoader} // Loader when loading next page.
                />
              </>
            )}
          </AnimatedView>
        </ScrollView>
      </View>
    </KeyboardAwareScrollView>
  );
}
