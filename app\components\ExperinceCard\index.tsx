import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import styles from './styles';
import {translate} from '@language/Translate';
import {isArray, isEmpty} from '@app/utils/lodashFactions';
import moment from 'moment';
import {BaseColors} from '@config/theme';
import EIcon from 'react-native-vector-icons/Entypo';
import AIcon from 'react-native-vector-icons/Feather';
import {getApiData} from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
// import Toast from 'react-native-simple-toast';
import authActions from '@redux/reducers/auth/actions';
import {useDispatch, useSelector} from 'react-redux';
import NoRecord from '@components/NoRecord';

interface Experience {
  id?: number;
  company: string;
  designation: string;
  startDate?: string;
  endDate?: string;
  setEditExperince?: any;
}

interface ExperienceCardProps {
  experienceList: Experience[];
  setExperienceList: any;
  reviewType: any;
  setPreExperince: any;
  setOpenBottomSheet: any;
  preExperince: any;
  refRBSheet: any;
  setEdit: any;
  edit: any;
  editExperince: any;
  setEditExperince: any;
  setEditCertificate: any;
  userDetails: any;
  setCheckSaveExperince: any;
  setCheckSaveCerti: any;
  setAddData: any;
}

const ExperienceCard: React.FC<ExperienceCardProps> = ({
  experienceList,
  setExperienceList,
  reviewType,
  setPreExperince,
  preExperince,
  setOpenBottomSheet,
  refRBSheet,
  setEdit,
  edit,
  editExperince,
  setEditExperince,
  setEditCertificate,
  userDetails,
  setCheckSaveExperince,
  setCheckSaveCerti,
  setAddData,
}) => {
  if (
    reviewType === 'reviewbyEmployer'
      ? isEmpty(userDetails?.workExperience) ||
        !isArray(userDetails?.workExperience)
      : isEmpty(preExperince) || !isArray(preExperince)
  ) {
    return (
      <>
        {reviewType === ('reviewbyEmployer' || 'review') ? (
          <View style={styles.card}>
            <Text style={styles?.titleTxtSty}>
              {translate('workExperince', '')}
            </Text>
            <NoRecord title={'noExperice'} />
          </View>
        ) : null}
      </>
    );
  }
  const {selectPosition, userData} = useSelector((state: any) => state.auth); // Use your RootState type

  const [loader, setLoader] = useState<any>(false);
  const [showAll, setShowAll] = useState(false);
  const dispatch = useDispatch();
  const [deletingIndex, setDeletingIndex] = useState<number | null>(null); // For individual deletion
  const [deletingAll, setDeletingAll] = useState<boolean>(false);
  const [truncatedStates, setTruncatedStates] = useState<{
    [key: number]: boolean;
  }>({}); // Track truncated state for each experience

  // Toggle function that only affects the clicked experience
  const toggleText = (index: number) => {
    setTruncatedStates(prev => ({
      ...prev,
      [index]: !prev[index], // Toggle the truncated state for the specific index
    }));
  };

  // For "delete all"
  const deleteFile = async (type: string, index?: any) => {
    if (type === 'all') {
      setDeletingAll(true); // Set deletingAll to true when deleting all files
    } else {
      setDeletingIndex(index); // Set the index of the certificate being deleted
    }
    let array = {};
    // If deleting a single file, send the specific file id
    if (index !== undefined) {
      const experince = preExperince[index];
      array = [experince.id]; // Wrap the id inside an array within 'array'
    }
    // If deleting all files, collect all ids in an array
    else if (type === 'all') {
      const ids = preExperince.map(cert => cert.id); // Collect all ids from the certificationList
      array = ids; // Wrap all ids inside 'array'
    }

    try {
      const url = BaseSetting.endpoints.deleteQualification;
      const resp = await getApiData({
        endpoint: url,
        method: 'POST',
        data: {array}, // Send array as the payload
      });

      console.log('🚀 ~ deleteFile ~ resp:', resp);

      if (resp?.status) {
        // Toast.show(resp?.message, Toast.BOTTOM);

        if (type === 'experince' && index !== undefined) {
          // Remove the specific file from the list after successful deletion
          const updatedFiles = preExperince.filter((_, i) => i !== index);
          setPreExperince(updatedFiles);
        }
        // If deleting all files
        else if (type === 'all') {
          setPreExperince([]); // Clear all files from the list
        }
      } else {
        console.log('🚀 ~ deleteFile ~ Failed to delete file');
        // Toast.show(resp?.message, Toast.BOTTOM);
      }
    } catch (err) {
      // Toast.show(err?.message || 'Something went wrong.', Toast.LONG);
    }
    // Reset loader states
    setDeletingIndex(null); // Reset deletingIndex after individual file deletion
    setDeletingAll(false); // Reset deletingAll after deleting all files
  };

  const visibleExperiences = showAll
    ? reviewType === 'reviewbyEmployer'
      ? userDetails?.workExperience
      : preExperince
    : reviewType === 'reviewbyEmployer'
    ? userDetails?.workExperience?.slice(0, 1)
    : preExperince?.slice(0, 2);

  return (
    <>
      <View>
        {visibleExperiences?.length > 0 && (
          <View style={styles.card}>
            <View
              style={{
                borderBottomWidth: 1,
                borderColor: BaseColors?.borderColor,
                marginBottom: 5,
              }}>
              <Text style={styles?.titleTxtSty}>
                {translate('workExperince', '')}
              </Text>
              {reviewType === 'reviewbyEmployer' ||
              reviewType === 'review' ? null : (
                <TouchableOpacity
                  onPress={() => deleteFile('all')} // Pass 'all' to delete all experinces
                  style={styles.removeFileButton}>
                  {deletingAll ? (
                    <ActivityIndicator
                      size="small"
                      color={BaseColors.textGrey}
                    />
                  ) : (
                    <EIcon name="cross" size={20} color={BaseColors.textGrey} />
                  )}
                </TouchableOpacity>
              )}
            </View>
            {visibleExperiences.map((experience, index) => {
              const isTruncated =
                truncatedStates[index] !== undefined
                  ? truncatedStates[index]
                  : true; // Default to true (truncated)

              const maxLength = 80; // Set the maximum length for truncated text
              const description = experience?.description || '';
              const isDescriptionLong = description.length > maxLength; // Check if description is long enough to truncate

              const visibleText =
                isTruncated && isDescriptionLong
                  ? description.slice(0, maxLength) + '...'
                  : description;
              return (
                <View key={experience.id || index} style={{...styles.cardRow}}>
                  {reviewType === 'reviewbyEmployer' ||
                  reviewType === 'review' ? null : (
                    <View style={styles?.iconViewSty}>
                      <TouchableOpacity
                        onPress={() => {
                          setEdit(true);
                          setAddData('Enter Experience');
                          setOpenBottomSheet(true);
                          setCheckSaveExperince(false);
                          setCheckSaveCerti(false);
                          setEditCertificate(false);
                          setEditExperince(experience);
                          refRBSheet.current?.open();
                        }} // Pass 'experince' and index for single file deletion
                        style={styles.editIconsty}>
                        <AIcon
                          name="edit-2"
                          size={14}
                          color={BaseColors.textGrey}
                        />
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => deleteFile('experince', index)} // Pass 'experince' and index for single file deletion
                        style={{...styles.removeFileButton, right: 5}}>
                        {deletingIndex === index ? (
                          <ActivityIndicator
                            size="small"
                            color={BaseColors.textGrey}
                          />
                        ) : (
                          <EIcon
                            name="cross"
                            size={20}
                            color={BaseColors.textGrey}
                          />
                        )}
                      </TouchableOpacity>
                    </View>
                  )}
                  <View style={{marginTop: reviewType === 'review' ? 0 : 5}}>
                    <Text numberOfLines={1} style={styles.companyNameSty}>
                      {experience.company}
                    </Text>
                    {/* <View
                    style={{
                      flexDirection: 'row',
                      // justifyContent: 'space-between',
                    }}>
                    <View style={{width: '51%'}}>
                      <Text
                        numberOfLines={3}
                        style={[styles.experinceCardDiscrpition]}>
                        {experience.designation}
                      </Text>
                    </View>
                    <View style={{flex: 1, alignContent: 'flex-end'}}>
                      <Text style={[styles.experinceCardDiscrpition]}>
                        {experience?.startDate
                          ? moment(experience?.startDate).format('DD MMM YYYY')
                          : 'N/A'}{' '}
                        -{' '}
                        {experience?.endDate
                          ? moment(experience?.endDate).format('DD MMM YYYY')
                          : translate('present', '')}
                      </Text>
                    </View>
                  </View> */}
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                      }}>
                      <Text
                        numberOfLines={3}
                        style={[
                          styles.experinceCardDiscrpition,
                          {
                            width: Dimensions.get('screen').width / 2.6,
                          },
                        ]}>
                        {experience.docTitle}
                      </Text>
                      <Text style={[styles.experinceCardDiscrpition]}>
                        {experience?.startDate
                          ? moment(experience?.startDate).format('M/D/YYYY')
                          : 'N/A'}{' '}
                        -{' '}
                        {experience?.endDate
                          ? moment(experience?.endDate).format('M/D/YYYY')
                          : translate('present', '')}
                      </Text>
                    </View>
                    <View
                      style={
                        {
                          // justifyContent: 'center',
                          // alignItems: 'flex-start',
                        }
                      }>
                      <>
                        {/* <View
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginTop: 10,
                          }}>
                          <View
                            style={{
                              height: 8,
                              width: 8,
                              borderRadius: 6,
                              backgroundColor: BaseColors.primary,
                            }}
                          />
                          <Text
                            style={[
                              styles.companyNameSty,
                              {fontSize: 14, marginLeft: 10},
                            ]}>
                            {translate('Title', '')}
                          </Text>
                        </View>
                        <Text style={[styles.discriptionSty]}>
                          {experience?.docTitle || '-'}
                        </Text> */}
                        {/* <View
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginTop: 10,
                          }}>
                          <View
                            style={{
                              height: 8,
                              width: 8,
                              borderRadius: 6,
                              backgroundColor: BaseColors.primary,
                            }}
                          />
                          <Text
                            style={[
                              styles.companyNameSty,
                              {fontSize: 14, marginLeft: 10},
                            ]}>
                            {translate('profileDiscription', '')}
                          </Text>
                        </View> */}
                        <View>
                          <Text style={[styles.discriptionSty]}>
                            {visibleText}
                          </Text>

                          {/* Show the See More button only if the description is long enough */}
                          {isDescriptionLong && (
                            <TouchableOpacity
                              onPress={() => toggleText(index)}
                              style={[
                                styles.seeMoreButton,
                                {
                                  alignItems: 'flex-start',
                                  marginHorizontal: 18,
                                },
                              ]}>
                              <Text style={styles.seeMoreText}>
                                {isTruncated
                                  ? translate('seeMore', '')
                                  : translate('seeLess', '')}
                              </Text>
                            </TouchableOpacity>
                          )}
                        </View>
                      </>
                    </View>
                  </View>
                  <View
                    style={{
                      borderBottomWidth: 0.7,
                      borderColor: BaseColors.borderColor,
                      marginTop: 10,
                    }}
                  />
                  {/* <View>
                <View style={styles.crossView}>
                  <View style={{marginBottom: 10}}>
                    <Text style={styles.titleStyle}>
                      {translate('company', '')}
                    </Text>
                    <Text numberOfLines={1} style={styles.cardTitle}>
                      {experience.company}
                    </Text>
                  </View>
                  {reviewType === 'review' ? null : (
                    <View style={styles?.iconViewSty}>
                      <TouchableOpacity
                        onPress={() => {
                          setEdit(true);
                          setOpenBottomSheet(true);
                          setEditCertificate(false);
                          setEditExperince(experience);
                          refRBSheet.current?.open();
                        }} // Pass 'experince' and index for single file deletion
                        style={styles.editIconsty}>
                        <AIcon
                          name="edit-2"
                          size={14}
                          color={BaseColors.textGrey}
                        />
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => deleteFile('experince', index)} // Pass 'experince' and index for single file deletion
                        style={styles.removeFileButton}>
                        {deletingIndex === index ? (
                          <ActivityIndicator
                            size="small"
                            color={BaseColors.textGrey}
                          />
                        ) : (
                          <EIcon
                            name="cross"
                            size={20}
                            color={BaseColors.textGrey}
                          />
                        )}
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
                <View>
                  <Text style={styles.titleStyle}>
                    {translate('designation', '')}
                  </Text>
                  <Text numberOfLines={3} style={styles.cardTitle}>
                    {experience.designation}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginTop: 10,
                  }}>
                  <View style={{flexDirection: 'column'}}>
                    <Text style={styles.titleStyle}>
                      {translate('workingSince', '')}
                    </Text>
                    <Text style={styles.cardTitle}>
                      {experience?.startDate
                        ? moment(experience?.startDate).format('DD MMM YYYY')
                        : 'N/A'}
                    </Text>
                  </View>
                  <View style={{flexDirection: 'column'}}>
                    <Text style={styles.titleStyle}>
                      {translate('working', '')}
                    </Text>
                    <Text style={styles.cardTitle}>
                      {experience?.endDate
                        ? moment(experience?.endDate).format('DD MMM YYYY')
                        : translate('present', '')}
                    </Text>
                  </View>
                </View>
              </View> */}
                </View>
              );
            })}

            {reviewType === 'reviewbyEmployer'
              ? userDetails?.workExperience?.length > 2
              : preExperince.length > 2 && (
                  <TouchableOpacity
                    onPress={() => setShowAll(!showAll)}
                    style={styles.seeMoreButton}>
                    <Text style={styles.seeMoreText}>
                      {showAll ? translate('Less', '') : translate('More', '')}
                    </Text>
                  </TouchableOpacity>
                )}
          </View>
        )}
      </View>
    </>
  );
};

export default ExperienceCard;
