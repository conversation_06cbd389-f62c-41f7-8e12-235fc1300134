import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {backgroundColor: BaseColors.white, flex: 1},
  mainView: {
    marginHorizontal: 15,
    marginBottom: 20,
  },
  settings: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    paddingRight: 25,
    backgroundColor: BaseColors.white,
    borderRadius: 10,
  },
  title: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 21,
    color: BaseColors.textBlack,
    paddingBottom: 15,
    textTransform: 'capitalize',
  },
  subCard: {
    // padding: 15,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    // alignItems: 'center',
    textAlign: 'center',
    alignItems: 'flex-end',
    paddingTop: 15,
    paddingBottom: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: BaseColors.lightGrey,
  },
  subCardIcon: {
    width: 40,
  },
  titleText: {
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
    fontSize: 16,
    // textTransform: 'capitalize',

  },
  btnViewSty: {
    marginHorizontal: 15,
    marginVertical: 15,
  },
});
