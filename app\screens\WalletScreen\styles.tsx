import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {Dimensions} from 'react-native';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    // justifyContent: 'center',
    flexGrow: 1,
    backgroundColor: '#fff',
    // paddingHorizontal: 15,
  },
  centerMain: {
    // flex: 1,
    justifyContent: 'center',
    marginTop: Dimensions.get('screen').height / 4,
    alignItems: 'center',
    width: '100%',
  },

  containerSub: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    // padding: 10,
    // width: '95%',
    // flex: 1,
    justifyContent: 'flex-end',
    marginBottom: 10,
    marginHorizontal: 15,
  },
  tab: {
    flex: 1,
    paddingVertical: 0,
    borderRadius: 10,
    borderWidth: 1,
    // borderColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
    // width: 102,
    height: 43,
    // maxWidth: 150,
    borderColor: BaseColors.primary,
    backgroundColor: '#fff',
    textAlign: 'center',
  },
  active: {
    backgroundColor: BaseColors.primary,
  },
  tabText: {
    // fontSize: 16,
    fontSize: 14,
    color: BaseColors.white,
    fontFamily: FontFamily?.OpenSansRegular,
    textTransform: 'capitalize',
  },
  recent: {
    fontSize: 18,
    flex: 1,
    color: BaseColors.blackColor,
    fontFamily: FontFamily.OpenSansMedium,
  },
  seeAll: {
    fontSize: 16,
    // flex: 1,
    textAlign: 'right',
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansMedium,
    borderBottomWidth: 0.5,
    borderBottomColor: BaseColors.primary,
    lineHeight: 22,
  },

  scrollContainer: {
    flexGrow: 1,
    backgroundColor: BaseColors.white,
    borderRadius: 12,
    // paddingVertical: 20,
    marginBottom: 20,
  },
  date: {
    fontSize: 12,
    color: BaseColors.textGrey,
    alignSelf: 'flex-end',
    fontFamily: FontFamily.OpenSansMedium,
  },
  renderView: {
    paddingVertical: 7,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  id: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansMedium,
  },
  name: {
    fontSize: 16,
    flex: 1,
    color: BaseColors.blackColor,
    fontFamily: FontFamily.OpenSansMedium,
  },
  title: {
    fontSize: 14,
    flex: 1,
    color: BaseColors.lightGrey,
    fontFamily: FontFamily.OpenSansMedium,
  },
  titleTxtSty: {
    fontSize: 14,
    flex: 1,
    color: BaseColors.blackColor,
    fontFamily: FontFamily.OpenSansMedium,
  },
  amount: {
    fontSize: 14,
    color: BaseColors.blackColor,
    fontFamily: FontFamily.OpenSansMedium,
  },
  separator: {
    borderWidth: 0.5,
    borderColor: BaseColors.borderColor,
  },
  titleText: {
    fontSize: 20,
    color: BaseColors.blackColor,
  },
  clipBoard: {
    // padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginLeft: 5,
  },
  descriptionText: {
    fontSize: 20,
    color: BaseColors.blackColor,
  },
  thirdRow: {
    borderWidth: 1,
    padding: 8,
    marginVertical: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: BaseColors.primary + 20,
    borderColor: BaseColors.primary + 20,
  },
  warningText: {
    fontSize: 14,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansBold,
    marginTop: 4,
    textTransform: 'capitalize',
  },
  underline: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    height: 1, // Adjust this value to change the thickness of the underline
    backgroundColor: BaseColors.primary, // Adjust the color as needed
  },
  completeNowBtnStyle: {
    alignItems: 'center',
    position: 'relative',
  },
});
