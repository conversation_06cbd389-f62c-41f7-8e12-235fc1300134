import { StyleSheet } from 'react-native';
import { Dimensions, Platform } from 'react-native';
import { BaseColors } from '../../config/theme';
import { FontFamily } from '@config/typography';
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingVertical: 10,
  },
  stepItem: {
    width: 80,
    alignItems: 'center',
    position: 'relative',
  },
  editIconSty: {
    // right: 40,
    width: 30,
    height: 30,
    // borderWidth: 1,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColors.primary,
  },
  stepCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#CACACA',
    lineHeight: 30,
    color: '#CACACA',
    backgroundColor: 'transparent', // Default background
    alignSelf: 'center',
    justifyContent: 'center',
  },
  activeCircle: {
    borderColor: BaseColors.primary,
    color: BaseColors.primary,
  },
  completedCircle: {
    backgroundColor: IOS ? BaseColors.primary : BaseColors.primary, // Blue background for completed steps
    color: IOS ? BaseColors.primary : '#FFFFFF', // White text color for completed steps
    borderColor: BaseColors.primary,
  },
  stepLabel: {
    fontSize: 13,
    color: BaseColors.textGrey,
    marginTop: IOS ? 10 : 5,
    fontFamily: FontFamily.OpenSansRegular,
  },
  activeLabel: {
    color: BaseColors.primary,
    fontSize: 12,
    fontFamily: FontFamily.OpenSansBold,
  },
  completedLabel: {
    color: BaseColors.primary, // Blue label for completed steps
    fontSize: 14,
    fontWeight: '600',
  },
  stepLine: {
    width: 70,
    height: 2,
    backgroundColor: '#CACACA',
    position: 'absolute',
    left: 61,
    top: 27,
  },
  componentContainer: {
    flex: 1,
    marginTop: 10,
    marginHorizontal: 20,
  },
  setUpViewSty: {
    // marginTop: 20,
    marginHorizontal: 20,
  },
  setUpSty: {
    borderWidth: 1,
    borderColor: '#CACACA',
    borderRadius: 10,
    paddingVertical: 10,
  },
  activetxtSty: {
    textAlign: 'center',
    color: BaseColors.primary,
  },
  completedtxtSty: {
    textAlign: 'center',
    color: BaseColors.white,
  },
  stepTxtSty: {
    textAlign: 'center',
    color: BaseColors.textGrey,
  },
});
