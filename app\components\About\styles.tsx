import {Dimensions, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const {width, height} = Dimensions.get('window');
const headerViewHeight = 160;
const bottomViewHeight = 40;
export default StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    // alignItems: 'center',
    paddingVertical: 20,
  },
  photoContainer: {
    marginBottom: 10,
  },
  dashedBorder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: BaseColors.secondaryBule, // Your desired border color
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 70,
    height: 70,
    borderRadius: 35,
  },
  text: {
    fontSize: 21,
    color: BaseColors.inputColor, // Gray color
    textAlign: 'center',
    paddingHorizontal: 30,
    fontFamily: FontFamily.OpenSansRegular,
  },
  skillViewSty: {
    padding: 15,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    marginVertical: 5,
    borderRadius: 10,
  },
  skillTxtSty: {
    fontSize: 21,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  mVertical: {
    marginVertical: 5,
  },
  suggestionsTxtSty: {
    fontSize: 14,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  addskillTxtSty: {
    fontSize: 14,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },

  aboutTxtSty: {
    fontSize: 21,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  selfTxtSty: {
    fontSize: 16,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansBold,
  },
  optionalTxt: {
    fontSize: 14,
    color: BaseColors.lightGrey,
    fontFamily: FontFamily?.OpenSansRegular,
    paddingHorizontal: 5,
    fontStyle: 'italic', // Add this line
  },
  marginTopSty: {
    marginTop: 15,
  },
  brifTxtSty: {
    fontSize: 14,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  nextBtnSty: {
    margin: 20,
  },
  mViewfive: {
    marginVertical: 5,
  },
  addSkillTxtSty: {
    fontSize: 21,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  aboutViewSty: {
    marginTop: 20,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  brifViewSty: {
    marginTop: 1,
  },
  skillSty: {
    borderWidth: 1,
    borderColor: 'transparent',
    backgroundColor: BaseColors.skillColor,
    padding: 5,
    borderRadius: 10,
  },
  skillsNameStyle: {
    borderWidth: 1,
    borderColor: 'transparent',
    backgroundColor: BaseColors.skillColor,
    padding: 5,
    borderRadius: 10,
  },
  tagText: {
    color: BaseColors.primary,
    marginRight: 5,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 13,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BaseColors.whiteColor,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: BaseColors.primary,
    margin: 5,
    justifyContent: 'center',
    textAlign: 'center',
    alignSelf: 'center',
  },
  item_wrap: {
    position: 'relative',
    paddingLeft: 15,
    paddingTop: 15,
    backgroundColor: '#ffff',
  },
  item: {
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#f5faff',
    borderRadius: 4,
    padding: 3,
  },
  item_clear_wrap: {
    position: 'absolute',
    left: 10,
    top: 10,
    width: 20,
    height: 20,
    zIndex: 999,
  },
  item_clear: {
    width: 20,
    height: 20,
  },
  item_moved: {
    opacity: 0.95,
    borderRadius: 4,
  },
  item_icon_swipe: {
    width: 50,
    height: 50,
    backgroundColor: '#fff',
    borderRadius: 50 * 0.5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  item_icon: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  item_text_swipe: {
    // backgroundColor: '#fff',
    // width: 56,
    // height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  item_text: {
    color: BaseColors.primary,
    fontSize: 14,
    fontFamily: FontFamily.OpenSansMedium,
  },
  header: {
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomColor: '#2ecc71',
    borderBottomWidth: 2,
  },
  header_title: {
    color: '#333',
    fontSize: 24,
    fontWeight: 'bold',
  },
  aheader: {
    height: headerViewHeight,
    flexDirection: 'row',
    borderBottomColor: '#2ecc71',
    borderBottomWidth: 2,
    zIndex: 100,
    backgroundColor: '#fff',
  },
  aheader_img: {
    width: headerViewHeight * 0.6,
    height: headerViewHeight * 0.6,
    resizeMode: 'cover',
    borderRadius: headerViewHeight * 0.3,
    marginLeft: 16,
    marginTop: 10,
  },
  aheader_context: {
    marginLeft: 8,
    height: headerViewHeight * 0.4,
    marginTop: 10,
  },
  aheader_title: {
    color: '#333',
    fontSize: 20,
    marginBottom: 10,
    fontWeight: 'bold',
  },
  aheader_desc: {
    color: '#444',
    fontSize: 16,
    width: width - headerViewHeight * 0.6 - 32,
  },
  abottom: {
    justifyContent: 'center',
    alignItems: 'center',
    height: bottomViewHeight,
    backgroundColor: '#fff',
    zIndex: 100,
    borderTopColor: '#2ecc71',
    borderTopWidth: 2,
  },
  abottom_desc: {
    color: '#333',
    fontSize: 20,
    fontWeight: 'bold',
  },
});
