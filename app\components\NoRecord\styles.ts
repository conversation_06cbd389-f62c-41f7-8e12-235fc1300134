import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { Dimensions, StyleSheet } from 'react-native';

export default StyleSheet.create({
  main: {
    flex: 1,
    paddingHorizontal: 14,
    marginBottom: 0,
    justifyContent: 'center',
  },
  mainView: {
    justifyContent: 'center',
    alignItems: 'center',

    height: Dimensions.get('screen').height / 6,
    // width: Dimensions.get('screen').width / 1.6,
  },
  chatMain: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loader: {
    height: 175,
    width: '100%',
    // flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    borderRadius: 200,
  },
  emptyTextChat: {
    fontSize: 20,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansBold,
    textAlign: 'center',
    marginTop: 15,
  },
  emptyText: {
    fontSize: 18,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansBold,
    textAlign: 'center',
  },
  descriptionText: {
    fontSize: 14,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
    textAlign: 'center',
    marginTop: 5,
  },
  chatIconMain: {
    height: Dimensions.get('screen').height / 6,
    width: Dimensions.get('screen').width / 1.6,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 100,
    borderColor: BaseColors.activeTab,
  },
});
