/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import moment from 'moment';
import {
  Text,
  StyleSheet,
  View,
  TextInput,
  Platform,
  TouchableOpacity,
  Keyboard,
  Dimensions,
  useColorScheme,
} from 'react-native';
// import {useSharedValue, withSpring} from 'react-native-reanimated';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Aicons from 'react-native-vector-icons/AntDesign';
// import Toast from 'react-native-simple-toast';
import CountryPicker from 'react-native-country-picker-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {FontFamily} from '@config/typography';
import {BaseColors} from '@config/theme';
import {CustomIcon} from '@config/LoadIcons';
import {has, isEmpty, isUndefined} from '@app/utils/lodashFactions';
/**
 *
 * Component for TextInput
 * @module CInput
 *
 */
function CInput(props: any, ref: any) {
  const {
    title = '',
    titleSty = {},
    titleLines = 1,
    placeholderText = '',
    onSubmit = () => {},
    onChange = () => {},
    value = '',
    secureText = false,
    showError = false,
    keyBoardType = 'default',
    errorText = '',
    returnKeyType,
    mandatory = false,
    textArea = false,
    Date = false,
    onDateChange = () => {},
    DateError = false,
    minDate,
    maxDate,
    editable = true,
    FinIconShow = false,
    FinIconPress = () => {},
    placeholderStyle = {},
    style = {},
    textAreaHeight = false,
    iseditable = true,
    searchIcon = false,
    isSuffix = false,
    maxLength = 1000000000000000,
    onFocus = () => {},
    onInputBlur = () => {},
    dateType = 'date',
    selectedDate = undefined,
    phoneNumber = false,
    callingCode = '1',
    countryCode = '',
    onCountryChange = () => {},
    maxChar = '',
    showDisableToast = true,
    contentStyle = {},
    datetimemodal = '',
    height = '',
    Time = false,
    searchButton = false,
    searchViewSty = {},
    textStyle = {},
    tagType = false,
    onKeyPress = () => {},
    // disabledDate,
    ...rest
  } = props;

  const IOS = Platform.OS === 'ios';
  const [focused, setFocused] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [show, setShow] = useState(false);

  const offset = {value: 0};

  const disabled = has(props, 'editable') && props.editable === false;

  const handlePress = () => {
    if (disabled) {
      // Toast.show('This Field Is Not Editable', Toast.SHORT);
    }
  };
  const handleTextChange = (text: any) => {
    if (maxChar && text.length > maxChar) {
      return;
    }

    onChange(text);
  };

  const myTheme = {
    primaryColor: '#000',
    primaryColorVariant: '#000',
    onBackgroundTextColor: !isVisible && BaseColors.black,
    backgroundColor: BaseColors.white,
    filterPlaceholderTextColor: BaseColors.inputColor,
    flagSize: 20,
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
  };
  const [selectedCallingCode, setSelectedCallingCode] = useState(callingCode); // Track the calling code
  const [selectedCountryCode, setSelectedCountryCode] = useState(
    countryCode || 'US',
  ); // Track the country code
  const scheme = useColorScheme();

  const onSelect = (country: any) => {
    setSelectedCallingCode(country.callingCode[0]); // Set the calling code on country select
    setSelectedCountryCode(country.cca2); // Set the country code
    onCountryChange(country); // Call the provided callback
  };

  useEffect(() => {
    setSelectedCallingCode(callingCode);
  }, [callingCode]);

  return (
    <>
      <View style={{...contentStyle}}>
        {title ? (
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text
              numberOfLines={titleLines || 1}
              style={[
                {
                  color: BaseColors.inputColor,
                },
                styles.titleTxt,
                titleSty,
              ]}>
              {title}
              <Text
                style={{
                  fontSize: 15,
                  color: BaseColors.inputColor,
                }}>
                {mandatory ? '*' : ''}
              </Text>
            </Text>
            {FinIconShow ? (
              <CustomIcon
                name="inform"
                color={BaseColors.white}
                style={{marginLeft: 5, paddingBottom: 2}}
                onPress={FinIconPress}
                size={12}
              />
            ) : null}
          </View>
        ) : null}
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            if (Date && !iseditable) {
              return null;
            } else if (Date) {
              setFocused(false);
              Keyboard.dismiss();
              setShow(true);
            } else if (showDisableToast) {
              handlePress();
            }
          }}
          style={{
            backgroundColor:
              searchButton && tagType
                ? '#f5faff'
                : searchButton
                ? '#ffff'
                : !iseditable
                ? BaseColors.lightGreyColor
                : showError
                ? '#FFF2F1'
                : '#f5faff',
            borderRadius: searchButton ? 10 : 15,
            borderColor: showError
              ? '#FFF2F1'
              : searchButton
              ? BaseColors.borderColor
              : 'transparent',
            borderWidth: 1,
            marginTop: textArea && maxChar !== '' ? 2 : 2,
            zIndex: 9,
            marginBottom: 3,
            opacity: !iseditable ? 0.8 : 1,
            ...style,
          }}>
          <View style={{flexDirection: 'row'}}>
            {phoneNumber && (
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  if (iseditable === true) {
                    setIsVisible(true);
                  } else {
                    null;
                  }
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingLeft: 10,
                }}>
                <CountryPicker
                  theme={myTheme}
                  containerButtonStyle={{
                    alignItems: 'center',
                    position: 'relative',
                  }}
                  onOpen={() => {
                    if (iseditable === true) {
                      setIsVisible(true);
                    } else {
                      null;
                    }
                  }}
                  onClose={() => {
                    setIsVisible(false);
                  }}
                  onSelect={onSelect}
                  {...{
                    countryCode: countryCode || selectedCountryCode, // Use selected country code
                    withCallingCode: true,
                    withFilter: true,
                    withAlphaFilter: true,
                    modalProps: {
                      visible: isVisible,
                    },
                  }}
                />

                <Text
                  style={{
                    color: showError
                      ? BaseColors.errorUpdatetxt
                      : BaseColors.primary,
                    fontFamily: FontFamily.OpenSansRegular,
                    paddingBottom: IOS ? 0 : 4,
                  }}>{`+${selectedCallingCode}`}</Text>

                <CustomIcon
                  name="ArrowUp" // Change to any icon name
                  size={20}
                  color={
                    showError ? BaseColors.errorUpdatetxt : BaseColors.primary
                  }
                  style={{
                    marginRight: 10,
                    paddingRight: 10,
                    transform: [{rotate: '180deg'}], // Rotate the icon
                  }}
                />

                <View
                  style={{
                    height: 20,
                    borderRightColor: showError
                      ? BaseColors.errorUpdatetxt
                      : BaseColors.primary,
                    borderRightWidth: 1,
                    marginHorizontal: 5,
                  }}
                />
              </TouchableOpacity>
            )}

            {Date ? (
              <View
                style={{
                  ...styles.datePickView,
                  borderColor: DateError ? 'transparent' : BaseColors.white,
                  borderWidth: DateError ? 1 : 0,
                  height: textAreaHeight
                    ? 110
                    : textArea && !textAreaHeight
                    ? 130
                    : 50,
                  // backgroundColor: DateError ? '#FFF2F1' : '#f5faff',
                }}>
                {Time ? (
                  <Aicons
                    style={{position: 'absolute', right: 20}}
                    name="clockcircleo"
                    color={BaseColors.primary}
                    size={20}
                  />
                ) : (
                  <Ionicons
                    style={{position: 'absolute', right: 20}}
                    name="calendar"
                    color={
                      DateError ? BaseColors.errorUpdatetxt : BaseColors.primary
                    }
                    size={20}
                  />
                )}
                {show ? (
                  <DateTimePickerModal
                    themeVariant={scheme === 'dark' ? 'light' : 'light'}
                    ref={ref}
                    display={
                      dateType === 'time'
                        ? 'spinner'
                        : IOS
                        ? 'inline'
                        : 'calendar'
                    }
                    isDarkModeEnabled={false}
                    buttonTextColorIOS={BaseColors.primary}
                    accentColor={BaseColors.primary}
                    // Additional styling for elements can be done by targeting iOS specific styles
                    // pickerContainerStyleIOS={{
                    //   backgroundColor: "transparanet", // Set background color to white
                    // }}
                    pickerStyleIOS={{
                      backgroundColor: '#fff',
                      alignItems: 'center',
                    }}
                    onConfirm={date => {
                      setShow(false);
                      onDateChange(moment(date));
                    }}
                    isVisible={show}
                    onCancel={() => setShow(false)}
                    minimumDate={minDate}
                    maximumDate={maxDate}
                    date={moment(selectedDate).toDate()}
                    textColor={BaseColors.black}
                    mode={dateType === 'time' ? 'time' : 'date' || 'date'}
                    confirmTextIOS="OK"
                  />
                ) : (
                  <TouchableOpacity
                    style={styles.dateTxt}
                    activeOpacity={0.7}
                    onPress={() => {
                      if (Date && !iseditable) {
                        return null;
                      } else {
                        setShow(true);
                        // offset.value = withSpring(!show ? 10 : 0);
                      }
                    }}>
                    <Text
                      style={{
                        paddingHorizontal: 10,
                        paddingVertical: 5,
                        color: disabled
                          ? BaseColors.textSecondary
                          : !selectedDate || isEmpty(selectedDate)
                          ? DateError
                            ? BaseColors.errorUpdatetxt
                            : BaseColors.dividerColor
                          : BaseColors.primary,
                        fontFamily: FontFamily.OpenSansRegular,
                        ...textStyle,
                      }}>
                      {!isUndefined(selectedDate)
                        ? dateType === 'time'
                          ? moment(selectedDate).format('hh:mm A')
                          : moment(selectedDate).format('M/D/YYYY')
                        : datetimemodal}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ) : searchButton ? (
              <>
                <View
                  style={{
                    alignSelf: 'center',
                    left: tagType ? 310 : 10,
                    // paddingHorizontal: 10,
                    position: tagType ? 'absolute' : 'relative',
                    ...searchViewSty,
                  }}>
                  <CustomIcon
                    name="Search"
                    size={18}
                    color={BaseColors.inputColor}
                  />
                </View>
                <TextInput
                  editable={iseditable}
                  {...rest}
                  ref={ref}
                  autoCorrect={false}
                  onFocus={() => {
                    setFocused(true);
                    onFocus ? onFocus() : null;
                    // offset.value = withSpring(!focused ? 10 : 0);
                  }}
                  multiline={textArea ? true : false}
                  numberOfLines={textArea ? 7 : 1}
                  value={value}
                  placeholder={placeholderText}
                  placeholderTextColor={
                    showError
                      ? BaseColors.errorUpdatetxt
                      : BaseColors.inputColor
                  }
                  onSubmitEditing={onSubmit}
                  onChangeText={handleTextChange}
                  secureTextEntry={secureText}
                  keyboardType={keyBoardType}
                  returnKeyType={returnKeyType}
                  maxLength={maxChar ? maxChar : maxLength}
                  style={{
                    fontSize: Dimensions.get('screen').width * 0.04,
                    ...placeholderStyle,
                    width: '100%',
                    // backgroundColor: 'red',
                    marginTop: textArea ? 10 : 0,
                    paddingLeft: 14,
                    fontFamily: FontFamily.OpenSansRegular,
                    color: showError
                      ? BaseColors.errorUpdatetxt
                      : BaseColors.inputColor,
                    // padding: IOS ? 0 : 0,
                    borderLeftColor: showError ? '#d62828' : '#D5DBDF',
                    textAlignVertical: textArea ? 'top' : 'center',
                    height: height
                      ? height
                      : textAreaHeight
                      ? 110
                      : textArea && !textAreaHeight
                      ? 130
                      : 44,

                    // paddingRight: searchIcon || isSuffix ? 38 : 7,
                    // paddingBottom: IOS ? 0 : 4,
                  }}
                  onBlur={() => {
                    setFocused(false);
                    onInputBlur ? onInputBlur() : null;
                    // offset.value = withSpring(focused ? 10 : 0);
                  }}
                />
              </>
            ) : (
              <TextInput
                editable={iseditable}
                {...rest}
                ref={ref}
                autoCorrect={false}
                onFocus={() => {
                  setFocused(true);
                  onFocus ? onFocus() : null;
                  // offset.value = withSpring(!focused ? 10 : 0);
                }}
                multiline={textArea ? true : false}
                numberOfLines={textArea ? 7 : 1}
                value={value}
                placeholder={placeholderText}
                placeholderTextColor={
                  showError
                    ? BaseColors.errorUpdatetxt
                    : BaseColors.dividerColor
                }
                onSubmitEditing={onSubmit}
                onChangeText={handleTextChange}
                secureTextEntry={secureText}
                keyboardType={keyBoardType}
                returnKeyType={returnKeyType}
                maxLength={maxChar ? maxChar : maxLength}
                style={{
                  width: '100%',
                  ...placeholderStyle,
                  marginTop: textArea ? 10 : 0,
                  fontSize: 14,
                  paddingLeft: 14,
                  fontFamily: FontFamily.OpenSansRegular,
                  color: showError
                    ? BaseColors.errorUpdatetxt
                    : BaseColors.primary,
                  padding: IOS ? 0 : 0,
                  borderLeftColor: showError ? '#d62828' : '#D5DBDF',
                  textAlignVertical: textArea ? 'top' : 'center',
                  height: height
                    ? height
                    : textAreaHeight
                    ? 110
                    : textArea && !textAreaHeight
                    ? 130
                    : 50,
                  paddingRight: searchIcon || isSuffix ? 38 : 7,
                  paddingBottom: IOS ? 0 : textArea ? 10 : 4,
                  paddingTop: textArea ? 5 : 0,
                }}
                onKeyPress={({nativeEvent}) => {
                  onKeyPress(nativeEvent);
                }}
                onBlur={() => {
                  setFocused(false);
                  onInputBlur ? onInputBlur() : null;
                  // offset.value = withSpring(focused ? 10 : 0);
                }}
              />
            )}
          </View>
        </TouchableOpacity>

        {showError || DateError ? (
          <Text
            style={[
              styles.errorTxt,
              {paddingBottom: DateError ? 0 : IOS ? 0 : 5},
            ]}>
            {errorText}
          </Text>
        ) : null}
      </View>
    </>
  );
}
const IOS = Platform.OS === 'ios';
const styles = StyleSheet.create({
  titleTxt: {
    paddingBottom: 5,
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors?.lightTxtColor,
  },
  datePickView: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginLeft: 5,
  },
  dateTxt: {
    flex: 1,
    textAlign: 'left',
    paddingVertical: 2,
  },
  errorTxt: {
    fontSize: 13,
    color: '#d62828',
    paddingBottom: 5,
    paddingTop: IOS ? 5 : 0,
    marginHorizontal: 4,
    fontFamily: FontFamily.OpenSansRegular,
  },
});

export default React.forwardRef(CInput);
