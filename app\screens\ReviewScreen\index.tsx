import React, {useState} from 'react';
import {View} from 'react-native';
import styles from './styles';
import {translate} from '@language/Translate';
import Button from '@components/UI/Button';
import EmployeeRating from '@components/EmployeeRating';
import Header from '@components/Header';
import Toast from 'react-native-simple-toast';
import BaseSetting from '@config/setting';
import {getApiData} from '@app/utils/apiHelper';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {updateReview} from '@app/utils/CommonFunction';
import authActions from '@redux/reducers/auth/actions';
import {useAppDispatch, useRedux} from '@components/UseRedux';

export default function ReviewScreen({navigation, route}: any) {
  const {useAppSelector} = useRedux();
  const jobDetail = route?.params?.jobID;
  const [text, setText] = useState<any>('');
  const [loader, setLoader] = useState<boolean>(false);
  const [rating, setRating] = useState<boolean>(false);
  const {userData} = useAppSelector((s: any) => s?.auth);
  const isEmployer = jobDetail?.userData?.id === userData?.id;
 
  const firstName = isEmployer ? jobDetail?.approvedApplicant?.firstName : jobDetail?.userData?.firstName;
  const lastName = isEmployer ? jobDetail?.approvedApplicant?.lastName : jobDetail?.userData?.lastName;

  const dispatch = useAppDispatch();

  const handlePost = async () => {
    setLoader(true);
    // Build the newObj with a dynamic description field
    const newObj: any = {
      jobId: jobDetail?.id || undefined,
      rating: rating || undefined,
      description: text || undefined,
    };
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.addRating,
        method: 'POST',
        data: newObj,
      });
      if (res?.status === true) {
        // setLoader(false);
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);

        const {status, badgeInfo} = await updateReview(
          'leave_review',
          userData.id,
        );

        if (status) {
          if (badgeInfo) {
            const updatedUserData = {
              ...userData, // Keep all other properties
              badgeInfo, // Update badgeInfo
            };

            dispatch(authActions.setUserData(updatedUserData));
            console.log(
              'User data updated successfully with new badgeInfo:',
              badgeInfo,
            );
          }
        } else {
          console.log('Update failed');
        }

        setLoader(false);
        navigation.goBack();
      } else {
        setLoader(false);
        Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      }
      setLoader(false);
    } catch (err) {
      setLoader(false);
      Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };

  return (
    <KeyboardAwareScrollView
      bounces={false}
      contentContainerStyle={styles.scrollContainer}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
      enableOnAndroid={false}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        leftIcon="back-arrow"
        title="Review"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <View style={styles.mainView}>
        <View style={styles.content}>
          <EmployeeRating
            setText={setText}
            text={text}
            setRating={setRating}
            rating={rating}
            firstName={firstName}
            lastName={lastName}
          />
        </View>
        <View style={styles.buttonContainer}>
          <Button
            type="text"
            loading={loader}
            onPress={handlePost}
            disable={loader}>
            {translate('Submitreview', '')}
          </Button>
        </View>
      </View>
    </KeyboardAwareScrollView>
  );
}
