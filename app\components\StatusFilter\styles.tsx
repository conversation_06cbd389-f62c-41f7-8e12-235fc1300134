import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { Dimensions, Platform } from 'react-native';
import { StyleSheet } from 'react-native';
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  mainCContain: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
  },
  container: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    // padding: 10,
    // width: '95%',
    flex: 1,
    justifyContent: 'flex-end',
  },
  tab: {
    flex: 1,
    paddingVertical: 0,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
    // width: 102,
    height: 43,
    maxWidth: 150,
  },
  activeTab: {
    borderColor: BaseColors.primary,
    backgroundColor: '#fff',
  },
  savedTab: {
    backgroundColor: BaseColors.saveButton,
  },
  completedTab: {
    backgroundColor: '#d8f0dc',
    borderWidth: 2,
  },
  tabText: {
    // fontSize: 16,
    fontSize: Dimensions.get('screen').width / 27,
    color: BaseColors.white,
    fontFamily: FontFamily?.OpenSansRegular,
    paddingBottom: IOS ? 0 : 5,
  },
  activeTabText: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
  },
  activePendingTXtsty: {
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.filterBorderColor,
    paddingBottom: IOS ? 0 : 5,
  },
  pendingViewSty: {
    borderWidth: 1,
    borderColor: BaseColors.filterBorderColor,
  },
  pendingActiveSTy: {
    borderWidth: 2,
    borderColor: BaseColors?.primary,
  },
  pendingTxtSTy: {
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.primary,
    paddingBottom: IOS ? 0 : 5,
  },
  savedActiveSty: {
    backgroundColor: BaseColors.primary,
  },
  completedActiveView: {
    backgroundColor: BaseColors.completedColor,
  },
  iconView: {
    padding: 5,
    // borderWidth: 1,
    // borderColor: BaseColors.bordrColor,
    // alignSelf: 'center',
    // alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    width: 40,
    height: 43,
    zIndex: 999,
    marginLeft: 5,
    // marginLeft: 10,
    // flex: 0.2, // Take 20% of the parent width
  },
});
