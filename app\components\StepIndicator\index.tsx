import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import React from 'react';
import Icons from 'react-native-vector-icons/Ionicons';
import EIcons from 'react-native-vector-icons/Entypo';

import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { translate } from '@language/Translate';
import { useSelector } from 'react-redux';

interface StepIndicatorProps {
  activeStep: number;
  steps: string[];
  goToStep: (step: number) => void;
  IOS: boolean;
  item: any;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  activeStep,
  steps,
  goToStep,
  IOS,
  item,
}) => {
  const { userData } = useSelector((s: any) => s.auth);

  const isSeeker = item?.userId !== userData?.id;

  console.log('item?.userId ===>', item?.userId, userData?.id);

  const getCircleStyle = (stepNumber: number) => {
    const status = item?.approvedApplicant?.status && !isSeeker
      ? item?.approvedApplicant?.status
      : item?.isApplied?.status;

    // Initialize default styles
    const circleStyle = [styles.stepCircle];
    const textStyle = [styles.stepTxtSty];
    let iconName = 'checkmark'; // Default icon

    // Status-based coloring logic
    if (status === 'Applied' && stepNumber === 1) {
      circleStyle.push(styles.activeCircle);
      textStyle.push(styles.activetxtSty);
    } else if (
      status === 'Approved' &&
      (stepNumber === 1 || stepNumber === 2)
    ) {
      circleStyle.push(styles.activeCircle);
      textStyle.push(styles.activetxtSty);
    } else if (status === 'Completed' && stepNumber <= 3) {
      circleStyle.push(styles.activeCircle);
      textStyle.push(styles.activetxtSty);
    } else if (
      status === 'Completed' &&
      item?.isReviewed === true &&
      stepNumber === 4
    ) {
      circleStyle.push(styles.activeCircle);
      textStyle.push(styles.activetxtSty);
    } else if (status === 'Declined') {
      if (stepNumber === 1) {
        circleStyle.push(styles.activeCircle); // First step remains blue
        textStyle.push(styles.activetxtSty);
      } else if (stepNumber === 2) {
        circleStyle.push(styles.declinedCircle); // Second step turns red
        textStyle.push(styles.declinedTxtSty);
        iconName = 'cross'; // Change icon to 'cross' for the declined step
      }
    }

    return { circleStyle, textStyle, iconName };
  };

  return (
    <View style={styles?.setUpViewSty}>
      <View style={[styles?.setUpSty]}>
        <View style={styles.container}>
          {steps.map((label, index) => {
            const stepNumber = index + 1;
            const status = item?.approvedApplicant?.status && !isSeeker
              ? item?.approvedApplicant?.status
              : item?.isApplied?.status; // Assuming `item.status` contains the status value

            // Conditionally replace "Approved" with "Declined"
            const dynamicLabel =
              status === 'Declined' && label === 'Approved'
                ? translate('Declined', '')
                : label;


            const { circleStyle, textStyle, iconName } =
              getCircleStyle(stepNumber);

            return (
              <TouchableOpacity
                activeOpacity={0.8}
                key={`${stepNumber}+${index}`}
                style={styles.stepItem}>
                <View style={circleStyle}>
                  {/* Render the icon based on the iconName */}
                  {iconName === 'checkmark' ? (
                    <Icons style={textStyle} name={iconName} size={18} />
                  ) : (
                    <EIcons style={textStyle} name={iconName} size={18} />
                  )}
                </View>

                <Text
                  style={[
                    styles.stepLabel,
                    (status === 'Applied' && stepNumber === 1) ||
                    (status === 'Approved' && stepNumber <= 2) ||
                    (status === 'Completed' && stepNumber <= 3) ||
                    (status === 'Completed' &&
                      item?.isReviewed === true &&
                      stepNumber === 4)
                      ? styles.activeLabel
                      : null, // Apply activeLabel style for appropriate steps
                  ]}>
                  {dynamicLabel} {/* Render the modified label */}
                </Text>
                {index < steps.length - 1 && (
                  <View
                    style={[
                      styles.stepLine,
                      {
                        width: IOS
                          ? Dimensions.get('screen').width / 8
                          : Dimensions.get('screen').width / 8,
                      },
                    ]}
                  />
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
  },
  stepItem: {
    width: 80,
    alignItems: 'center',
    position: 'relative',
  },
  stepCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#CACACA',
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeCircle: {
    borderColor: BaseColors.primary,
    backgroundColor: BaseColors.primary,
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedCircle: {
    backgroundColor: BaseColors.primary,
    borderColor: BaseColors.primary,
  },
  declinedCircle: {
    backgroundColor: 'red',
    borderColor: 'red',
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepLabel: {
    fontSize: 13,
    color: BaseColors.textGrey,
    marginTop: 10,
    fontFamily: FontFamily.OpenSansRegular,
    textAlign: 'center',
  },
  activeLabel: {
    color: BaseColors.primary,
    fontSize: 12,
    fontFamily: FontFamily.OpenSansBold,
  },
  completedLabel: {
    color: BaseColors.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  stepLine: {
    width: 70,
    height: 2,
    backgroundColor: '#CACACA',
    position: 'absolute',
    left: 61,
    top: 27,
  },
  setUpViewSty: {
    marginTop: 20,
    marginHorizontal: 20,
  },
  setUpSty: {
    borderWidth: 1,
    borderColor: '#CACACA',
    borderRadius: 10,
    paddingVertical: 10,
  },
  activetxtSty: {
    textAlign: 'center',
    color: BaseColors.white,
  },
  completedtxtSty: {
    textAlign: 'center',
    color: BaseColors.white,
  },
  stepTxtSty: {
    textAlign: 'center',
    color: BaseColors.textGrey,
  },
  declinedTxtSty: {
    textAlign: 'center',
    color: BaseColors.white,
    fontWeight: 'bold',
  },
});

export default StepIndicator;
