import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {Dimensions, StyleSheet} from 'react-native';
const HEIGHT = Dimensions.get('screen').height;

export default StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: BaseColors.white,
  },

  centerMain: {
    flex: 1,
    justifyContent: 'center',
    height: HEIGHT / 1.3,
  },
  mainView: {
    flex: 1,

    marginHorizontal: 15,
    marginTop: 10,
  },
  content: {
    flex: 1,
  },
  buttonContainer: {
    paddingBottom: 20, // Add padding for safe area spacing
    alignSelf: 'stretch', // Ensures the button takes up the full width
    marginHorizontal: 10,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  seekerSty: {
    color: BaseColors?.textColor,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 18,
  },
  seekerDesSty: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
  },
  seeMoreTxt: {
    color: BaseColors?.primary,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
  },
  textView: {
    width: '55%',
  },
  ratingView: {
    width: '45%',
  },
  ratingWrapSty: {
    marginTop: 10,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  underLineSty: {
    borderBottomWidth: 1,
    borderBottomColor: BaseColors.bordrColor,
    marginTop: 15,
  },
  titleSty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  bodySty: {
    // marginHorizontal: 15,
  },
  imageViewSty: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 1,
    borderColor: BaseColors.bordrColor,
  },
  txtSty: {
    fontSize: 16,
    paddingHorizontal: 10,
    fontFamily: FontFamily?.OpenSansMedium,
  },
  mainReviewView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainImgView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imgSty: {
    width: '100%',
    height: '100%',
    borderRadius: 30,
  },
  timeView: {
    alignContent: 'center',
  },
  decscriptionView: {
    flexDirection: 'row',
  },
  mainReviewSty: {
    flexDirection: 'column',
  },
});
