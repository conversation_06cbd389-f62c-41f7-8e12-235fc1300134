import React, {useEffect, useState, useRef} from 'react';
import {
  ActivityIndicator,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Platform,
} from 'react-native';
import styles from './styles';
import TextInput from '@components/UI/TextInput';
import {translate} from '@language/Translate';
import Button from '@components/UI/Button';
import DocumentPicker from 'react-native-document-picker';
import {isEmpty} from '@app/utils/lodashFactions';
import {chatFilesVal, handleFilePick} from '@app/utils/CommonFunction';
import Toast from 'react-native-simple-toast';
import {BaseColors} from '@config/theme';
import EIcon from 'react-native-vector-icons/Entypo';
import moment from 'moment';
import BaseSetting from '@config/setting';
import {getApiData} from '@app/utils/apiHelper';
import ActionSheet from 'react-native-actionsheet';
import RBSheet from 'react-native-raw-bottom-sheet';
import Icon from 'react-native-vector-icons/Fontisto';
import ImagePicker from 'react-native-image-crop-picker';
import FIcon from 'react-native-vector-icons/FontAwesome';
import ImageCropPicker from 'react-native-image-crop-picker';
import { smartAlbums } from '@config/staticdata';

const Certifications = ({
  setCertificateErrorShow,
  preCertificate,
  setPreCertificate,
  editCertificate,
  setEditCertificateList,
  editCertificateList,
  setUpdateCertificateList,
  setCheckSave,
  setCheckSaveCerti,
  refRBSheet,
}: any) => {
  console.log('🚀 ~ preCertificate:', preCertificate);
  const [errors, setErrors] = useState({});
  const [loader, setLoader] = useState(false);
  const [saveLoader, setSaveLoader] = useState(false);
  const [showForm, setShowForm] = useState(true);
  const [savedCertificates, setSavedCertificates] = useState<Array<any>>([]);
  const [saveClick, setSaveClick] = useState(false);
  const CANCEL_INDEX = 3;
  const DESTRUCTIVE_INDEX = 0;
  // Track if component is mounted
  const isMounted = useRef(true);

  // Current form data
  const [currentCertificate, setCurrentCertificate] = useState({
    id: editCertificateList?.id ? editCertificateList?.id : Date.now(),
    company:
      editCertificateList?.company === null
        ? editCertificateList?.docTitle
        : editCertificateList?.company || '',
    startDate: editCertificateList?.startDate
      ? moment(editCertificateList?.startDate)
      : undefined,
    endDate: editCertificateList?.endDate
      ? moment(editCertificateList?.endDate)
      : undefined,
    fileName: editCertificateList?.fileName
      ? editCertificateList?.fileName.split('/').pop()
      : '',
    filePath: editCertificateList?.filePath
      ? editCertificateList?.filePath
      : '',
    docType: 'certifications',
  });
  const IOS = Platform.OS === 'ios';

  const ActionSheetRef = useRef<any>();
  const ActionSheetRefIOS = useRef<any>();
  function showActionSheet() {
    if (IOS) {
      ActionSheetRefIOS.current.open();
    } else {
      ActionSheetRef.current.show();
    }
  }

  function doAction(index: any) {
    if (index === 0) {
      handleCertificateFileSelect();
    } else if (index === 1) {
      handleCameraCapture();
    }
  }

  const options = [
    <TouchableOpacity
      onPress={() => handleCertificateFileSelect()}
      key={`gallery-option-${1}`}
      style={[styles.optionsContainer, {marginTop: IOS ? 15 : 0}]}>
      <Icon
        name="file-1"
        size={22}
        color={BaseColors.primary}
        style={{paddingRight: 5}}
      />
      <Text
        style={{
          marginLeft: 15,
          color: BaseColors.primary,
        }}>
        {translate('Files', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => handleCameraCapture('type')}
      key={`camera-option-${2}`}
      style={[styles.optionsContainer, {paddingVertical: 10, marginLeft: 6}]}>
      <FIcon
        name="camera"
        size={18}
        color={BaseColors.primary}
        style={{paddingLeft: 4}}
      />
      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Camera', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => handleImageFileSelect()}
      key={`camera-option-${3}`}
      style={[
        styles.optionsContainer,
        {paddingVertical: IOS ? 0 : 10, marginLeft: 6},
      ]}>
      <FIcon name="photo" size={18} color={BaseColors.primary} />
      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Photos', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => {
        if (IOS) {
          ActionSheetRefIOS.current.close();
        } else {
          ActionSheetRef.current.hide();
        }
      }}
      key={`cancel-option-${4}`}
      style={[
        styles.optionsContainer,
        {
          paddingVertical: 10,
          marginHorizontal: IOS ? 0 : 20,
          borderTopWidth: IOS ? 3 : 0,
          borderTopColor: BaseColors.textInput,
        },
      ]}>
      <EIcon name="cross" size={18} color={BaseColors.primary} />

      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Cancel', '')}
      </Text>
    </TouchableOpacity>,
  ];

  // Validate fields
  const validateFields = (certificate: any) => {
    let newErrors = {};
    if (!certificate.company)
      newErrors.company = 'Certificate name is required';
    if (!certificate.startDate) newErrors.startDate = 'Start date is required';
    if (!certificate.endDate) newErrors.endDate = 'End date is required';
    if (!certificate.fileName) newErrors.fileName = 'File is required';
    return newErrors;
  };

  // Reset form to initial state
  const resetForm = () => {
    setCurrentCertificate({
      id: Date.now(),
      company: '',
      startDate: undefined,
      endDate: undefined,
      fileName: '',
      filePath: '',
      docType: 'certifications',
    });
    setErrors({});
  };

  // Update certificate field
  const updateCertificate = (field: string, value: any) => {
    setCurrentCertificate(prev => ({...prev, [field]: value}));
    setErrors(prevErrors => ({...prevErrors, [field]: undefined}));
    checkFormValid();
  };

  // Check if form is valid
  const checkFormValid = () => {
    const newErrors = validateFields(currentCertificate);
    const hasErrors = Object.keys(newErrors).length > 0;
    setCertificateErrorShow(hasErrors);
    return hasErrors;
  };

  // Add new certificate (show form)
  const addCertificate = () => {
    resetForm();
    setShowForm(true);
  };

  // Handle file selection
  const handleCertificateFileSelect = async () => {
    setLoader(true);
    try {
      const [file] = await DocumentPicker.pick({
        type: [
          DocumentPicker.types.pdf,
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
        ],
      });

      const fileRes = await handleFilePick(file, 'cv');
      if (!isEmpty(fileRes)) {
        updateCertificate('fileName', fileRes?.data.fileName);
        updateCertificate('filePath', fileRes?.data.filePath);
        if (IOS) {
          ActionSheetRefIOS.current.close();
        } else {
          ActionSheetRef.current.hide();
        }
      }
    } catch (err) {
      if (!DocumentPicker.isCancel(err)) {
        console.error('File selection error:', err);
      }
      if (IOS) {
        ActionSheetRefIOS.current.close();
      } else {
        ActionSheetRef.current.hide();
      }
    }
    setLoader(false);
  };

  const handleImageFileSelect = async () => {
    setLoader(true);
    try {
      const file: any = await ImageCropPicker.openPicker({
        mediaType: 'photo', // Only allow image files
        cropping: false, // Set to true if you want cropping
        smartAlbums: smartAlbums,
      });

      const fileRes = await handleFilePick(file, 'image');
      if (!isEmpty(fileRes)) {
        updateCertificate('fileName', fileRes?.data?.fileName);
        updateCertificate('filePath', fileRes?.data?.filePath);
        if (IOS) {
          ActionSheetRefIOS.current.close();
        } else {
          ActionSheetRef.current.hide();
        }
      }
    } catch (err) {
      console.error('Image selection error:', err);
      if (IOS) {
        ActionSheetRefIOS.current.close();
      } else {
        ActionSheetRef.current.hide();
      }
    }
    setLoader(false);
  };

  const handleCameraCapture = async () => {
    setLoader(true);
    try {
      const image: any = await ImagePicker.openCamera({
        cropping: true,
      });

      if (image) {
        const fType = image?.mime || '';
        const isValidFile = chatFilesVal(fType, image.size);

        if (isValidFile) {
          const fileRes = await handleFilePick(image, 'image');

          if (!isEmpty(fileRes)) {
            updateCertificate('fileName', fileRes?.data?.fileName);
            updateCertificate('filePath', fileRes?.data?.filePath);
            if (IOS) {
              ActionSheetRefIOS.current.close();
            } else {
              ActionSheetRef.current.hide();
            }
          }
        } else {
          setTimeout(() => {
            // Toast.show(translate('appliedValidSizeFile', ''), Toast.BOTTOM);
          }, 50);
          if (IOS) {
            ActionSheetRefIOS.current.close();
          } else {
            ActionSheetRef.current.hide();
          }
        }
      }
    } catch (error) {
      console.error('Error capturing image:', error);
      if (IOS) {
        ActionSheetRefIOS.current.close();
      } else {
        ActionSheetRef.current.hide();
      }
    }
    setLoader(false);
  };

  // Handle file removal
  const handleFileRemove = () => {
    updateCertificate('fileName', '');
    updateCertificate('filePath', '');
  };

  // Handle save certificate
  const handleSave = () => {
    setSaveLoader(true);
    const newErrors = validateFields(currentCertificate);
    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      // Add the current certificate to saved certificates
      const certificateToSave = {...currentCertificate};
      setSavedCertificates(prev => [...prev, certificateToSave]);

      // Update preCertificate state with all saved certificates including the current one
      const updatedCertificates = [...savedCertificates, certificateToSave];
      setPreCertificate(updatedCertificates);

      // Notify success
      Toast.show(translate('savedSucessfully', ''));
      setCheckSaveCerti(true);

      // Call handleSubmit after successfully saving
      handleSubmit();
    } else {
      setSaveLoader(false);
    }
  };

  const formatCertificateForAPI = certificate => {
    return certificate.map(({id, startDate, endDate, ...rest}) => ({
      ...rest,
      startDate: moment(startDate).format('MM/DD/YYYY hh:mm A'),
      ...(endDate !== null && {
        endDate: moment(endDate).format('MM/DD/YYYY hh:mm A'),
      }),
    }));
  };

  const formateditCertificateForAPI = certificate => {
    return certificate.map(({startDate, endDate, ...rest}) => ({
      ...rest,
      startDate: moment(startDate).format('MM/DD/YYYY hh:mm A'),
      ...(endDate !== null && {
        endDate: moment(endDate).format('MM/DD/YYYY hh:mm A'),
      }),
    }));
  };

  const handleSubmit = async () => {
    console.log('abssss');
    setSaveLoader(true);

    // Get only the current experience data for API submission
    const experienceToSubmit = editCertificate
      ? [currentCertificate]
      : [currentCertificate];
    const formattedCertificate = formatCertificateForAPI(experienceToSubmit);
    const formattedEditCertificate =
      formateditCertificateForAPI(experienceToSubmit);

    try {
      const res = await getApiData({
        endpoint: editCertificate
          ? BaseSetting.endpoints.updateQualification
          : BaseSetting.endpoints.AddQualification,
        method: 'POST',
        data: editCertificate
          ? formattedEditCertificate[0]
          : {array: formattedCertificate},
      });
      console.log('🚀 ~ handleSubmit ~ res:', res);

      if (res?.status === true) {
        if (isMounted.current) {
          setPreCertificate(res?.data?.certifications);
          setSaveClick(false);
          Toast.show(translate('savedSucessfully', ''), Toast.BOTTOM);

          // Reset the form after successful API call
          if (!editCertificate) {
            setShowForm(false);
            resetForm();

            // Clear the updateExperinceList to prevent re-adding old data
            // Only update with the current successful submission
            if (res?.data?.certifications) {
              // If the API returns the updated list, use that instead
              setPreCertificate(res?.data?.certifications);
            }
          } else if (editCertificate) {
            refRBSheet?.current?.close();
          }
        }
      } else {
        if (isMounted.current) {
          Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        }
      }

      if (isMounted.current) {
        setSaveLoader(false);
      }
    } catch (err) {
      console.log('check Error', err);
      if (isMounted.current) {
        setSaveLoader(false);
        Toast.show(translate('err', ''), Toast.BOTTOM);
      }
    }
  };

  // Initialize data when component mounts
  useEffect(() => {
    if (editCertificate && editCertificateList) {
      // If in edit mode, only set the current certificate being edited
      setUpdateCertificateList([]);
    } else {
      // If in add mode, clear any previous data
      setUpdateCertificateList([]);
    }

    // Initially check if the form is valid when component mounts
    checkFormValid();

    // Cleanup function when component unmounts
    return () => {
      isMounted.current = false;
      setUpdateCertificateList([]);
    };
  }, []);

  // Check form validity whenever currentCertificate changes
  useEffect(() => {
    checkFormValid();
  }, [currentCertificate]);

  return (
    <>
      <View style={styles.fileContainer}>
        <Text style={styles.resumeText}>Certificate</Text>
        <ScrollView style={{flex: 1}}>
          {/* Display saved certificates count if any */}
          {savedCertificates.length > 0 && !showForm && (
            <View>
              <Text>{/* Optional: display count of saved certificates */}</Text>
            </View>
          )}

          {/* Certificate form - only shown when showForm is true */}
          {showForm && (
            <View style={styles.mainView}>
              <TextInput
                value={currentCertificate.company}
                onChange={(value: any) => updateCertificate('company', value)}
                title={translate('certificateTitle', '')}
                placeholderText={translate('typeHere', '')}
                maxLength={50}
                showError={!!errors?.company}
                errorText={errors?.company}
              />
              <View style={styles.dateView}>
                <View style={styles.startDateView}>
                  <TextInput
                    Date={true}
                    selectedDate={currentCertificate.startDate}
                    onDateChange={(date: any) =>
                      updateCertificate('startDate', date)
                    }
                    title={translate('startDate', '')}
                    datetimemodal="Start Date"
                    showError={!!errors?.startDate}
                    errorText={errors?.startDate}
                  />
                </View>
                <View style={styles.startDateView}>
                  <TextInput
                    iseditable={!!currentCertificate.startDate}
                    Date={true}
                    selectedDate={currentCertificate.endDate}
                    onDateChange={(date: any) =>
                      updateCertificate('endDate', date)
                    }
                    title={translate('endDate', '')}
                    datetimemodal="End Date"
                    showError={!!errors?.endDate}
                    errorText={errors?.endDate}
                    // minDate={new Date(currentCertificate?.startDate)}
                  />
                </View>
              </View>
              <View style={styles.chooseFileContainer}>
                <View style={styles?.removeSty}>
                  <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={showActionSheet}>
                    {loader ? (
                      <ActivityIndicator color={BaseColors.primary} />
                    ) : (
                      <Text style={styles.chooseFileText}>
                        {currentCertificate.fileName
                          ? currentCertificate.fileName
                          : translate('ChooseFile', '')}
                      </Text>
                    )}
                  </TouchableOpacity>
                  {currentCertificate.fileName && (
                    <TouchableOpacity
                      onPress={handleFileRemove}
                      style={styles.removeIcon}>
                      <EIcon
                        name="cross"
                        size={22}
                        color={BaseColors.primary}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              </View>

              {errors?.fileName && (
                <Text style={styles.errorText}>{errors?.fileName}</Text>
              )}
            </View>
          )}

          {editCertificate ? null : (
            <>
              {/* Add Certificate button - only shown when not showing the form */}
              {!showForm && (
                <TouchableOpacity
                  onPress={addCertificate}
                  activeOpacity={0.8}
                  style={styles.addExpeinceViewSty}>
                  <Text style={styles.addExperinceSty}>
                    + {translate('AddCertificate', '')}
                  </Text>
                </TouchableOpacity>
              )}
            </>
          )}
        </ScrollView>
      </View>

      {/* Save button - only shown when form is displayed */}
      {showForm ? (
        <View style={styles?.btnViewSty}>
          <View style={{width: '100%'}}>
            <Button loading={saveLoader} onPress={handleSave} type="text">
              {translate('save', '')}
            </Button>
          </View>
        </View>
      ) : (
        <View style={styles?.btnViewSty}>
          <View style={{width: '100%'}}>
            <View style={styles?.btnViewSty}>
              <View style={{width: '100%'}}>
                <Button
                  onPress={() => {
                    refRBSheet?.current?.close();
                  }}
                  type="text">
                  Done
                </Button>
              </View>
              <View />
            </View>
          </View>
        </View>
      )}
      <ActionSheet
        ref={ActionSheetRef}
        options={options}
        cancelButtonIndex={CANCEL_INDEX}
        destructiveButtonIndex={DESTRUCTIVE_INDEX}
        onPress={(index: any) => doAction(index)}
      />
      <RBSheet
        ref={ActionSheetRefIOS}
        closeOnDragDown={true}
        closeOnPressMask={true}
        dragFromTopOnly={true}
        height={180}
        customStyles={{
          draggableIcon: {
            width: 50,
            marginTop: 30,
          },
          container: {
            backgroundColor: '#FFF',
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
          },
        }}>
        <View>
          {options?.map(item => {
            return item;
          })}
        </View>
      </RBSheet>
    </>
  );
};

export default Certifications;
