import React, { useCallback, useState } from 'react';
import {
  ActivityIndicator,
  <PERSON><PERSON>,
  FlatList,
  RefreshControl,
  ScrollView,
  View,
} from 'react-native';
import Header from '@components/Header';
import { BaseColors } from '@config/theme';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import NoRecord from '@components/NoRecord';
 import Toast from 'react-native-simple-toast';
import { translate } from '@language/Translate';
import { useFocusEffect } from '@react-navigation/native';
import CounterOfferCard from '@components/SeekerCard/CounterOfferCard';
import styles from '@screens/Applicants/styles';
import { useRedux } from '@components/UseRedux';
import { isEmpty } from '@app/utils/lodashFactions';
import { updateJobStatus } from '@screens/JobApplicant/ApiFuntions';
import AlertModal from '@components/AlertModal';
import CustomOfferModal from '@components/CustomOfferModal';
import moment from 'moment';
import { convertToCamelCase } from '@app/utils/CommonFunction';
import { initPaymentSheet, presentPaymentSheet } from '@stripe/stripe-react-native';

export default function CounterHistory({ navigation, route, jobDetail, applicant }: { navigation?: any, route?: any, jobDetail?: any, applicant?: any }) {
  const params = route?.params || {};
  const job = params?.job || {};
  const item = params.item || {};
  const jobID = job?.id || jobDetail?.id || null;
  const [jobDetails, setJobDetail] = useState<any>({});
  const { useAppSelector } = useRedux();
  const { userData } = useAppSelector((s: any) => s.auth);

  const [list, setList] = useState({
    data: [],
    pagination: { page: 1, next_enable: null },
  });
  const [state, setState] = useState({
    bottomLoading: false,
    refreshing: false,
    loader: false,
    saveLoader: false,
    applicantsLoader: false,
    confirmationModal: false,
    applicant: {},
    job: {},
    type: '',



    customOfferModal: false,
    counterOfferModal: false,
    counterHistory: [],
    customOfferLoader: false,
    counterOfferLoader: false,
  });
  const isEmployer = userData?.id === job?.userId;
  const isOfferByEmployer = item?.offerBy === 'employer';



  const { loader, refreshing, bottomLoading, customOfferModal, counterOfferModal } = state;

  // Function to get list of seekers or employers
  const getList = async (page = 1, bottomLoader: boolean = false) => {
    if (loader) {return;} // Prevent duplicate calls
    if (bottomLoader) {
      setState((p: any) => ({ ...p, bottomLoading: true }));
    } else {
      setState((p: any) => ({ ...p, loader: true }));
    }
    console.log('isEmployer ==>', applicant, jobDetail);
    const data: any = {
      page,
      perPage: isEmpty(jobDetail) ? 5 : 2,
      jobId: jobID,
      userId: userData?.id,
      seekerId: item?.seekerId || applicant?.seekerId,
      employerId: item?.employerId || applicant?.employerId,
      // opponentId: getOpponentID,
      // userType: userData?.id === job?.userId ? 'employer' : 'seeker',
    };
    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.JobCounterHistory,
        method: 'POST',
        data: data,
      });
      console.log('resp ==>', data, JSON.stringify(resp));
      if (resp?.data) {
        setList(p => ({
          ...p,
          data:
            page > 1
              ? [...list?.data, ...resp?.data]
              : resp.data || [],
          pagination: resp,
        }));
        getDetails();
      } else {
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
      setState((p: any) => ({
        ...p,
        loader: false,
        refreshing: false,
        bottomLoading: false,
      }));
    } catch (e) {
      setState((p: any) => ({
        ...p,
        loader: false,
        refreshing: false,
        bottomLoading: false,
      }));
      console.log('ERRR', e);
    }
  };
  const fetchPaymentSheetParams = async (amount = 20, id = null) => {
    console.log('amount ===>', amount);
    let data: {
      amount?: number;
      jobId?: number;
      currency?: string;
      seekerId?: any;
      timezone: any;
    } = {};
    if (Number(amount) > 0) {
      data.amount = amount;
      data.jobId = jobID;
      data.currency = 'USD';
      data.seekerId = id;
      data.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    }

    console.log('🚀 ~ fetchPaymentSheetParams ~ data:', data);
    const resp = await getApiData({
      endpoint: BaseSetting.endpoints.getStripIntentId,
      method: 'POST',
      data: data,
    });
    console.log('🚀 ~ fetchPaymentSheetParams ~ resp:', resp);
    if (!resp?.status) {
      // Toast.show(resp?.message || translate('error'), Toast.LONG);
    }

    const { paymentIntent, ephemeralKey, customer } = resp?.data || {};

    return {
      paymentIntent,
      ephemeralKey,
      customer,
    };
  };

  const initializePaymentSheet = async (id?: any, item?: any) => {
    console.log('paymentIntent ===>');
    const { paymentIntent, ephemeralKey, customer } =
      await fetchPaymentSheetParams(jobDetails?.seekerPayableAmount + jobDetails?.serviceCharge, id);

    const { error } = await initPaymentSheet({
      merchantDisplayName: 'The Harbor App',
      customerId: customer,
      customerEphemeralKeySecret: ephemeralKey,
      paymentIntentClientSecret: paymentIntent,
      // Set `allowsDelayedPaymentMethods` to true if your business can handle payment
      //methods that complete payment after a delay, like SEPA Debit and Sofort.
      allowsDelayedPaymentMethods: true,
      // defaultBillingDetails: {
      //   name: 'Jane Doe',
      // },
      googlePay: {
        merchantCountryCode: 'US',
        testEnv: false, // use test environment
      },
      applePay: {
        merchantCountryCode: 'US',
      },
      returnURL: 'com.harbor.app://stripe-redirect',
    });
    if (!error) {
      openPaymentSheet();
      // setLoading(true);
    }
  };

  const openPaymentSheet = async () => {
    console.log('🚀 ~ openPaymentSheet ~ error:');
    const { error } = await presentPaymentSheet();
    console.log('🚀 ~ openPaymentSheet ~ error:', error);

    if (error) {
      Alert.alert(`Error code: ${error.code}`, error.message);
    } else {
      // updateJobStatus('Approved');
      getList();
      // Alert.alert('Success', 'Your order is confirmed!');
    }
  };

  const handleClick = useCallback(async (item: any, type: string) => {
    console.log('item ===>', item);
    if (item?.counterOfferId) {
      if (type === 'pay') {
        await initializePaymentSheet(item?.seekerId, item);
        // navigation.navigate('JobDetailScreen', { applicant: { ...item, id: applicant?.seekerId }, type, job: jobDetails });
        return false;
      }
      setState((p: any) => ({
        ...p,
        counterOfferModal: true,
        applicant: item,
        type: type,
        job: job,
      }));
      // return false;
    } else if (type === 'Approved') {
      navigation.navigate('JobDetailScreen', { applicant: item, type, job: jobDetails });
    } else {
      setState((p: any) => ({
        ...p,
        confirmationModal: true,
        applicant: item,
        type,
        job,
      }));
    }
  }, [jobDetails, job, applicant]);


  const getDetails = useCallback(async () => {
    try {
      console.log('job');

      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.jobDetail + `/${jobID}`,
        method: 'GET',
      });

      if (resp?.data && resp?.status) {
        setJobDetail(resp?.data); // Update list with new data
        setState((p: any) => ({
          ...p,
          loader: false,
          refreshing: false,
          bottomLoading: false,
        }));
      }
    } catch (error) {
      console.error('Error fetching list:', error);
      // Toast.show('Failed to fetch data.', Toast.SHORT);
    }
  },
  [job, jobDetail],
  );

  const onRefresh = React.useCallback(() => {
    if (!loader) {
      setState((p: any) => ({ ...p, refreshing: true }));
      getList(1, bottomLoading);
      setState((p: any) => ({ ...p, refreshing: false }));
    }
  }, [loader]);

  const ListEndLoader = () => {
    if (!loader && bottomLoading) {
      return <ActivityIndicator color={BaseColors.primary} size={'small'} />;
    }
  };

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (
      isCloseToBottom &&
      list?.pagination?.next_enable &&
      !loader &&
      !bottomLoading
      && isEmpty(jobDetail)
    ) {
      getList(Number(list?.pagination?.page) + 1, true);
    }
  };

  const onSubmit = useCallback(
    async (values: any, type?: string) => {
      const isEdit = state?.type === 'edit';
      if (state?.applicant?.counterOfferId || type === 'custom') {
        if (state?.type === 'Approved' || state?.type === 'Declined') {
          const url = BaseSetting.endpoints.updateCounterOfferEdit;
          const apiData = {
            counterOfferId: state?.applicant?.counterOfferId || null,
            userId: userData?.id,
            jobId: jobID || null,
            userType: state?.applicant?.offerBy === 'seeker' ? 'employer' : 'seeker',
            status: String(state?.type).toLowerCase(),
          };
          try {
            setState((p: any) => ({ ...p, customOfferLoader: true }));
            const response = await getApiData({
              endpoint: url,
              data: apiData,
              method:  'POST',
            });

            if (response?.status) {
              setState({ ...state, customOfferModal: false, confirmationModal: false, counterOfferModal: false, customOfferLoader: false, type: '', applicant: {}, job: {} });
              getList();
              // Toast.show(response?.message || translate('err'), Toast.SHORT);
            } else {
              // Toast.show(response?.message || translate('err'), Toast.SHORT);
            }
          } catch (error) {
            console.error('Error submitting form:', error);
          }
        } else {
          setState((p) => ({ ...p, customOfferLoader: true }));
          console.log('🚀 ~ handleSubmit values:', state?.type === 'counter' ? isEmployer ? state?.applicant?.id :  state?.applicant?.sendBy : isEmployer ? state?.applicant?.id : job?.userId || jobDetail?.userId,
            state?.applicant,
          );
          // duration must of following :perHour,perDay,perWeek,perMonth"
          const payload: any = {
            jobId: jobID,
            finalPrice: values?.salaryAmount,
            duration: values?.duration === 'Flat Rate' || (jobDetail?.isFlateRate && values?.flatRate) ? undefined : convertToCamelCase(values?.duration),
            startTime: values?.startTime ? moment(values.startTime).format('hh:mm A') : '',
            endTime: values?.endTime ? moment(values.endTime).format('hh:mm A') : '',
            startDate: values?.startDate ? moment(values.startDate).format('MM/DD/YYYY') : '',
            endDate: values?.endDate ? moment(values.endDate).format('MM/DD/YYYY') : '',
            message: values?.msg || '',
            sendBy: isEmployer ? state?.applicant?.employerId : state?.applicant?.seekerId,
            receiveBy: isEmployer ? state?.applicant?.seekerId : state?.applicant?.employerId,
            // sendBy: isEmployer ? userData?.id : state?.applicant?.receiveBy,
            // receiveBy: isEmployer ? state?.applicant?.id :  state?.applicant?.sendBy,
            offerBy: userData?.id == (job?.userId || jobDetail?.userId) ? 'employer' : 'seeker',
          };
          if (isEdit) {
            payload.counterOfferId = state?.applicant?.counterOfferId || null;
            payload.userId = userData?.id;
            payload.userType = userData?.id == (job?.userId || jobDetail?.userId) ? 'employer' : 'seeker';
          }

          const endPoint = isEdit ? BaseSetting.endpoints.createCounterOfferEdit : BaseSetting.endpoints.createOffer;
          try {
            const response = await getApiData({
              endpoint: endPoint,
              data: payload,
              method:  'POST',
            });

            console.log('response ==>', response);

            if (response?.status) {
              setState({ ...state, customOfferModal: false, counterOfferModal: false, customOfferLoader: false, applicant: false, job: {} });
              getList();
              // Toast.show(response?.message, Toast.SHORT);
            } else {
              setState({ ...state, customOfferLoader: false });
              // Toast.show(response?.message || translate('error'), Toast.SHORT);
            }
          } catch (error) {
            setState({ ...state, customOfferLoader: false });
            console.error('Error submitting form:', error);
          }
        }
        return false;
      } else if (type === 'Approved') {
        navigation.navigate('JobDetailScreen', { applicant: state?.applicant, type, job: jobDetails });
      } else {
        setState((p: any) => ({
          ...p,
          confirmationModal: true,
          applicant: state?.applicant,
          type,
          job: jobDetail,
        }));
      }
    },
    [customOfferModal, state, counterOfferModal, jobDetail, applicant, jobDetails],
  ); // Only depend on jobId to avoid unnecessary re-creations


  const handleOnClose = useCallback(() => {
    setState({ ...state, customOfferModal: false, counterOfferModal: false });
  }, [state]);

  const renderItem = (item: any) => {
    return (
      <CounterOfferCard
        item={item}
        onActionClick={handleClick}
        applicantsLoader={false}
        navigation={navigation}
        jobDetail={job}
        type={'History'}
        buttons={isEmpty(jobDetail) && jobDetails?.approvedApplicant?.status !== 'Approved'}
      />
    );
  };

  useFocusEffect(
    React.useCallback(() => {
      console.log('useFocuss effect called');
      getList(1, false);
      return () => { };
    }, []),
  );

  return (
    <View style={[styles.container]}>
      {isEmpty(jobDetail) ? (<Header
        leftIcon="back-arrow"
        title={translate('counterHistory', '')}
        onLeftPress={() => {
          navigation.goBack();
        }}
      />) : null}
      <ScrollView
        onScroll={handleScroll}
        style={{
          ...styles.mainView,
        }}
        nestedScrollEnabled={true}
        contentContainerStyle={{ flexGrow: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[BaseColors.primary]} // Customize refresh indicator color
            tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
          />
        }
        showsVerticalScrollIndicator={false}>
        {loader ? (
          <View style={styles.centerMain}>
            <ActivityIndicator color={BaseColors.primary} />
          </View>
        ) : (
          <FlatList
            data={list?.data || []}
            keyExtractor={(item: any) => `${item.id}+1`}
            renderItem={({ item }) => renderItem(item)}
            ListEmptyComponent={
              <View style={styles.centerMain}>
                <NoRecord
                  title={'noHistory'}
                  type="employer"
                  iconName="employer"
                />
              </View>
            }
            style={{
              ...styles.mainView,
            }}
            scrollEnabled={false} // Disable FlatList's scrolling
            ListFooterComponent={ListEndLoader} // Loader when loading next page.
          />
        )}
      </ScrollView>

      <CustomOfferModal
        setState={setState}
        state={state}
        title={translate('customOffer')}
        jobDetail={jobDetails || jobDetail}
        onSubmit={(values: any) => {
          // getList();
          onSubmit(values, 'custom');
        }}
        navigation={navigation}
        onCancel={handleOnClose}
      />

      {state.confirmationModal && (
        <AlertModal
          image
          title={translate('declineJob', '')}
          visible={state.confirmationModal}
          setVisible={(val: any) =>
            setState((p: any) => ({ ...p, confirmationModal: false }))
          }
          btnYPress={async () => {
            if (state?.applicant?.counterOfferId) {
              onSubmit({});
            } else {
              setState((p: any) => ({ ...p, applicantsLoader: true }));
              const resp = await updateJobStatus('Declined', { jobId: jobID, userId: state?.applicant?.id });
              if (resp?.status) {
                setState((p: any) => ({
                  ...p,
                  confirmationModal: false,
                  applicant: {},
                  job: {},
                }));
                getList();
              }
            }
          }}
          loader={state?.applicantsLoader}
          btnYTitle={translate('YES')}
          btnNTitle={translate('CANCEL')}
          btnNPress={() => {
            setState((p: any) => ({ ...p, confirmationModal: false }));
          }}
          confirmation
        />
      )}
    </View>
  );
}
