import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import styles from './styles';
import { translate } from '@language/Translate';
import { useTheme } from '@react-navigation/native';
import Header from '@components/Header';
import { CustomIcon } from '@config/LoadIcons';

export default function HarborPolicy({ navigation }: any) {
  const colors = useTheme();
  const BaseColors: any = colors.colors;

  // Data for policy list
  const policies = [
    {
      title: translate('termsandService', ''),
      screen: 'WebViewScreen',
      type: 'terms',
    },
    {
      title: translate('cancellationPolicy', ''),
      screen: 'Policy',
      type: 'cancellation_policy',
    },
    {
      title: translate('paymentPolicy', ''),
      screen: 'Policy',
      type: 'payment_policy',
    },
    {
      title: translate('privacyPolicy', ''),
      icon: 'PrivacyPolicy',
      screen: 'WebViewScreen',
      type: 'PrivacyPolicy',
    },
  ];

  return (
    <View style={{ ...styles.container }}>
      {/*  <StatusBar barStyle={'dark-content'} /> */}
      <Header
        title="Harbor Policies"
        leftIcon="back-arrow"
        onLeftPress={() => navigation.goBack()}
      />

      {/* Policy List */}
      <View style={styles.policyListContainer}>
        {policies.map((policy, index) => (
          <TouchableOpacity
            key={index}
            style={styles.policyItem}
            onPress={() => {
              navigation.navigate(policy.screen, {
                type: policy?.type,
                title: policy?.title,
              });
            }}>
            <Text style={styles.policyTitle}>{policy.title}</Text>
            <CustomIcon
              name="PrivacyPolicy"
              size={16}
              color={BaseColors.text}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}
