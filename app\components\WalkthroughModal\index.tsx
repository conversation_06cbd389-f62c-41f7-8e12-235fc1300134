// OnboardingModal.tsx
import React, {useState} from 'react';
import {
  Dimensions,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Button from '@components/UI/Button';
import {Images} from '@config/images';
import {CustomIcon} from '@config/LoadIcons';
import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import FastImage from 'react-native-fast-image';
import Swiper from 'react-native-swiper';

const {height} = Dimensions.get('window');

const slides = [
  {
    header: 'Welcome to Harbor!',
    body: 'Let’s shape the future of yachting.',
    cta: '▶️ Start Walkthrough',
  },
  {
    header: '🔍 Search',
    body: `Find your dream job or your perfect crew:
Employers = Job listings
Seekers = Verified crew`,
    cta: '➡️ Next',
    search: true,
  },
  {
    header: 'My Harbor',
    body: 'Your control center for managing all pending, upcoming, and completed jobs.',
    cta: '➡️ Next',
    icon: true,
  },
  {
    header: '🚀 Build Your Profile',
    body: 'Add your details, verify your identity, and connect your bank to get booked and paid.',
    cta: '➡️ Next',
  },
  {
    header: '📈 Level Up Your Career',
    body: 'Complete jobs, challenges, reviews, and more to boost your level and showcase your skills.',
    cta: '➡️ Final Step',
  },
  {
    header: '🌊 Welcome!',
    body: 'Let’s change how the industry connects—one job at a time.',
    cta: 'Have thoughts? Contact Us! Feedback helps us build a better industry—together.',
    finalButtons: true,
  },
];

const WalkthroughModal = ({
  visible,
  onClose,
}: {
  visible: boolean;
  onClose: () => void;
}) => {
  const [swiperRef, setSwiperRef] = useState<any>(null);

  const goToNextSlide = (index: number) => {
    if (swiperRef && index < slides.length - 1) {
      swiperRef.scrollBy(1);
    } else {
      onClose();
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        style={styles.modal}
        animationType="fade"
        transparent={true}>
        <View style={styles.overlay}>
          <View style={styles.modalContent}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
            <Swiper
              loop={false}
              dotStyle={styles.dot}
              activeDotStyle={styles.activeDot}
              showsButtons={false}
              ref={ref => setSwiperRef(ref)}>
              {slides.map((slide, index) => (
                <View
                  style={{...styles.slide, justifyContent: 'center'}}
                  key={index}>
                  {index === 0 ? (
                    <FastImage
                      source={Images.shortLogo}
                      resizeMode={FastImage.resizeMode.contain}
                      style={styles.logo}
                    />
                  ) : null}
                  <View style={styles.row}>
                    {slide?.icon ? (
                      <CustomIcon
                        name="harbor-outlined"
                        color={BaseColors.primary}
                        size={22}
                        style={styles.icon}
                      />
                    ) : null}
                    <Text style={styles.header}>{slide.header}</Text>
                  </View>
                  <Text style={[styles.body, styles.fText]}>{slide.body}</Text>
                  <View
                    style={{
                      backgroundColor: 'white',
                      width: '100%',
                      marginBottom: 15,
                      pointerEvents: 'none',
                    }}>
                    {/* {slide?.search ? <TabComponent selectedTab="Seeker" /> : null} */}
                  </View>
                  {slide.finalButtons ? (
                    <Text style={[styles.body]}>{slide.cta}</Text>
                  ) : null}
                  {slide.finalButtons ? (
                    <View style={styles.buttonRow}>
                      {/* <TouchableOpacity
                      style={styles.ctaButton}
                      onPress={() => {
                        onClose();
                      }}> */}
                      <Button
                        onPress={() => {
                          onClose();
                        }}
                        containerStyle={{paddingHorizontal: 10}}
                        type="text">
                        <Text style={styles.ctaText}>🔎 Search Harbor</Text>
                      </Button>
                      {/* <TouchableOpacity
                      style={styles.ctaButton}
                      onPress={() => {}}>
                      <Text style={styles.ctaText}>📣 Post a Job</Text>
                    </TouchableOpacity> */}

                      <Button
                        containerStyle={{paddingHorizontal: 10}}
                        onPress={() => {
                          // if (userData?.isProfileSet !== false ) {
                          //   setModalOpen(p => ({ ...p, confirmationModal: true }));
                          // } else {
                          //   navigationRef?.current?.navigate('JobPosting', {});
                          onClose('postJob');
                          // }
                        }}
                        type="outlined">
                        📣 Post a Job
                      </Button>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={styles.ctaButton}
                      onPress={() => goToNextSlide(index)}>
                      <Text style={styles.ctaText}>{slide.cta}</Text>
                    </TouchableOpacity>
                  )}
                  {index === 0 && (
                    <TouchableOpacity
                      style={styles.laterButton}
                      onPress={onClose}>
                      <Text style={styles.laterText}>Later</Text>
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </Swiper>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  modalContent: {
    backgroundColor: BaseColors.white,
    borderRadius: 12,
    marginHorizontal: 20,
    // height: '100%',
    // flex: 0.44,
    height: height / 2.5,
    // maxHeight: '40%', // Prevent excessive height on larger screens
    width: '90%',
    alignSelf: 'center',
    zIndex: 3, // Modal content above overlay
  },
  row: {
    flexDirection: 'row',
    display: 'flex',
    alignItems: 'center',
    marginBottom: 16,
  },
  slide: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
    flex: 1,
  },
  header: {
    fontSize: 22,
    fontFamily: FontFamily.OpenSansBold,
    textAlign: 'center',
  },
  body: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    textAlign: 'center',
    color: '#333',
    // maxWidth: '85%',
  },
  fText: {
    // marginBottom: 10,
  },
  ctaButton: {
    backgroundColor: BaseColors.primary,
    paddingVertical: 13,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginVertical: 8,
  },
  ctaText: {
    color: BaseColors.white,
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
  },
  laterButton: {
    marginTop: 10,
  },
  laterText: {
    color: '#888',
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    textDecorationLine: 'underline',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 5,
    justifyContent: 'space-around',
    // width: '100%',
    marginTop: 20,
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  dot: {
    backgroundColor: '#ccc',
    width: 10,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: BaseColors.primary,
    width: 12,
    height: 12,
    borderRadius: 6,
    marginHorizontal: 4,
  },
  modal: {
    justifyContent: 'center',
    alignItems: 'center',
    margin: 0,
    flex: 1,
  },
  icon: {
    textAlign: 'center',
    marginRight: 10,
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.2)',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: 40,
    height: 40,
    // marginBottom: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 10,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 15,
  },
  closeButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: BaseColors.black,
    textAlign: 'center',
  },
});

export default WalkthroughModal;
