
import Header from '@components/Header';
import { BaseColors } from '@config/theme';
import React from 'react';
import { View } from 'react-native';
import WebView from 'react-native-webview';


const KycCompleteScreen = ({ navigation, route }: any) => {
  const uri = route?.params?.uri;
  return (
    <View style={{ flex: 1, backgroundColor: BaseColors.white }}>
      <Header
        leftIcon="back-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <WebView
        source={{ uri: uri }}
        incognito
        startInLoadingState
        onLoad={() => { }}
        showsHorizontalScrollIndicator={false}
        automaticallyAdjustContentInsets={true}
        injectedJavaScript="window.ReactNativeWebView.postMessage(document.body.scrollHeight)"
        postMessage={(w) => console.log('YOur Dat ais :', w)}
        onNavigationStateChange={(e) => {
          console.log('🚀 ~ KycCompleteScreen ~ e:', e);
          const success = e?.url?.includes('success');
          if (success) {
            console.log('🚀 ~ KycCompleteScreen ~ data:', success);
            navigation.goBack();
          }
        }}
      />
    </View>
  );
};
export default KycCompleteScreen;
