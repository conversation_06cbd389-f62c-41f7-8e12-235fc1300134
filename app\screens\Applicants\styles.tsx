import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {StyleSheet} from 'react-native';
import {Dimensions} from 'react-native';
const HEIGHT = Dimensions.get('window').height;

export default StyleSheet.create({
  container: {
    backgroundColor: BaseColors.white,
    flex: 1,
    // marginBottom: 10,
    paddingBottom: 20,
  },
  nosearchView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: Dimensions.get('window').height / 1.6,
  },
  noFound: {
    fontSize: 26,
    fontFamily: FontFamily.OpenSansMedium,
    lineHeight: 51,
    marginVertical: 20,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '80%',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  tabText: {
    fontSize: 16,
    color: '#7a7a7a', // Default text color
  },
  activeText: {
    color: '#1d559f', // Active text color
    fontWeight: 'bold',
  },
  barContainer: {
    position: 'relative',
    width: '80%',
    marginTop: 4,
    height: 4,
    backgroundColor: 'transparent',
  },
  backgroundBar: {
    position: 'absolute',
    height: 4,
    width: '100%',
    backgroundColor: '#e3eaf5', // Light blue background bar color
    borderRadius: 2,
  },
  activeBar: {
    position: 'absolute',
    height: 4,
    width: '50%', // Half width for one tab
    backgroundColor: '#1d559f', // Blue active bar color
    borderRadius: 2,
    transitionDuration: '0.3s', // Smooth transition
  },
  mainView: {
    marginHorizontal: 10,
    // marginVertical: 10,
  },
  searchViewSty: {
    flexDirection: 'row',
    alignSelf: 'center',
    // alignItems: 'center',
    textAlign: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 10,
    marginTop: 35,
  },

  searchFieldSty: {
    flex: 0.98, // Take 80% of the parent width
    // height: 45, // Adjust height if needed
  },
  iconView: {
    padding: 5,
    borderWidth: 1,
    borderColor: BaseColors.bordrColor,
    // alignSelf: 'center',
    // alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    width: 40,
    height: 43,
    zIndex: 999,
    // marginLeft: 10,
    // flex: 0.2, // Take 20% of the parent width
  },
  centerMain: {
    flex: 1,
    alignSelf: 'center',
    justifyContent: 'center',
  },
});
