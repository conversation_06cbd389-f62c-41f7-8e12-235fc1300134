import React from 'react';

import ProfileComponant from '../../components/PaymentComponant';
import { StatusBar, View } from 'react-native';
import { useSelector } from 'react-redux';
import Header from '../../components/Header';
import styles from './styles';

export default function PaymentScreen({ route, navigation }: any) {
  const { darkmode } = useSelector((state: any) => state.auth);

  const selectedPlan = route?.params?.selectedPlan;

  return (
    <View style={styles?.container}>
      {/*  <StatusBar barStyle={'dark-content'} /> */}
      <Header
        leftIcon="back-arrow"
        title={
          selectedPlan === 0
            ? 'Basic'
            : selectedPlan === 1
              ? 'Harbor Verified'
              : selectedPlan === 2
                ? 'Harbor Pro'
                : ''
        }
        rSkip="true"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <ProfileComponant selectedPlan={selectedPlan} />
    </View>
  );
}
