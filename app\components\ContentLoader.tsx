import { BaseColors } from '@config/theme';
import React from 'react';
import ContentLoader, { Circle, Path, Rect } from 'react-content-loader/native';
import { View } from 'react-native';

const ContentLoad  = (props) => (
  <ContentLoader
    speed={2}
    width={360}
    height={160}
    viewBox="0 0 360 160"
    backgroundColor="#f2f2f2"
    foregroundColor="#ecebeb"
    {...props}
  >
    {/* Profile Image */}
    <Circle cx="32" cy="32" r="24" />

    {/* Title */}
    <Rect x="70" y="18" rx="4" ry="4" width="180" height="12" />

    {/* Location */}
    <Rect x="70" y="36" rx="3" ry="3" width="120" height="10" />

    {/* Badge */}
    <Rect x="310" y="16" rx="10" ry="10" width="32" height="32" />

    {/* Business Name */}
    <Rect x="16" y="70" rx="4" ry="4" width="100" height="12" />

    {/* Rating */}
    <Rect x="130" y="70" rx="4" ry="4" width="60" height="12" />

    {/* Date */}
    <Rect x="16" y="90" rx="4" ry="4" width="140" height="10" />

    {/* Time */}
    <Rect x="16" y="110" rx="4" ry="4" width="120" height="10" />

    {/* Category */}
    <Rect x="16" y="130" rx="10" ry="10" width="80" height="20" />

    {/* Price */}
    <Rect x="290" y="130" rx="10" ry="10" width="50" height="20" />
  </ContentLoader>
);


const MyLoader = () => (
  <View
    style={{
      marginHorizontal: 10,
      marginVertical: 10,
    }}>
    {[1,2,3,4].map(() => (<View
      style={{
        marginBottom: 10,
        backgroundColor: BaseColors.white,
        borderWidth: 0.6,
        borderRadius: 7,
        borderColor: BaseColors.borderColor,
      }}>
      <ContentLoad />
    </View>)
    )}
  </View>
);

export const ChatLoader = () => (
  <View
    style={{
      marginHorizontal: 10,
      marginVertical: 10,
    }}>
    {[1,2,3,4, 5, 6, 7, 8, 9, 10, 11, 12].map(() => (<View
      style={{
        marginBottom: 10,
        backgroundColor: BaseColors.white,
        borderBottomWidth: 0.6,
        borderRadius: 7,
        borderColor: BaseColors.borderColor,
      }}>
      <ContentLoader
        speed={2}
        width={412}
        height={55}
        viewBox="0 0 412 55"
        backgroundColor="#f2f2f2"
        foregroundColor="#ecebeb"
      >
        <Rect x="74" y="7" rx="3" ry="3" width="237" height="10" />
        <Circle cx="26" cy="26" r="26" />
        <Rect x="48" y="142" rx="0" ry="0" width="0" height="1" />
        <Rect x="76" y="30" rx="0" ry="0" width="232" height="9" />
      </ContentLoader>
    </View>))}
  </View>
);

export default MyLoader;
