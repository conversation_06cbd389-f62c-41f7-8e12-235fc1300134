/* eslint-disable react/no-unstable-nested-components */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {
  DefaultTheme,
  NavigationContainer,
  Theme,
} from '@react-navigation/native';
import BottomTabbar from '@navigation/BottomTabbar';
import {store} from '@redux/store/configureStore';
import AuthAction from '@redux/reducers/auth/actions';
import {Keyboard, Linking, Platform} from 'react-native';
import Home from '@screens/Home';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import notificationReducer from '../redux/reducers/notification/reducer';
import ProfileScreen from '@screens/ProfileScreen';
import SplashScreen from '@screens/SplashScreen';
import {NotificationContext} from '@components';
import {navigationRef} from './NavigationService';
import {EventRegister} from 'react-native-event-listeners';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Provider, useDispatch, useSelector} from 'react-redux';
import NetInfo from '@react-native-community/netinfo';
import {BaseColors, DarkBaseColor} from '../config/theme';
import IntroScreen from '@screens/IntroScreen';
import {StackCardInterpolationProps} from '@react-navigation/stack';
import OTPVerify from '@screens/OTPVerify';
import AdName from '@screens/AddName';
import ProfileSetUp from '@screens/ProfileSetup';
import PaymentScreen from '@screens/PaymentScreen';
import AuthScreen from '@screens/AuthScreen';
import NetworkLost from '@screens/NoInternet';
import Settings from '@screens/Settings';
import JobPosting from '@screens/JobPosting';
import WelcomeScreen from '@screens/WelcomeScreen';
import FilterScreen from '@screens/FilterScreen';
import MyHarbor from '@screens/MyHarbor';
import ReviewScreen from '@screens/ReviewScreen';
import JobApplicant from '@screens/JobApplicant';
import Applicants from '@screens/Applicants/Index';
import JobDetailScreen from '@screens/JobDetailScreen';
import ApplicantDetails from '@screens/ApplicantDetails';
import Notification from '@screens/Notification';
import {StripeProvider, useStripe} from '@stripe/stripe-react-native';
import ChatScreen from '@screens/Chat';
import ChatDetails from '@screens/ChatDetails';
import NotificationAction from '@redux/reducers/notification/actions';
import BaseSetting from '@config/setting';
import ReviewList from '@screens/ReviewList/indx';
import PrivacyPolicy from '@screens/PrivacyPolicy';
import AboutScreen from '@screens/AboutScreen';
import ContactUs from '@screens/ContactUs';
import FAQs from '@screens/FAQ';
import VerificationDetail from '@screens/VerificationDetail';
import HarborPolicy from '@screens/HarborPolicy';
import Policy from '@screens/Policy';
import RewardScreen from '@screens/RewardScreen';
import SearchScreen from '@screens/SearchScreen';
import BatchComponent from '@components/BatchComponant';
import FeaturedList from '@screens/FeaturedScreen';
import Wallet from '@screens/WalletScreen';
import KycCompleteScreen from '@components/KycCompalete';
import GalleryView from '@components/GalleryView';
import {handleAvailabilityUpdate, isIOS} from '@app/utils/CommonFunction';
import WebViewScreen from '@screens/WebView';
import HarborHistory from '@screens/HarborHistory';
import AllHistory from '@screens/AllHistory';
import EditAccount from '@screens/EditAccount';
import CounterHistory from '@screens/CounterHistory/Index';
import VideoTutorial from '@screens/VideoTutorial';
import RemotePushNotification from '@components/Common/PushNotification';
import AlertModal from '@components/AlertModal';
import {translate} from '@language/Translate';
import {useAppSelector} from '@components/UseRedux';
import WalkthroughModal from '@components/WalkthroughModal';

const Stack = createStackNavigator();
const HomeStack = createStackNavigator();
const ProfileStack = createStackNavigator();
const StoreStack = createStackNavigator();
// const ExploreStack = createStackNavigator();
const Tab = createBottomTabNavigator();
const intitialNotificationState: any = {
  notification: null,
  openedNotification: null,
  countOfNotification: 0,
};
const IOS = Platform.OS === 'ios';

let isOpen = false;

export default function NavigationStack() {
  const {handleURLCallback} = useStripe();

  const [modalOpen, setModalOpen] = useState<any>({
    loader: false,
    confirmationModal: false,
  });
  const darkmode = store.getState().auth.darkmode;
  const [darkApp, setdarkApp] = useState(darkmode);

  const notification = useSelector((s: any) => s.notification);
  const [Notifystate, dispatchState] = useReducer(
    notificationReducer,
    intitialNotificationState,
  );

  const dispatch = useDispatch();
  const {userData, isWalkthroughVisible} = useSelector((state: any) => state.auth);
  
  const [isNetWorkConnected, setIsNetWorkConnected] = useState(true);
  const [state, setState] = useState<any>({badgeModal: false});

  // memo
  const isNetWorkConnectedMemo = useMemo(
    () => isNetWorkConnected,
    [isNetWorkConnected],
  );

  const handleClose = () => {
    if (state?.badgeModal === 'pending' && state?.action && state?.meta) {
      setState((p: any) => ({
        ...p,
        badgeModal: true,
      }));
    }
    // Hide walkthrough using Redux
    dispatch(AuthAction.setWalkthroughVisible(false) as any);

    // Update user data to mark walkthrough as completed
    dispatch(
      AuthAction.setUserData({
        ...userData,
        isWalkThroughCompleted: true,
      }) as any,
    );
    handleAvailabilityUpdate('isWalkThroughCompleted', true);
  };

  const notiValue = useMemo(() => {
    return {Notifystate, dispatchState};
  }, [Notifystate, dispatchState]);

  const HomeStackNavigator = ({}: // navigation,
  // route,
  {
    navigation: any;
    route: any;
  }) => {
    return (
      <HomeStack.Navigator
        detachInactiveScreens={false}
        screenOptions={{headerShown: false}}>
        {/* <HomeStack.Screen
          name="Home"
          component={Home}
          options={{ gestureEnabled: true }}
        /> */}
        <HomeStack.Screen
          name="SearchScreen"
          component={SearchScreen}
          options={{gestureEnabled: true}}
        />
      </HomeStack.Navigator>
    );
  };
  const StoreStackNavigator = ({}: // navigation,
  // route,
  {
    navigation: any;
    route: any;
  }) => {
    return (
      <StoreStack.Navigator
        detachInactiveScreens={false}
        screenOptions={{headerShown: false}}>
        <StoreStack.Screen
          name="MyHarbor"
          component={MyHarbor}
          options={{gestureEnabled: true}}
        />
      </StoreStack.Navigator>
    );
  };
  const ProfileStackNavigator = ({}: // navigation,
  // route,
  {
    navigation: any;
    route: any;
  }) => {
    return (
      <ProfileStack.Navigator
        detachInactiveScreens={false}
        screenOptions={{headerShown: false}}>
        <ProfileStack.Screen
          name="Pofile"
          component={ProfileScreen}
          // options={{ gestureEnabled: true }}
        />
        {/* <ProfileStack.Screen
          name="Settings"
          component={Settings}
          options={{gestureEnabled: true}}
        /> */}
      </ProfileStack.Navigator>
    );
  };
  const BottomTabsNavigator = () => {
    const [isKeyboardVisible, setKeyboardVisible] = useState(false);

    useEffect(() => {
      const keyboardDidShowListener = Keyboard.addListener(
        'keyboardDidShow',
        () => setKeyboardVisible(true),
      );
      const keyboardDidHideListener = Keyboard.addListener(
        'keyboardDidHide',
        () => setKeyboardVisible(false),
      );

      return () => {
        keyboardDidShowListener.remove();
        keyboardDidHideListener.remove();
      };
    }, []);

    return (
      <Tab.Navigator
        initialRouteName={'HomeStackNavigator'}
        detachInactiveScreens={IOS ? true : false}
        tabBar={(props: any) =>
          isKeyboardVisible ? null : <BottomTabbar {...props} />
        }
        screenOptions={{
          headerShown: false,
          unmountOnBlur: true,
          tabBarHideOnKeyboard: true,
        }}>
        <Tab.Screen name="HomeStackNavigator" component={HomeStackNavigator} />
        <Tab.Screen
          name="ExploreStackNavigator"
          component={StoreStackNavigator}
        />
        <Tab.Screen
          name="StoreStackNavigator"
          component={JobPosting}
          listeners={({navigation}) => ({
            tabPress: event => {
              event.preventDefault();
              navigation.navigate('JobPosting'); // Navigate to different Stack Navigator, which is created by createStackNavigator
            },
          })}
        />
        <Tab.Screen name="MessageNavigator" component={ChatScreen} />

        <Tab.Screen
          name="profilesetUpNavigator"
          component={ProfileStackNavigator}
        />
      </Tab.Navigator>
    );
  };
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener(
      'changeAppThemeMode',
      (data: any) => {
        setdarkApp(data);
      },
    );
    return () => {
      EventRegister.removeEventListener(eventListener);
    };
  }, []);

  useEffect(() => {
    NetInfo.addEventListener((state: {isConnected: any}) => {
      setIsNetWorkConnected(state.isConnected);
    });
  }, []);

  interface AppTheme extends Theme {
    dark: boolean;
  }
  function getTheme(): AppTheme {
    let themeObj: AppTheme = {
      ...DefaultTheme,
      colors: darkApp
        ? {
            ...DarkBaseColor,
          }
        : {
            ...BaseColors,
          },
    };

    return themeObj;
  }
  const linking = {
    enabled: true,
    prefixes: ['harbor://', 'https://harbor.com', 'https://*.harbor.com'],
    config: {
      screens: {
        Dashboard: {},
      },
    },
  };
  const appTheme = getTheme();

  const forFade = ({current}: StackCardInterpolationProps) => ({
    cardStyle: {
      opacity: current.progress,
    },
  });

  const handleDeepLink = useCallback(
    async (url: string) => {
      if (url) {
        const stripeHandled = await handleURLCallback(url);
        if (stripeHandled) {
          // This was a Stripe URL - you can return or add extra handling here as you see fit
        } else {
          // This was NOT a Stripe URL – handle as you normally would
        }
      }
    },
    [handleURLCallback],
  );

  useEffect(() => {
    const getUrlAsync = async () => {
      const initialUrl = await Linking.getInitialURL();
      if (initialUrl) {
        handleDeepLink(initialUrl);
      }
    };

    getUrlAsync();

    const deepLinkListener = Linking.addEventListener('url', ({url}) => {
      handleDeepLink(url);
    });

    return () => deepLinkListener.remove();
  }, [handleDeepLink]);

  let notiData = state.data || {};

  useEffect(() => {
    EventRegister.addEventListener('reward_Update', (remoteMessage: any) => {
      console.log('addEventListener remoteMessage ===>', remoteMessage);
      setState((p: any) => ({...p, data: remoteMessage}));
    });
  }, []);

  useEffect(() => {
    if (notiData?.data?.action) {
      //?.action === 'reward') {
      try {
        const action =
          typeof notiData?.data?.action === 'string'
            ? JSON.parse(notiData?.data?.action || {})
            : notiData?.data?.action;
        if (
          action?.action === 'reward' ||
          action?.action === 'streakNotification'
        ) {
          setState((p: any) => ({
            ...p,
            badgeModal:
              isWalkthroughVisible || modalOpen?.confirmationModal
                ? 'pending'
                : true,
            action,
            meta:
              typeof notiData?.data?.meta === 'string'
                ? JSON.parse(notiData?.data?.meta || '{}')
                : notiData?.data?.meta,
          }));
        }
      } catch (e) {
        console.log('e ===>', e);
      }
    }
  }, [state.data]);

  useEffect(() => {
    if (!isIOS()) {
      const nData =
        notification?.notificationType?.notification ||
        notification?.notificationType ||
        {};
      if (nData?.data?.action) {
        //?.action === 'reward') {
        try {
          const action =
            typeof notiData?.data?.action === 'string'
              ? JSON.parse(notiData?.data?.action || {})
              : notiData?.data?.action;
          if (action?.action === 'reward') {
            setState((p: any) => ({
              ...p,
              badgeModal: true,
              action,
              data: notiData?.data,
              meta:
                typeof notiData?.data?.meta === 'string'
                  ? JSON.parse(notiData?.data?.meta || '{}')
                  : notiData?.data?.meta,
            }));
          }
        } catch (e) {
          console.log('e ===>', e);
        }
      }
    }
  }, [notification?.notificationType]);

  useEffect(() => {
    EventRegister.addEventListener('open_walkThrough', (remoteMessage: any) => {
      console.log('addEventListener remoteMessage ===>', isOpen, remoteMessage);
      if (userData?.isWalkThroughCompleted === false && !isOpen) {
        // dispatch(AuthAction.setWalkthroughVisible(true) as any);
      }
    });
    return () => {};
  }, [userData?.isWalkThroughCompleted]);

  return (
    <SafeAreaProvider>
      {/* <StatusBar barStyle={'dark-content'} /> */}
      <Provider store={store}>
        <StripeProvider
          publishableKey={BaseSetting.stripeKey}
          // urlScheme="your-url-scheme" // required for 3D Secure and bank redirects
          merchantIdentifier="merchant.com.harbor.newapp" // required for Apple Pay
        >
          <NavigationContainer
            ref={navigationRef}
            theme={appTheme}
            linking={linking}>
            <NetworkLost isVisible={!isNetWorkConnectedMemo} />
            <NotificationContext.Provider value={notiValue}>
              {/* <RemotePushController /> */}
              <Stack.Navigator
                initialRouteName={'SplashScreen'}
                detachInactiveScreens={IOS ? true : false}
                screenOptions={{
                  headerShown: false,
                }}>
                <Stack.Screen
                  name="SplashScreen"
                  component={SplashScreen}
                  options={{
                    cardStyleInterpolator: forFade,
                    headerShown: false,
                    gestureEnabled: true,
                    animationEnabled: true,
                    cardOverlayEnabled: true,
                    cardStyle: {backgroundColor: 'transparent'},
                    detachPreviousScreen: false,
                  }}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="BottomTabsNavigator"
                  component={BottomTabsNavigator}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="IntroScreen"
                  component={IntroScreen}
                />
                <Stack.Screen
                  options={{gestureEnabled: false}}
                  name="OTPVerify"
                  component={OTPVerify}
                />

                <Stack.Screen
                  options={{gestureEnabled: false}}
                  name="AddName"
                  component={AdName}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="ProfileSetUp"
                  component={ProfileSetUp}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="PaymentScreen"
                  component={PaymentScreen}
                />
                <Stack.Screen
                  options={{gestureEnabled: false}}
                  name="AuthScreen"
                  component={AuthScreen}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="Home"
                  component={Home}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="settings"
                  component={Settings}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="WelcomeScreen"
                  component={WelcomeScreen}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="JobPosting"
                  component={JobPosting}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="Applicants"
                  component={Applicants}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="FilterScreen"
                  component={FilterScreen}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="ReviewScreen"
                  component={ReviewScreen}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="ReviewList"
                  component={ReviewList}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="FeaturedList"
                  component={FeaturedList}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="GalleryView"
                  component={GalleryView}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="Wallet"
                  component={Wallet}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="KycCompleteScreen"
                  component={KycCompleteScreen}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="JobApplicant"
                  component={JobApplicant}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="JobDetailScreen"
                  component={JobDetailScreen}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="ApplicantDetails"
                  component={ApplicantDetails}
                />
                <Stack.Screen
                  options={{gestureEnabled: true}}
                  name="Notification"
                  component={Notification}
                />
                <Stack.Screen
                  name="chat"
                  options={{gestureEnabled: true}}
                  component={ChatScreen}
                />
                <Stack.Screen
                  name="Policy"
                  options={{gestureEnabled: true}}
                  component={Policy}
                />
                <Stack.Screen
                  name="PrivacyPolicy"
                  options={{gestureEnabled: true}}
                  component={PrivacyPolicy}
                />
                <Stack.Screen
                  name="WebViewScreen"
                  options={{gestureEnabled: true}}
                  component={WebViewScreen}
                />
                <Stack.Screen
                  name="HarborHistory"
                  options={{gestureEnabled: true}}
                  component={HarborHistory}
                />
                <Stack.Screen
                  name="AllHistory"
                  options={{gestureEnabled: true}}
                  component={AllHistory}
                />
                <Stack.Screen
                  name="ChatDetails"
                  options={{gestureEnabled: true}}
                  component={ChatDetails}
                />
                <Stack.Screen
                  name="AboutScreen"
                  options={{gestureEnabled: true}}
                  component={AboutScreen}
                />
                <Stack.Screen
                  name="FAQs"
                  options={{gestureEnabled: true}}
                  component={FAQs}
                />
                <Stack.Screen
                  name="ContactUs"
                  options={{gestureEnabled: true}}
                  component={ContactUs}
                />
                <Stack.Screen
                  name="HarborPolicy"
                  options={{gestureEnabled: true}}
                  component={HarborPolicy}
                />
                <Stack.Screen
                  name="CounterHistory"
                  options={{gestureEnabled: true}}
                  component={CounterHistory}
                />
                <Stack.Screen
                  name="EditAccount"
                  options={{gestureEnabled: true}}
                  component={EditAccount}
                />
                <Stack.Screen
                  name="SearchScreen"
                  options={{gestureEnabled: false}}
                  component={SearchScreen}
                />
                <Stack.Screen
                  name="VerificationDetail"
                  options={{gestureEnabled: true}}
                  component={VerificationDetail}
                />
                <Stack.Screen
                  name="VideoTutorial"
                  options={{gestureEnabled: true}}
                  component={VideoTutorial}
                />
                <Stack.Screen
                  name="RewardScreen"
                  options={{gestureEnabled: true}}
                  component={RewardScreen}
                />
              </Stack.Navigator>
              {state?.badgeModal === true && (
                <BatchComponent
                  visible={state?.badgeModal === true}
                  modalTitle={
                    state?.data?.title || state?.data?.notification?.title
                  }
                  type={
                    state?.action?.isUpgrade || state?.isUpgrade
                      ? 'badge'
                      : 'normal'
                  }
                  state={state}
                  // modalDescription={notiData?.body || notiData?.message}
                  highLightText={
                    state?.data?.message || state?.data?.notification?.body
                  }
                  setModalVisible={() => {
                    setState((p: any) => ({...p, badgeModal: false}));
                    dispatch(NotificationAction.setNotificationType({}) as any);
                  }}
                  modalHeder={''}
                />
              )}

              <AlertModal
                image
                title={translate('complete', '')}
                visible={modalOpen.confirmationModal}
                setVisible={(val: any) =>
                  setModalOpen((p: any) => ({...p, confirmationModal: false}))
                }
                lottieViewVisible
                btnYPress={() => {
                  navigationRef?.current?.navigate('ProfileSetUp', {});
                  handleClose();
                  setModalOpen((p: any) => ({...p, confirmationModal: false}));
                }}
                loader={modalOpen?.loader}
                btnYTitle={translate('letsdo')}
                confirmation
                // completeProfile
                titlesty={{textAlign: 'center'}}
                description={translate('postDescription', '')}
                btnNTitle={translate('maybe')}
                btnNPress={() => {
                  // handleClose();
                  isOpen = true;
                  setModalOpen((p: any) => ({...p, confirmationModal: false}));
                  setTimeout(() => {
                    dispatch(AuthAction.setWalkthroughVisible(true) as any);
                  }, 100);
                }}
              />

              <WalkthroughModal
                visible={isWalkthroughVisible}
                onClose={(type?: string) => {
                  if (type === 'postJob') {
                    if (userData?.isProfileSet === false) {
                      isOpen = true;
                      dispatch(AuthAction.setWalkthroughVisible(false) as any);
                      setTimeout(() => {
                        setModalOpen(p => ({...p, confirmationModal: true}));
                      }, 100);
                    } else {
                      navigationRef?.current?.navigate('JobPosting');
                      handleClose();
                    }
                  } else {
                    handleClose();
                  }
                }}
              />
              <RemotePushNotification />
            </NotificationContext.Provider>
          </NavigationContainer>
        </StripeProvider>
      </Provider>
    </SafeAreaProvider>
  );
}
