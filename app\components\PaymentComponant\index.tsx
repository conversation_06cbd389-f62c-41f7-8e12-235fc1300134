/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import {View, Text, ScrollView, Switch, TouchableOpacity} from 'react-native';
import styles from './styles';
import Button from '../UI/Button';
import {BaseColors} from '../../config/theme';
import SwitchComponent from '../SwitchComponant';
import {CustomIcon} from '@config/LoadIcons';
import {translate} from '@language/Translate';

interface PlanFeature {
  text: string;
}

interface Plan {
  id: 'basic' | 'verified' | 'pro';
  title: string;
  description: string;
  features: PlanFeature[];
  badge?: boolean;
  proTag?: boolean;
  payMonth: string;
  payYear: string;
  cancelamount: string;
}

const planData: Plan[] = [
  {
    id: 'basic',
    title: 'Get Started at No Cost !',
    description: 'Start your journey with basic features',
    payMonth: '',
    payYear: '',
    cancelamount: '',
    features: [
      {text: 'Profile creation'},
      {text: 'Limited access to jobs'},
      {text: 'Basic matching & search filters'},
      {text: 'Trial AI assistant'},
      {text: '6 job applications per month access'},
      {text: '2 job postings per month access'},
    ],
  },
  {
    id: 'verified',
    title: '$3.99/mo or $39.99/yr',
    description: 'Get verified status and unlock advanced features',
    payMonth: '$2.99/mo',
    payYear: '$19.99/yr',
    cancelamount: '$35.88/yr',
    features: [
      {text: 'Profile creation'},
      {text: 'Limited access to jobs'},
      {text: 'Basic matching & search filters'},
      {text: 'Trial AI assistant'},
      {text: '6 job applications per month access'},
      {text: '2 job postings per month access'},
      {text: 'Identity verification (ID check, credentials review, etc.)'},
    ],
    badge: true,
  },
  {
    id: 'pro',
    title: '$9.99/mo or $99.99/yr',
    description: 'Full access to all premium features',
    payMonth: '$9.99/mo',
    payYear: '$99.99/yr',
    cancelamount: '$119.88/yr',
    features: [
      {text: 'Profile creation'},
      {text: 'Limited access to jobs'},
      {text: 'Basic matching & search filters'},
      {text: 'Trial AI assistant'},
      {text: '6 job applications per month access'},
      {text: '2 job postings per month access'},
      {text: 'Identity verification (ID check, credentials review, etc.)'},
      {text: 'Verified Badge for profile'},
    ],
    badge: true,
    proTag: true,
  },
];

const ProfileComponent: React.FC<{selectedPlan: number}> = ({selectedPlan}) => {
  const [isEnabled, setIsEnabled] = useState(true);
  const [verifyEnable, setVerifyEnable] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState<'month' | 'year'>(
    'month',
  );
  const [planId, setPlanId] = useState<'basic' | 'verified' | 'pro'>(
    planData[selectedPlan].id,
  );

  const toggleSwitch = () => {
    setIsEnabled(previousState => !previousState);
    setPlanId('pro'); // When Harbor Pro is toggled, set planId to 'pro'
  };

  const toggleverifySwitch = () => {
    setVerifyEnable(previousState => !previousState);
    setPlanId('verified'); // When Harbor Verified is toggled, set planId to 'verified'
  };

  const selectedPlanData = planData.find(plan => plan.id === planId); // Get the selected plan data based on the planId state

  const renderFeaturesList = (features: PlanFeature[]) => {
    return features.map((feature, index) => (
      <View key={index} style={styles.featureItem}>
        <Text style={styles.bulletPoint}>•</Text>
        <Text style={styles.featureText}>{feature.text}</Text>
      </View>
    ));
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <Text style={styles.header}>{selectedPlanData?.title}</Text>
        {/* Features Card */}
        <View style={styles.card}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <View>
              <Text style={styles.featuresTitle}>Features:</Text>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              {(selectedPlanData?.id === 'verified' ||
                selectedPlanData?.id === 'pro') && (
                <CustomIcon
                  name="verified"
                  color={BaseColors.secondaryBule}
                  size={20}
                />
              )}
              {selectedPlanData?.id === 'pro' && (
                <View style={styles.proViewSty}>
                  <Text style={styles.proTxtColor}>Pro</Text>
                </View>
              )}
            </View>
          </View>
          {renderFeaturesList(selectedPlanData?.features)}
          {/* Payment Options - Display only for 'verified' and 'pro' plans */}
          {(selectedPlanData?.id === 'verified' ||
            selectedPlanData?.id === 'pro') && (
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                paddingTop: 10,
              }}>
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  width: '49%',

                  padding: 10,
                  borderRadius: 8,
                  backgroundColor:
                    selectedPayment === 'month'
                      ? BaseColors.secondaryBule
                      : 'transparent',
                  borderWidth: 1,
                  borderColor:
                    selectedPayment === 'month'
                      ? 'transparent'
                      : BaseColors.primary,
                }}
                onPress={() => setSelectedPayment('month')}>
                <View
                  style={[
                    styles.paymnthView,
                    {
                      backgroundColor:
                        selectedPayment === 'month'
                          ? BaseColors.primary
                          : 'white',
                      borderColor:
                        selectedPayment === 'month'
                          ? BaseColors.white
                          : BaseColors.primary,
                    },
                  ]}
                />
                <Text
                  style={[
                    styles.paymnthSty,
                    {
                      color:
                        selectedPayment === 'month'
                          ? BaseColors.white
                          : BaseColors.primary,
                    },
                  ]}>
                  {selectedPlanData?.payMonth}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.yearViewSty,
                  {
                    borderColor: BaseColors.primary,
                    backgroundColor:
                      selectedPayment === 'year'
                        ? BaseColors.secondaryBule
                        : 'white',
                  },
                ]}
                onPress={() => setSelectedPayment('year')}>
                <View
                  style={[
                    styles.amountViewSty,
                    {
                      borderColor:
                        selectedPayment === 'year'
                          ? BaseColors.white
                          : BaseColors.primary,

                      backgroundColor:
                        selectedPayment === 'year'
                          ? BaseColors.primary
                          : 'transparent',
                    },
                  ]}
                />
                <View style={styles.payView}>
                  <Text
                    style={[
                      styles.paySty,
                      {
                        color:
                          selectedPayment === 'year'
                            ? BaseColors.white
                            : BaseColors.primary,
                      },
                    ]}>
                    {selectedPlanData?.payYear}
                  </Text>
                  <Text
                    style={[
                      styles.cancelAmountSty,
                      {
                        color:
                          selectedPayment === 'year'
                            ? BaseColors.white
                            : BaseColors.black,
                      },
                    ]}>
                    {selectedPlanData?.cancelamount}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          )}
        </View>
        {/* Payment Breakdown Card */}
        <View style={styles.breakDownCard}>
          <Text style={styles.sectionTitle}>
            {translate('paymentBreakDown')}
          </Text>
          <View style={styles.dividerContainer}>
            <View style={styles.divider} />
            <Text style={styles.freeText}>It's free!</Text>
            <View style={styles.divider} />
          </View>
        </View>
        {/* Upgrade Section */}
        {selectedPlanData?.id === 'basic' && (
          <View style={styles.upgradeSection}>
            <Text style={styles.upgradeSectionTitle}>Upgrade Anytime!</Text>

            {/* Harbor Verified Option */}
            <View style={styles.planCard}>
              <View style={styles.planHeader}>
                <View style={styles.planTitleContainer}>
                  <Text style={styles.planTitle}>Harbor Verified</Text>
                  <View style={styles.verifiedBadge}>
                    <CustomIcon
                      name="verified"
                      color={BaseColors.secondaryBule}
                      size={20}
                    />
                  </View>
                </View>
                <SwitchComponent
                  onValueChange={toggleverifySwitch}
                  value={verifyEnable}
                  backgroundInactive="#fdfdfe"
                />
              </View>
              <Text style={styles.planDescription}>
                Lorem ipsum dolor sit amet consectetur. Ornare tempus
              </Text>
            </View>

            {/* Harbor Pro Option */}
            <View style={styles.planCard}>
              <View style={styles.planHeader}>
                <View style={styles.planTitleContainer}>
                  <Text style={styles.planTitle}>Harbor Pro</Text>
                  <View style={styles.verifiedBadge}>
                    <CustomIcon
                      name="verified"
                      color={BaseColors.secondaryBule}
                      size={20}
                    />
                  </View>
                  <View style={styles.proTag}>
                    <Text style={styles.proTagText}>Pro</Text>
                  </View>
                </View>
                <SwitchComponent
                  onValueChange={toggleSwitch}
                  value={isEnabled}
                  backgroundInactive="#fdfdfe"
                />
              </View>
              <Text style={styles.planDescription}>
                Lorem ipsum dolor sit amet consectetur. Ornare tempus
              </Text>
            </View>
          </View>
        )}
        {/* Confirm Button */}
      </View>
      <View style={styles.btnViewSty}>
        <Button type="text">Confirm</Button>
      </View>
    </ScrollView>
  );
};

export default ProfileComponent;
