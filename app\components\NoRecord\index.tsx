import React from 'react';
import {Dimensions, Text, View} from 'react-native';
import styles from './styles';
import {CustomIcon} from '@config/LoadIcons';
import {BaseColors} from '@config/theme';
import {isEmpty} from '@app/utils/lodashFactions';
import {translate} from '@language/Translate';
import {Images} from '@config/images';
import FastImage from 'react-native-fast-image';
import {getNoDataImage} from '@app/utils/CommonFunction';
import {FontFamily} from '@config/typography';
const LottieView = require('lottie-react-native').default;

function NoRecord({
  title = '',
  type = '',
  description = '',
  iconName = 'BsChat',
  selectScreen = '',
}) {
  if (type === 'chat') {
    return (
      <View style={styles.main}>
        <View style={styles.chatMain}>
          <View style={styles.chatIconMain}>
            {['chat'].includes(type) ? (
              <FastImage
                source={getNoDataImage(type)}
                style={{height: '100%', width: '100%'}}
                resizeMode="contain"
              />
            ) : (
              <CustomIcon
                name={!isEmpty(iconName) ? iconName : 'BsChat'}
                size={30}
                color={BaseColors.black}
              />
            )}
          </View>
        </View>
        <Text style={styles.emptyTextChat}>{translate(title)}</Text>
        <Text style={styles.descriptionText}>{translate(description)}</Text>
      </View>
    );
  } else {
    return (
      <View style={styles.main}>
        {selectScreen === 'isHistoryView' ? null : (
          <>
            <View
              style={{
                ...styles.mainView,
                width:
                  type === 'Employer'
                    ? null
                    : Dimensions.get('screen').width / 1.2,
                height:
                  type === 'review'
                    ? Dimensions.get('screen').height / 8
                    : Dimensions.get('screen').height / 6,
              }}>
              {['chat', 'Employer', 'Seeker', 'noJobsFound'].includes(type) ? (
                <FastImage
                  source={getNoDataImage(type)}
                  style={{height: '100%', width: '100%'}}
                  resizeMode="contain"
                />
              ) : (
                <LottieView
                  autoSize={true}
                  source={Images.noData}
                  autoPlay={true}
                  style={styles.loader}
                />
              )}
            </View>
          </>
        )}
        {selectScreen === 'isHistoryView' ? null : (
          <Text
            style={[styles.emptyText, {fontSize: type === 'review' ? 16 : 18}]}>
            {translate(title)}
          </Text>
        )}
        <Text
          style={[
            styles.descriptionText,
            {
              paddingBottom: selectScreen === 'isHistoryView' ? 15 : 0,
              fontSize: selectScreen === 'isHistoryView' ? 16 : 14,
              fontFamily:
                selectScreen === 'isHistoryView'
                  ? FontFamily.OpenSansMedium
                  : FontFamily.OpenSansRegular,
            },
          ]}>
          {translate(description)}
        </Text>
      </View>
    );
  }
}

export default NoRecord;
